# -*- coding: utf-8 -*-
"""
مدير قاعدة بيانات Oracle
Oracle Database Manager
"""

try:
    import oracledb
    ORACLE_LIB = 'oracledb'
except ImportError:
    try:
        import cx_Oracle as oracledb
        ORACLE_LIB = 'cx_Oracle'
    except ImportError:
        print("❌ لم يتم العثور على مكتبة Oracle. يرجى تثبيت oracledb أو cx_Oracle")
        oracledb = None
        ORACLE_LIB = None

import json
from pathlib import Path
from datetime import datetime
import logging


class OracleManager:
    """مدير قاعدة بيانات Oracle"""
    
    def __init__(self, config_file="config/oracle_config.json"):
        self.config_file = Path(config_file)
        self.connection = None
        self.cursor = None
        self.config = {}
        self.load_config()
        
        # إعداد السجلات
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """تحميل إعدادات قاعدة البيانات"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                # إعدادات افتراضية
                self.config = {
                    "username": "ship2025",
                    "password": "ys123",
                    "dsn": "yemensoft",
                    "encoding": "UTF-8",
                    "pool_min": 1,
                    "pool_max": 5,
                    "pool_increment": 1
                }
                self.save_config()
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإعدادات: {e}")
            # استخدام إعدادات افتراضية
            self.config = {
                "username": "ship2025",
                "password": "ys123",
                "dsn": "yemensoft",
                "encoding": "UTF-8"
            }
    
    def save_config(self):
        """حفظ إعدادات قاعدة البيانات"""
        try:
            self.config_file.parent.mkdir(exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {e}")
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        if not oracledb:
            raise Exception("مكتبة Oracle غير متاحة")
        
        try:
            self.logger.info(f"محاولة الاتصال بقاعدة البيانات Oracle...")
            self.logger.info(f"المكتبة المستخدمة: {ORACLE_LIB}")
            self.logger.info(f"المستخدم: {self.config['username']}")
            self.logger.info(f"الخادم: {self.config['dsn']}")
            
            # محاولة الاتصال بطرق مختلفة
            try:
                # الطريقة الأولى
                self.connection = oracledb.connect(
                    user=self.config['username'],
                    password=self.config['password'],
                    dsn=self.config['dsn']
                )
            except:
                # الطريقة الثانية
                connection_string = f"{self.config['username']}/{self.config['password']}@{self.config['dsn']}"
                self.connection = oracledb.connect(connection_string)
            
            self.cursor = self.connection.cursor()
            
            # اختبار الاتصال
            self.cursor.execute("SELECT USER FROM DUAL")
            current_user = self.cursor.fetchone()[0]
            
            self.logger.info(f"✅ تم الاتصال بنجاح!")
            self.logger.info(f"المستخدم الحالي: {current_user}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
            if self.connection:
                self.connection.close()
                self.connection = None
            self.logger.info("تم قطع الاتصال بقاعدة البيانات")
        except Exception as e:
            self.logger.error(f"خطأ في قطع الاتصال: {e}")
    
    def execute_query(self, query, params=None, fetch=True):
        """تنفيذ استعلام"""
        try:
            if not self.connection:
                if not self.connect():
                    raise Exception("فشل في الاتصال بقاعدة البيانات")
            
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            if fetch:
                if query.strip().upper().startswith('SELECT'):
                    return self.cursor.fetchall()
                else:
                    self.connection.commit()
                    return self.cursor.rowcount
            else:
                self.connection.commit()
                return self.cursor.rowcount
                
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            raise
    
    def execute_many(self, query, params_list):
        """تنفيذ استعلام متعدد"""
        try:
            if not self.connection:
                if not self.connect():
                    raise Exception("فشل في الاتصال بقاعدة البيانات")
            
            self.cursor.executemany(query, params_list)
            self.connection.commit()
            return self.cursor.rowcount
            
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام المتعدد: {e}")
            if self.connection:
                self.connection.rollback()
            raise
    
    def get_sequence_next_value(self, sequence_name):
        """الحصول على القيمة التالية من المتسلسلة"""
        try:
            query = f"SELECT {sequence_name}.NEXTVAL FROM DUAL"
            result = self.execute_query(query)
            return result[0][0] if result else None
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على قيمة المتسلسلة: {e}")
            return None
    
    def table_exists(self, table_name):
        """التحقق من وجود جدول"""
        try:
            query = """
                SELECT COUNT(*) FROM user_tables 
                WHERE table_name = UPPER(:table_name)
            """
            result = self.execute_query(query, {'table_name': table_name})
            return result[0][0] > 0 if result else False
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من وجود الجدول: {e}")
            return False
    
    def get_table_columns(self, table_name):
        """الحصول على أعمدة الجدول"""
        try:
            query = """
                SELECT column_name, data_type, data_length, nullable
                FROM user_tab_columns
                WHERE table_name = UPPER(:table_name)
                ORDER BY column_id
            """
            return self.execute_query(query, {'table_name': table_name})
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على أعمدة الجدول: {e}")
            return []
    
    def test_connection(self):
        """اختبار الاتصال"""
        try:
            if self.connect():
                # اختبار بسيط
                result = self.execute_query("SELECT SYSDATE FROM DUAL")
                current_time = result[0][0] if result else None
                
                self.logger.info(f"✅ اختبار الاتصال نجح")
                self.logger.info(f"الوقت الحالي في الخادم: {current_time}")
                
                return True
            else:
                return False
        except Exception as e:
            self.logger.error(f"❌ فشل اختبار الاتصال: {e}")
            return False
        finally:
            self.disconnect()
    
    def __enter__(self):
        """دعم context manager"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """إغلاق الاتصال عند الخروج"""
        self.disconnect()


# مثيل عام لمدير قاعدة البيانات
oracle_manager = OracleManager()


def get_oracle_connection():
    """الحصول على اتصال Oracle"""
    return oracle_manager


def test_oracle_connection():
    """اختبار اتصال Oracle"""
    manager = OracleManager()
    return manager.test_connection()


if __name__ == "__main__":
    # اختبار الاتصال
    print("🧪 اختبار اتصال Oracle...")
    if test_oracle_connection():
        print("✅ الاتصال يعمل بشكل صحيح!")
    else:
        print("❌ فشل في الاتصال!")
