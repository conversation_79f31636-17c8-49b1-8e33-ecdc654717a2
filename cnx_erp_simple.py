#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CnX ERP - نسخة مبسطة للاختبار
"""

import sys
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTreeWidget, QTreeWidgetItem, QFrame,
    QLineEdit, QComboBox, QDateEdit, QToolBar
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QAction

class SimpleERPWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        # إعداد النافذة
        self.setWindowTitle("CnX ERP - شركة القدس للتجارة والتوريدات المحدودة")
        self.setGeometry(100, 100, 1200, 800)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        
        # الشريط الجانبي الأيسر
        left_sidebar = self.create_left_sidebar()
        
        # المنطقة المركزية
        central_area = self.create_central_area()
        
        # الشريط الجانبي الأيمن
        right_sidebar = self.create_right_sidebar()
        
        # ترتيب المكونات
        main_layout.addWidget(right_sidebar)
        main_layout.addWidget(central_area, 1)
        main_layout.addWidget(left_sidebar)
        
        # شريط الأدوات
        self.create_toolbar()
        
        # تطبيق الأنماط
        self.apply_styles()
        
    def create_toolbar(self):
        toolbar = self.addToolBar("الأدوات")
        
        # إضافة أدوات بسيطة
        info_action = QAction("معلومات", self)
        toolbar.addAction(info_action)
        
        settings_action = QAction("إعدادات", self)
        toolbar.addAction(settings_action)
        
        print_action = QAction("طباعة", self)
        toolbar.addAction(print_action)
        
    def create_left_sidebar(self):
        sidebar = QFrame()
        sidebar.setFixedWidth(220)
        sidebar.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(sidebar)
        
        # عنوان
        title = QLabel("القوائم الرئيسية")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # شجرة القوائم
        tree = QTreeWidget()
        tree.setHeaderHidden(True)
        
        # إضافة عناصر القائمة
        menu_items = [
            "التقرير الإحصائي",
            "مركز التكلفة", 
            "أوامر الشراء",
            "بيانات الأصناف",
            "بيانات وحسابات",
            "سجل الأرصدة",
            "قائمة الجرد",
            "تقرير الأرصدة الحالية",
            "تقرير حركة المخزون",
            "تقارير الحركات المالية"
        ]
        
        for item_text in menu_items:
            item = QTreeWidgetItem([item_text])
            tree.addTopLevelItem(item)
            
        layout.addWidget(tree)
        return sidebar
        
    def create_central_area(self):
        area = QFrame()
        layout = QVBoxLayout(area)
        layout.setAlignment(Qt.AlignCenter)
        
        # شعار CnX ERP
        logo = QLabel("CnX ERP")
        logo.setFont(QFont("Arial", 48, QFont.Bold))
        logo.setAlignment(Qt.AlignCenter)
        logo.setStyleSheet("color: #2196F3; margin: 20px;")
        layout.addWidget(logo)
        
        # النص التوضيحي
        subtitle = QLabel("Enterprise Resource Planning Solutions")
        subtitle.setFont(QFont("Arial", 16))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("color: #666666; margin: 10px;")
        layout.addWidget(subtitle)
        
        # النص العربي
        arabic_text = QLabel("حلول تخطيط موارد المؤسسات")
        arabic_text.setFont(QFont("Arial", 14))
        arabic_text.setAlignment(Qt.AlignCenter)
        arabic_text.setStyleSheet("color: #888888; margin: 10px;")
        layout.addWidget(arabic_text)
        
        return area
        
    def create_right_sidebar(self):
        sidebar = QFrame()
        sidebar.setFixedWidth(200)
        sidebar.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(sidebar)
        
        # عناصر التحكم
        layout.addWidget(QLabel("البحث:"))
        layout.addWidget(QLineEdit())
        
        layout.addWidget(QLabel("التاريخ:"))
        layout.addWidget(QDateEdit(QDate.currentDate()))
        
        layout.addWidget(QLabel("النوع:"))
        combo = QComboBox()
        combo.addItems(["الكل", "مبيعات", "مشتريات", "مخزون"])
        layout.addWidget(combo)
        
        # أزرار
        layout.addStretch()
        for btn_text in ["بحث", "تصفية", "تصدير", "طباعة"]:
            btn = QPushButton(btn_text)
            layout.addWidget(btn)
            
        return sidebar
        
    def apply_styles(self):
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F5F5F5;
            }
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
            }
            QTreeWidget {
                background-color: white;
                border: 1px solid #E0E0E0;
                font-size: 11px;
            }
            QTreeWidget::item {
                padding: 8px;
            }
            QTreeWidget::item:hover {
                background-color: #E3F2FD;
            }
            QTreeWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QLineEdit, QComboBox, QDateEdit {
                padding: 6px;
                border: 1px solid #CCCCCC;
                border-radius: 4px;
                margin: 2px;
            }
            QLabel {
                color: #333333;
                margin: 2px;
            }
            QToolBar {
                background-color: #E3F2FD;
                border: none;
                padding: 5px;
            }
        """)

def main():
    app = QApplication(sys.argv)
    
    # تعيين اتجاه التخطيط للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = SimpleERPWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
