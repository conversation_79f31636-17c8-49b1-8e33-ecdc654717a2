
-- سكريب<PERSON> إنشاء المستخدم ship2025
-- يجب تشغيله بصلاحيات DBA

-- حذف المستخدم إذا كان موجوداً
BEGIN
    EXECUTE IMMEDIATE 'DROP USER ship2025 CASCADE';
    DBMS_OUTPUT.PUT_LINE('تم حذف المستخدم الموجود');
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('المستخدم غير موجود');
END;
/

-- إنشاء المستخدم الجديد
CREATE USER ship2025 IDENTIFIED BY ys123;

-- من<PERSON> الصلاحيات الأساسية
GRANT CONNECT TO ship2025;
GRANT RESOURCE TO ship2025;
GRANT CREATE SESSION TO ship2025;

-- صلاحيات الكائنات
GRANT CREATE TABLE TO ship2025;
GRANT CREATE VIEW TO ship2025;
GRANT CREATE SEQUENCE TO ship2025;
GRANT CREATE PROCEDURE TO ship2025;
GRANT CREATE FUNCTION TO ship2025;
GRANT CREATE PACKAGE TO ship2025;
GRANT CREATE TRIGGER TO ship2025;
GRANT CREATE SYNONYM TO ship2025;

-- صلاحيات متقدمة
GRANT CREATE ANY TABLE TO ship2025;
GRANT ALTER ANY TABLE TO ship2025;
GRANT DROP ANY TABLE TO ship2025;
GRANT SELECT ANY TABLE TO ship2025;
GRANT INSERT ANY TABLE TO ship2025;
GRANT UPDATE ANY TABLE TO ship2025;
GRANT DELETE ANY TABLE TO ship2025;

-- صلاحيات الفهارس
GRANT CREATE ANY INDEX TO ship2025;
GRANT ALTER ANY INDEX TO ship2025;
GRANT DROP ANY INDEX TO ship2025;

-- صلاحيات المتسلسلات
GRANT CREATE ANY SEQUENCE TO ship2025;
GRANT ALTER ANY SEQUENCE TO ship2025;
GRANT DROP ANY SEQUENCE TO ship2025;
GRANT SELECT ANY SEQUENCE TO ship2025;

-- إعدادات التخزين
ALTER USER ship2025 DEFAULT TABLESPACE USERS;
ALTER USER ship2025 TEMPORARY TABLESPACE TEMP;
ALTER USER ship2025 QUOTA UNLIMITED ON USERS;

-- صلاحيات إضافية
GRANT SELECT ANY DICTIONARY TO ship2025;
GRANT EXECUTE ANY PROCEDURE TO ship2025;

-- منح صلاحيات للوصول لجداول ias20241
GRANT SELECT ON ias20241.* TO ship2025;

COMMIT;

-- رسالة تأكيد
SELECT 'تم إنشاء المستخدم ship2025 بنجاح!' AS STATUS FROM DUAL;
