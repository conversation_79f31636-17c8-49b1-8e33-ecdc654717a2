#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 10: نسخ التسلسلات
Step 10: Copy sequences
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class SequenceCopier:
    """ناسخ التسلسلات"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'sequences_created': 0,
            'sequences_failed': 0,
            'sequences_skipped': 0,
            'failed_list': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def get_sequences_info(self):
        """الحصول على معلومات التسلسلات"""
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على التسلسلات
            cursor.execute("""
                SELECT sequence_name, min_value, max_value, increment_by,
                       cycle_flag, order_flag, cache_size, last_number
                FROM user_sequences
                WHERE sequence_name NOT LIKE 'BIN$%'
                ORDER BY sequence_name
            """)
            
            sequences = cursor.fetchall()
            
            print(f"📊 تم العثور على {len(sequences)} تسلسل للنسخ")
            
            sequences_info = []
            for seq_data in sequences:
                sequence_info = {
                    'name': seq_data[0],
                    'min_value': seq_data[1],
                    'max_value': seq_data[2],
                    'increment_by': seq_data[3],
                    'cycle_flag': seq_data[4],
                    'order_flag': seq_data[5],
                    'cache_size': seq_data[6],
                    'last_number': seq_data[7]
                }
                sequences_info.append(sequence_info)
            
            cursor.close()
            return sequences_info
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات التسلسلات: {e}")
            return []
    
    def create_sequence(self, sequence_info):
        """إنشاء تسلسل في الهدف"""
        try:
            sequence_name = sequence_info['name']
            
            target_cursor = self.target_conn.cursor()
            
            # التحقق من وجود التسلسل
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_sequences WHERE sequence_name = :sequence_name
            """, {'sequence_name': sequence_name})
            
            if target_cursor.fetchone()[0] > 0:
                print(f"   ⚠️ التسلسل {sequence_name} موجود بالفعل - سيتم تخطيه")
                target_cursor.close()
                return 'skipped'
            
            # بناء DDL للتسلسل
            ddl = f"CREATE SEQUENCE {sequence_name}"
            ddl += f" START WITH {sequence_info['last_number']}"
            ddl += f" INCREMENT BY {sequence_info['increment_by']}"
            
            if sequence_info['min_value'] != 1:
                ddl += f" MINVALUE {sequence_info['min_value']}"
            else:
                ddl += " NOMINVALUE"
            
            if sequence_info['max_value'] and sequence_info['max_value'] != 999999999999999999999999999:
                ddl += f" MAXVALUE {sequence_info['max_value']}"
            else:
                ddl += " NOMAXVALUE"
            
            if sequence_info['cycle_flag'] == 'Y':
                ddl += " CYCLE"
            else:
                ddl += " NOCYCLE"
            
            if sequence_info['order_flag'] == 'Y':
                ddl += " ORDER"
            else:
                ddl += " NOORDER"
            
            if sequence_info['cache_size'] > 0:
                ddl += f" CACHE {sequence_info['cache_size']}"
            else:
                ddl += " NOCACHE"
            
            # إنشاء التسلسل
            target_cursor.execute(ddl)
            target_cursor.close()
            
            self.stats['sequences_created'] += 1
            return 'created'
            
        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ خطأ في إنشاء التسلسل {sequence_info['name']}: {error_msg}")
            
            self.stats['sequences_failed'] += 1
            self.stats['failed_list'].append(sequence_name)
            return 'failed'
    
    def copy_sequences(self):
        """نسخ جميع التسلسلات"""
        print("🚀 بدء نسخ التسلسلات")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # الحصول على معلومات التسلسلات
        sequences_info = self.get_sequences_info()
        if not sequences_info:
            print("❌ لم يتم العثور على تسلسلات للنسخ")
            return False
        
        print(f"📊 سيتم نسخ {len(sequences_info)} تسلسل")
        
        # نسخ التسلسلات
        for i, sequence_info in enumerate(sequences_info, 1):
            sequence_name = sequence_info['name']
            
            print(f"[{i:4d}/{len(sequences_info)}] إنشاء تسلسل: {sequence_name}")
            
            result = self.create_sequence(sequence_info)
            
            if result == 'created':
                print(f"   ✅ تم إنشاء التسلسل {sequence_name}")
            elif result == 'skipped':
                print(f"   ⚠️ تم تخطي التسلسل {sequence_name}")
                self.stats['sequences_skipped'] += 1
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("🎉 تم إكمال نسخ التسلسلات!")
        print(f"✅ التسلسلات المنشأة: {self.stats['sequences_created']}")
        print(f"❌ التسلسلات الفاشلة: {self.stats['sequences_failed']}")
        print(f"⚠️ التسلسلات المتخطاة: {self.stats['sequences_skipped']}")
        print(f"⏱️ المدة: {duration}")
        
        if self.stats['failed_list']:
            print(f"\n❌ التسلسلات الفاشلة:")
            for seq in self.stats['failed_list']:
                print(f"   - {seq}")
        
        print("=" * 60)
        
        return self.stats['sequences_created'] > 0
    
    def verify_sequences(self):
        """التحقق من التسلسلات المنسوخة"""
        print("\n🔍 التحقق من التسلسلات المنسوخة...")
        
        try:
            # عد التسلسلات في الهدف
            target_cursor = self.target_conn.cursor()
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_sequences WHERE sequence_name NOT LIKE 'BIN$%'
            """)
            target_count = target_cursor.fetchone()[0]
            
            # عد التسلسلات في المصدر
            source_cursor = self.source_conn.cursor()
            source_cursor.execute("""
                SELECT COUNT(*) FROM user_sequences WHERE sequence_name NOT LIKE 'BIN$%'
            """)
            source_count = source_cursor.fetchone()[0]
            
            print("📊 مقارنة التسلسلات:")
            print(f"   المصدر: {source_count}")
            print(f"   الهدف: {target_count}")
            if source_count > 0:
                percentage = (target_count / source_count) * 100
                print(f"   النسبة: {percentage:.1f}%")
            
            target_cursor.close()
            source_cursor.close()
            
            return target_count > 0
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_copy_process(self):
        """تشغيل عملية نسخ التسلسلات"""
        try:
            if not self.connect():
                return False
            
            # نسخ التسلسلات
            success = self.copy_sequences()
            
            if success:
                # التحقق من النتائج
                self.verify_sequences()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في عملية النسخ: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = SequenceCopier()
    success = copier.run_copy_process()
    
    if success:
        print("\n✅ الخطوة 10 مكتملة - تم نسخ التسلسلات")
    else:
        print("\n❌ فشل في الخطوة 10")
    
    return success


if __name__ == "__main__":
    main()
