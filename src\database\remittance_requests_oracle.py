# -*- coding: utf-8 -*-
"""
إدارة طلبات الحوالات مع قاعدة بيانات Oracle
Remittance Requests Oracle Manager
"""

from datetime import datetime
import uuid
from .oracle_manager import OracleManager


class RemittanceRequestsOracle:
    """إدارة طلبات الحوالات مع Oracle"""
    
    def __init__(self):
        self.oracle_manager = OracleManager()
        self.table_name = "REMITTANCE_REQUESTS"
        self.sequence_name = "SEQ_REMITTANCE_REQUESTS"
    
    def create_table_if_not_exists(self):
        """إنشاء جدول طلبات الحوالات إذا لم يكن موجوداً"""
        try:
            if not self.oracle_manager.table_exists(self.table_name):
                create_table_sql = """
                CREATE TABLE REMITTANCE_REQUESTS (
                    ID NUMBER PRIMARY KEY,
                    REQUEST_NUMBER VARCHAR2(50) UNIQUE NOT NULL,
                    REQUEST_DATE DATE NOT NULL,
                    BRANCH VARCHAR2(100),
                    EXCHANGER VARCHAR2(100),
                    AMOUNT NUMBER(15,2) NOT NULL,
                    CURRENCY VARCHAR2(50) NOT NULL,
                    EXCHANGE_RATE NUMBER(10,4) DEFAULT 1.0,
                    PRIORITY VARCHAR2(20) DEFAULT 'عادي',
                    PURPOSE VARCHAR2(500) NOT NULL,
                    
                    -- معلومات المرسل
                    SENDER_NAME VARCHAR2(200) NOT NULL,
                    SENDER_ID VARCHAR2(50),
                    SENDER_NATIONALITY VARCHAR2(50),
                    SENDER_ID_TYPE VARCHAR2(50),
                    SENDER_PHONE VARCHAR2(20),
                    SENDER_LANDLINE VARCHAR2(20),
                    SENDER_EMAIL VARCHAR2(100),
                    SENDER_POBOX VARCHAR2(50),
                    SENDER_ADDRESS VARCHAR2(500),
                    
                    -- معلومات المستقبل
                    RECEIVER_NAME VARCHAR2(200) NOT NULL,
                    RECEIVER_ID VARCHAR2(50),
                    RECEIVER_PHONE VARCHAR2(20),
                    RECEIVER_ACCOUNT VARCHAR2(50),
                    RECEIVER_BANK VARCHAR2(200),
                    RECEIVER_BRANCH VARCHAR2(200),
                    RECEIVER_SWIFT VARCHAR2(20),
                    RECEIVER_COUNTRY VARCHAR2(100),
                    RECEIVER_CITY VARCHAR2(100),
                    RECEIVER_BANK_COUNTRY VARCHAR2(100),
                    RECEIVER_ADDRESS VARCHAR2(500),
                    
                    -- ملاحظات وخيارات
                    NOTES CLOB,
                    SMS_NOTIFICATION NUMBER(1) DEFAULT 1,
                    EMAIL_NOTIFICATION NUMBER(1) DEFAULT 0,
                    AUTO_CREATE_REMITTANCE NUMBER(1) DEFAULT 1,
                    PRINT_RECEIPT NUMBER(1) DEFAULT 0,
                    
                    -- معلومات النظام
                    STATUS VARCHAR2(50) DEFAULT 'معلق',
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UPDATED_AT TIMESTAMP
                )
                """
                
                self.oracle_manager.execute_query(create_table_sql, fetch=False)
                
                # إنشاء المتسلسلة
                create_sequence_sql = f"""
                CREATE SEQUENCE {self.sequence_name}
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE
                """
                
                try:
                    self.oracle_manager.execute_query(create_sequence_sql, fetch=False)
                except:
                    # المتسلسلة موجودة بالفعل
                    pass
                
                # إنشاء فهارس
                indexes = [
                    f"CREATE INDEX IDX_RR_REQUEST_DATE ON {self.table_name} (REQUEST_DATE)",
                    f"CREATE INDEX IDX_RR_STATUS ON {self.table_name} (STATUS)",
                    f"CREATE INDEX IDX_RR_SENDER_NAME ON {self.table_name} (SENDER_NAME)",
                    f"CREATE INDEX IDX_RR_RECEIVER_NAME ON {self.table_name} (RECEIVER_NAME)"
                ]
                
                for index_sql in indexes:
                    try:
                        self.oracle_manager.execute_query(index_sql, fetch=False)
                    except:
                        # الفهرس موجود بالفعل
                        pass
                
                print(f"✅ تم إنشاء جدول {self.table_name} والفهارس")
                return True
            else:
                print(f"✅ جدول {self.table_name} موجود بالفعل")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجدول: {e}")
            return False
    
    def generate_request_number(self):
        """إنشاء رقم طلب تلقائي"""
        return f"REQ{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def create_request(self, data):
        """إنشاء طلب حوالة جديد"""
        try:
            # التأكد من وجود الجدول
            if not self.create_table_if_not_exists():
                raise Exception("فشل في إنشاء الجدول")
            
            # الحصول على ID جديد من المتسلسلة
            request_id = self.oracle_manager.get_sequence_next_value(self.sequence_name)
            if not request_id:
                raise Exception("فشل في الحصول على معرف جديد")
            
            # إعداد البيانات
            insert_sql = f"""
            INSERT INTO {self.table_name} (
                ID, REQUEST_NUMBER, REQUEST_DATE, BRANCH, EXCHANGER, AMOUNT, CURRENCY,
                EXCHANGE_RATE, PRIORITY, PURPOSE, SENDER_NAME, SENDER_ID, SENDER_NATIONALITY,
                SENDER_ID_TYPE, SENDER_PHONE, SENDER_LANDLINE, SENDER_EMAIL, SENDER_POBOX,
                SENDER_ADDRESS, RECEIVER_NAME, RECEIVER_ID, RECEIVER_PHONE, RECEIVER_ACCOUNT,
                RECEIVER_BANK, RECEIVER_BRANCH, RECEIVER_SWIFT, RECEIVER_COUNTRY, RECEIVER_CITY,
                RECEIVER_BANK_COUNTRY, RECEIVER_ADDRESS, NOTES, SMS_NOTIFICATION,
                EMAIL_NOTIFICATION, AUTO_CREATE_REMITTANCE, PRINT_RECEIPT, STATUS, CREATED_AT
            ) VALUES (
                :id, :request_number, TO_DATE(:request_date, 'YYYY-MM-DD'), :branch, :exchanger,
                :amount, :currency, :exchange_rate, :priority, :purpose, :sender_name, :sender_id,
                :sender_nationality, :sender_id_type, :sender_phone, :sender_landline, :sender_email,
                :sender_pobox, :sender_address, :receiver_name, :receiver_id, :receiver_phone,
                :receiver_account, :receiver_bank, :receiver_branch, :receiver_swift, :receiver_country,
                :receiver_city, :receiver_bank_country, :receiver_address, :notes, :sms_notification,
                :email_notification, :auto_create_remittance, :print_receipt, :status, CURRENT_TIMESTAMP
            )
            """
            
            params = {
                'id': request_id,
                'request_number': data['request_number'],
                'request_date': data['request_date'],
                'branch': data['branch'],
                'exchanger': data['exchanger'],
                'amount': data['amount'],
                'currency': data['currency'],
                'exchange_rate': data['exchange_rate'],
                'priority': data['priority'],
                'purpose': data['purpose'],
                'sender_name': data['sender_name'],
                'sender_id': data['sender_id'],
                'sender_nationality': data['sender_nationality'],
                'sender_id_type': data['sender_id_type'],
                'sender_phone': data['sender_phone'],
                'sender_landline': data['sender_landline'],
                'sender_email': data['sender_email'],
                'sender_pobox': data['sender_pobox'],
                'sender_address': data['sender_address'],
                'receiver_name': data['receiver_name'],
                'receiver_id': data['receiver_id'],
                'receiver_phone': data['receiver_phone'],
                'receiver_account': data['receiver_account'],
                'receiver_bank': data['receiver_bank'],
                'receiver_branch': data['receiver_branch'],
                'receiver_swift': data['receiver_swift'],
                'receiver_country': data['receiver_country'],
                'receiver_city': data['receiver_city'],
                'receiver_bank_country': data['receiver_bank_country'],
                'receiver_address': data['receiver_address'],
                'notes': data['notes'],
                'sms_notification': data['sms_notification'],
                'email_notification': data['email_notification'],
                'auto_create_remittance': data['auto_create_remittance'],
                'print_receipt': data['print_receipt'],
                'status': data['status']
            }
            
            rows_affected = self.oracle_manager.execute_query(insert_sql, params, fetch=False)
            
            if rows_affected > 0:
                print(f"✅ تم إنشاء طلب الحوالة بنجاح - ID: {request_id}")
                return request_id
            else:
                raise Exception("لم يتم إدراج أي صف")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء طلب الحوالة: {e}")
            raise
    
    def get_all_requests(self):
        """الحصول على جميع طلبات الحوالات"""
        try:
            query = f"""
            SELECT ID, REQUEST_NUMBER, SENDER_NAME, RECEIVER_NAME, AMOUNT, CURRENCY,
                   TO_CHAR(REQUEST_DATE, 'YYYY-MM-DD') as REQUEST_DATE, STATUS
            FROM {self.table_name}
            ORDER BY CREATED_AT DESC
            """
            
            return self.oracle_manager.execute_query(query)
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على الطلبات: {e}")
            return []
    
    def get_request_by_id(self, request_id):
        """الحصول على طلب حوالة بالمعرف"""
        try:
            query = f"""
            SELECT * FROM {self.table_name}
            WHERE ID = :request_id
            """
            
            result = self.oracle_manager.execute_query(query, {'request_id': request_id})
            return result[0] if result else None
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على الطلب: {e}")
            return None
    
    def update_request(self, request_id, data):
        """تحديث طلب حوالة"""
        try:
            update_sql = f"""
            UPDATE {self.table_name} SET
                REQUEST_DATE = TO_DATE(:request_date, 'YYYY-MM-DD'),
                BRANCH = :branch,
                EXCHANGER = :exchanger,
                AMOUNT = :amount,
                CURRENCY = :currency,
                EXCHANGE_RATE = :exchange_rate,
                PRIORITY = :priority,
                PURPOSE = :purpose,
                SENDER_NAME = :sender_name,
                SENDER_ID = :sender_id,
                SENDER_NATIONALITY = :sender_nationality,
                SENDER_ID_TYPE = :sender_id_type,
                SENDER_PHONE = :sender_phone,
                SENDER_LANDLINE = :sender_landline,
                SENDER_EMAIL = :sender_email,
                SENDER_POBOX = :sender_pobox,
                SENDER_ADDRESS = :sender_address,
                RECEIVER_NAME = :receiver_name,
                RECEIVER_ID = :receiver_id,
                RECEIVER_PHONE = :receiver_phone,
                RECEIVER_ACCOUNT = :receiver_account,
                RECEIVER_BANK = :receiver_bank,
                RECEIVER_BRANCH = :receiver_branch,
                RECEIVER_SWIFT = :receiver_swift,
                RECEIVER_COUNTRY = :receiver_country,
                RECEIVER_CITY = :receiver_city,
                RECEIVER_BANK_COUNTRY = :receiver_bank_country,
                RECEIVER_ADDRESS = :receiver_address,
                NOTES = :notes,
                SMS_NOTIFICATION = :sms_notification,
                EMAIL_NOTIFICATION = :email_notification,
                AUTO_CREATE_REMITTANCE = :auto_create_remittance,
                PRINT_RECEIPT = :print_receipt,
                UPDATED_AT = CURRENT_TIMESTAMP
            WHERE ID = :request_id
            """
            
            params = data.copy()
            params['request_id'] = request_id
            
            rows_affected = self.oracle_manager.execute_query(update_sql, params, fetch=False)
            
            if rows_affected > 0:
                print(f"✅ تم تحديث طلب الحوالة بنجاح - ID: {request_id}")
                return True
            else:
                print(f"⚠️ لم يتم العثور على طلب الحوالة - ID: {request_id}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحديث طلب الحوالة: {e}")
            raise
    
    def delete_request(self, request_id):
        """حذف طلب حوالة"""
        try:
            delete_sql = f"DELETE FROM {self.table_name} WHERE ID = :request_id"
            
            rows_affected = self.oracle_manager.execute_query(
                delete_sql, 
                {'request_id': request_id}, 
                fetch=False
            )
            
            if rows_affected > 0:
                print(f"✅ تم حذف طلب الحوالة بنجاح - ID: {request_id}")
                return True
            else:
                print(f"⚠️ لم يتم العثور على طلب الحوالة - ID: {request_id}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في حذف طلب الحوالة: {e}")
            raise
