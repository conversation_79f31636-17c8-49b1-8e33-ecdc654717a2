#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قاعدة البيانات الجديدة
Test the new database
"""

import os
import cx_Oracle
from pathlib import Path


def test_database():
    """اختبار قاعدة البيانات الجديدة"""
    print("🧪 اختبار قاعدة البيانات الجديدة ship2025")
    print("=" * 50)
    
    try:
        # إعداد البيئة
        tns_admin = Path("network/admin")
        if tns_admin.exists():
            os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        # الاتصال بقاعدة البيانات
        print("🔌 الاتصال بقاعدة البيانات...")
        conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        cursor = conn.cursor()
        
        # اختبار الجداول
        print("\n📋 اختبار الجداول:")
        cursor.execute("SELECT COUNT(*) FROM user_tables WHERE table_name NOT LIKE 'BIN$%'")
        table_count = cursor.fetchone()[0]
        print(f"   عدد الجداول: {table_count:,}")
        
        # اختبار الفهارس
        print("\n📊 اختبار الفهارس:")
        cursor.execute("SELECT COUNT(*) FROM user_indexes WHERE table_name NOT LIKE 'BIN$%'")
        index_count = cursor.fetchone()[0]
        print(f"   عدد الفهارس: {index_count:,}")
        
        # اختبار القيود
        print("\n🔒 اختبار القيود:")
        cursor.execute("SELECT COUNT(*) FROM user_constraints WHERE table_name NOT LIKE 'BIN$%'")
        constraint_count = cursor.fetchone()[0]
        print(f"   عدد القيود: {constraint_count:,}")
        
        # اختبار التسلسلات
        print("\n🔢 اختبار التسلسلات:")
        cursor.execute("SELECT COUNT(*) FROM user_sequences WHERE sequence_name NOT LIKE 'BIN$%'")
        sequence_count = cursor.fetchone()[0]
        print(f"   عدد التسلسلات: {sequence_count:,}")
        
        # اختبار المشاهد
        print("\n👁️ اختبار المشاهد:")
        cursor.execute("SELECT COUNT(*) FROM user_views WHERE view_name NOT LIKE 'BIN$%'")
        view_count = cursor.fetchone()[0]
        print(f"   عدد المشاهد: {view_count:,}")
        
        # اختبار عينة من الجداول
        print("\n📊 اختبار عينة من الجداول:")
        cursor.execute("""
            SELECT table_name, num_rows 
            FROM user_tables 
            WHERE table_name NOT LIKE 'BIN$%' 
            AND ROWNUM <= 10
            ORDER BY table_name
        """)
        
        sample_tables = cursor.fetchall()
        for table_name, num_rows in sample_tables:
            print(f"   {table_name}: {num_rows or 0:,} صف")
        
        # اختبار التسلسلات
        print("\n🔢 اختبار عينة من التسلسلات:")
        cursor.execute("""
            SELECT sequence_name, last_number 
            FROM user_sequences 
            WHERE sequence_name NOT LIKE 'BIN$%' 
            AND ROWNUM <= 5
            ORDER BY sequence_name
        """)
        
        sample_sequences = cursor.fetchall()
        for seq_name, last_number in sample_sequences:
            print(f"   {seq_name}: {last_number}")
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 50)
        print("✅ جميع الاختبارات نجحت!")
        print("🎉 قاعدة البيانات ship2025 جاهزة للاستخدام!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    success = test_database()
    
    if success:
        print("\n🎯 النتيجة: قاعدة البيانات الجديدة تعمل بشكل صحيح!")
    else:
        print("\n💥 النتيجة: هناك مشكلة في قاعدة البيانات الجديدة!")
    
    return success


if __name__ == "__main__":
    main()
