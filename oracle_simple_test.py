#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار اتصال Oracle مبسط
Simple Oracle Connection Test
"""

import os
import sys
from pathlib import Path

# تعيين متغير البيئة
tns_admin = Path(__file__).parent / "network" / "admin"
os.environ['TNS_ADMIN'] = str(tns_admin.absolute())

print(f"🔧 TNS_ADMIN: {os.environ['TNS_ADMIN']}")

try:
    import cx_Oracle
    print(f"✅ cx_Oracle متاح - الإصدار: {cx_Oracle.version}")
    
    # اختبار اتصال مباشر
    print("\n🧪 اختبار اتصال مباشر...")
    
    # طرق اتصال مختلفة
    connection_methods = [
        {
            'name': 'طريقة 1: DSN',
            'method': lambda: cx_Oracle.connect("ias20241", "ys123", "yemensoft")
        },
        {
            'name': 'طريقة 2: Connection String',
            'method': lambda: cx_Oracle.connect("ias20241/ys123@yemensoft")
        },
        {
            'name': 'طريقة 3: localhost',
            'method': lambda: cx_Oracle.connect("ias20241/ys123@localhost:1521/XE")
        },
        {
            'name': 'طريقة 4: IP مباشر',
            'method': lambda: cx_Oracle.connect("ias20241/ys123@127.0.0.1:1521/XE")
        }
    ]
    
    for method in connection_methods:
        print(f"\n🔌 {method['name']}:")
        try:
            conn = method['method']()
            cursor = conn.cursor()
            
            # اختبار بسيط
            cursor.execute("SELECT USER, SYSDATE FROM DUAL")
            user, sysdate = cursor.fetchone()
            
            print(f"   ✅ نجح الاتصال!")
            print(f"   👤 المستخدم: {user}")
            print(f"   🕐 الوقت: {sysdate}")
            
            # اختبار الجداول
            cursor.execute("SELECT COUNT(*) FROM user_tables")
            table_count = cursor.fetchone()[0]
            print(f"   📊 عدد الجداول: {table_count}")
            
            cursor.close()
            conn.close()
            
            print(f"   🎉 الطريقة {method['name']} تعمل!")
            break
            
        except Exception as e:
            print(f"   ❌ فشل: {e}")
    
    else:
        print("\n💥 جميع طرق الاتصال فشلت!")
        print("\n🔧 تحقق من:")
        print("1. تشغيل خادم Oracle")
        print("2. صحة اسم المستخدم وكلمة المرور")
        print("3. إعدادات الشبكة")
        print("4. ملف tnsnames.ora")

except ImportError:
    print("❌ cx_Oracle غير مثبت")
    print("💡 قم بتثبيته: pip install cx_Oracle")
except Exception as e:
    print(f"❌ خطأ عام: {e}")

print("\n📋 معلومات البيئة:")
print(f"Python: {sys.version}")
print(f"Platform: {sys.platform}")
print(f"TNS_ADMIN: {os.environ.get('TNS_ADMIN', 'غير محدد')}")

# فحص ملف tnsnames.ora
tnsnames_file = Path(os.environ.get('TNS_ADMIN', '')) / "tnsnames.ora"
if tnsnames_file.exists():
    print(f"✅ ملف tnsnames.ora موجود: {tnsnames_file}")
    try:
        with open(tnsnames_file, 'r') as f:
            content = f.read()
            if 'YEMENSOFT' in content.upper():
                print("✅ إعداد YEMENSOFT موجود")
            else:
                print("⚠️ إعداد YEMENSOFT غير موجود")
    except Exception as e:
        print(f"❌ خطأ في قراءة tnsnames.ora: {e}")
else:
    print(f"❌ ملف tnsnames.ora غير موجود: {tnsnames_file}")
