#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تقرير نهائي شامل للفهارس
Final comprehensive indexes report
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def generate_final_indexes_report():
    """إنشاء تقرير نهائي شامل للفهارس"""
    setup_environment()
    
    print("📊 تقرير نهائي شامل للفهارس")
    print("=" * 60)
    
    try:
        # الاتصال بالمصدر
        print("🔌 الاتصال بقاعدة البيانات المصدر...")
        source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        source_cursor = source_conn.cursor()
        
        # الاتصال بالهدف
        print("🔌 الاتصال بقاعدة البيانات الهدف...")
        target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        target_cursor = target_conn.cursor()
        
        print("\n📊 تحليل الفهارس في المصدر...")
        
        # تحليل الفهارس في المصدر
        source_cursor.execute("""
            SELECT 
                CASE 
                    WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                    WHEN index_type = 'BITMAP' THEN 'BITMAP'
                    WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                    ELSE 'NORMAL'
                END as index_category,
                COUNT(*) as count
            FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            GROUP BY 
                CASE 
                    WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                    WHEN index_type = 'BITMAP' THEN 'BITMAP'
                    WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                    ELSE 'NORMAL'
                END
            ORDER BY 1
        """)
        source_by_type = dict(source_cursor.fetchall())
        
        # إجمالي الفهارس في المصدر
        source_cursor.execute("""
            SELECT COUNT(*) 
            FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
        """)
        source_total = source_cursor.fetchone()[0]
        
        print("\n📊 تحليل الفهارس في الهدف...")
        
        # تحليل الفهارس في الهدف
        target_cursor.execute("""
            SELECT 
                CASE 
                    WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                    WHEN index_type = 'BITMAP' THEN 'BITMAP'
                    WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                    ELSE 'NORMAL'
                END as index_category,
                COUNT(*) as count
            FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            GROUP BY 
                CASE 
                    WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                    WHEN index_type = 'BITMAP' THEN 'BITMAP'
                    WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                    ELSE 'NORMAL'
                END
            ORDER BY 1
        """)
        target_by_type = dict(target_cursor.fetchall())
        
        # إجمالي الفهارس في الهدف
        target_cursor.execute("""
            SELECT COUNT(*) 
            FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
        """)
        target_total = target_cursor.fetchone()[0]
        
        # عرض التقرير
        print("\n" + "=" * 60)
        print("📋 تقرير مقارنة الفهارس")
        print("=" * 60)
        
        type_names = {
            'NORMAL': 'فهارس عادية (Normal Indexes)',
            'UNIQUE': 'فهارس فريدة (Unique Indexes)', 
            'BITMAP': 'فهارس بت ماب (Bitmap Indexes)',
            'FUNCTION': 'فهارس وظيفية (Function-based Indexes)'
        }
        
        print(f"{'نوع الفهرس':<35} {'المصدر':<10} {'الهدف':<10} {'النسبة':<10}")
        print("-" * 65)
        
        total_source_detailed = 0
        total_target_detailed = 0
        
        for idx_type in ['NORMAL', 'UNIQUE', 'BITMAP', 'FUNCTION']:
            source_count = source_by_type.get(idx_type, 0)
            target_count = target_by_type.get(idx_type, 0)
            percentage = (target_count / source_count * 100) if source_count > 0 else 0
            
            total_source_detailed += source_count
            total_target_detailed += target_count
            
            print(f"{type_names[idx_type]:<35} {source_count:<10} {target_count:<10} {percentage:<10.1f}%")
        
        print("-" * 65)
        print(f"{'الإجمالي':<35} {source_total:<10} {target_total:<10} {(target_total/source_total*100):.1f}%")
        
        # فحص حالة الفهارس
        print(f"\n📊 حالة الفهارس في الهدف:")
        target_cursor.execute("""
            SELECT status, COUNT(*)
            FROM user_indexes
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            GROUP BY status
            ORDER BY status
        """)
        
        status_counts = target_cursor.fetchall()
        
        for status, count in status_counts:
            status_icon = "✅" if status == "VALID" else "⚠️"
            percentage = (count / target_total * 100) if target_total > 0 else 0
            print(f"   {status_icon} {status}: {count} فهرس ({percentage:.1f}%)")
        
        # فحص الفهارس المفقودة
        print(f"\n🔍 فحص الفهارس المفقودة...")
        
        # الحصول على أسماء الفهارس في المصدر
        source_cursor.execute("""
            SELECT index_name
            FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            AND index_name NOT IN (
                SELECT c.index_name FROM user_constraints c 
                WHERE c.index_name IS NOT NULL
                AND c.constraint_type IN ('P', 'U')
            )
        """)
        source_indexes = {row[0] for row in source_cursor.fetchall()}
        
        # الحصول على أسماء الفهارس في الهدف
        target_cursor.execute("""
            SELECT index_name
            FROM user_indexes
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
        """)
        target_indexes = {row[0] for row in target_cursor.fetchall()}
        
        missing_indexes = source_indexes - target_indexes
        
        if missing_indexes:
            print(f"   ⚠️ فهارس مفقودة: {len(missing_indexes)}")
            if len(missing_indexes) <= 10:
                for idx in sorted(missing_indexes):
                    print(f"      - {idx}")
            else:
                for idx in sorted(list(missing_indexes)[:10]):
                    print(f"      - {idx}")
                print(f"      ... و {len(missing_indexes) - 10} فهرس آخر")
        else:
            print("   ✅ لا توجد فهارس مفقودة!")
        
        # عينة من الفهارس المنسوخة
        print(f"\n📋 عينة من الفهارس المنسوخة:")
        target_cursor.execute("""
            SELECT index_name, table_name, index_type, uniqueness
            FROM user_indexes
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            AND ROWNUM <= 15
            ORDER BY table_name, index_name
        """)
        
        sample_indexes = target_cursor.fetchall()
        
        for i, (idx_name, table_name, idx_type, uniqueness) in enumerate(sample_indexes, 1):
            if uniqueness == 'UNIQUE':
                icon = "🔒"
            elif idx_type == 'BITMAP':
                icon = "🗂️"
            elif idx_type == 'FUNCTION-BASED NORMAL':
                icon = "⚙️"
            else:
                icon = "📊"
            
            print(f"   {i:2d}. {icon} {idx_name} على {table_name}")
        
        # إغلاق الاتصالات
        source_cursor.close()
        source_conn.close()
        target_cursor.close()
        target_conn.close()
        
        print("\n" + "=" * 60)
        print("🎉 تم إكمال تقرير الفهارس!")
        
        # تقييم النتائج
        success_rate = (target_total / source_total * 100) if source_total > 0 else 0
        
        if success_rate >= 95:
            print("✅ نسخ الفهارس مكتمل بنجاح!")
        elif success_rate >= 85:
            print("⚠️ نسخ الفهارس مكتمل مع بعض النقص")
        else:
            print("❌ نسخ الفهارس يحتاج مراجعة")
        
        print("=" * 60)
        
        return success_rate >= 85
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    success = generate_final_indexes_report()
    
    if success:
        print("\n🎉 تقرير الفهارس مكتمل!")
    else:
        print("\n💥 فشل في إنشاء تقرير الفهارس!")
    
    return success


if __name__ == "__main__":
    main()
