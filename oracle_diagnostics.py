#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص اتصال Oracle
Oracle Connection Diagnostics Tool
"""

import sys
import os
from pathlib import Path

def check_oracle_client():
    """فحص Oracle Client"""
    print("🔍 فحص Oracle Client...")
    
    try:
        import cx_Oracle
        print(f"✅ مكتبة cx_Oracle متاحة - الإصدار: {cx_Oracle.version}")
        
        # فحص Oracle Client
        try:
            client_version = cx_Oracle.clientversion()
            print(f"✅ Oracle Client متاح - الإصدار: {client_version}")
        except Exception as e:
            print(f"❌ Oracle Client غير متاح: {e}")
            print("💡 تحتاج لتثبيت Oracle Instant Client")
            return False
            
        return True
        
    except ImportError:
        print("❌ مكتبة cx_Oracle غير مثبتة")
        print("💡 قم بتثبيتها: pip install cx_Oracle")
        return False

def check_tnsnames():
    """فحص ملف tnsnames.ora"""
    print("\n🔍 فحص ملف tnsnames.ora...")
    
    # البحث عن ملف tnsnames.ora في المواقع المعتادة
    possible_paths = [
        os.environ.get('TNS_ADMIN', ''),
        os.environ.get('ORACLE_HOME', '') + '/network/admin',
        'C:/oracle/product/19.0.0/client_1/network/admin',
        'C:/app/oracle/product/19.0.0/client_1/network/admin',
        'C:/instantclient_19_8/network/admin'
    ]
    
    tnsnames_found = False
    for path in possible_paths:
        if path:
            tnsnames_path = Path(path) / 'tnsnames.ora'
            if tnsnames_path.exists():
                print(f"✅ تم العثور على tnsnames.ora في: {tnsnames_path}")
                
                # قراءة محتوى الملف
                try:
                    with open(tnsnames_path, 'r') as f:
                        content = f.read()
                        if 'yemensoft' in content.lower():
                            print("✅ تم العثور على إعداد yemensoft")
                        else:
                            print("⚠️ لم يتم العثور على إعداد yemensoft")
                            print("💡 تحتاج لإضافة إعداد yemensoft في tnsnames.ora")
                except Exception as e:
                    print(f"❌ خطأ في قراءة الملف: {e}")
                
                tnsnames_found = True
                break
    
    if not tnsnames_found:
        print("❌ لم يتم العثور على ملف tnsnames.ora")
        print("💡 تحتاج لإنشاء ملف tnsnames.ora مع إعداد yemensoft")
        
        # اقتراح محتوى ملف tnsnames.ora
        suggested_content = """
YEMENSOFT =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = XE)
    )
  )
"""
        print("\n📝 محتوى مقترح لملف tnsnames.ora:")
        print(suggested_content)
    
    return tnsnames_found

def test_simple_connection():
    """اختبار اتصال بسيط"""
    print("\n🧪 اختبار اتصال بسيط...")
    
    try:
        import cx_Oracle
        
        # محاولة اتصال بسيط
        connection_strings = [
            "ias20241/ys123@yemensoft",
            "ias20241/ys123@localhost:1521/XE",
            "ias20241/ys123@localhost:1521/ORCL"
        ]
        
        for conn_str in connection_strings:
            print(f"🔌 محاولة الاتصال: {conn_str}")
            try:
                conn = cx_Oracle.connect(conn_str)
                cursor = conn.cursor()
                cursor.execute("SELECT USER FROM DUAL")
                user = cursor.fetchone()[0]
                print(f"✅ نجح الاتصال! المستخدم: {user}")
                cursor.close()
                conn.close()
                return True
            except Exception as e:
                print(f"❌ فشل الاتصال: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def check_environment():
    """فحص متغيرات البيئة"""
    print("\n🔍 فحص متغيرات البيئة...")
    
    env_vars = ['ORACLE_HOME', 'TNS_ADMIN', 'PATH', 'LD_LIBRARY_PATH']
    
    for var in env_vars:
        value = os.environ.get(var, '')
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"⚠️ {var}: غير محدد")
    
    # فحص PATH للبحث عن Oracle
    path = os.environ.get('PATH', '')
    oracle_in_path = any('oracle' in p.lower() for p in path.split(os.pathsep))
    
    if oracle_in_path:
        print("✅ تم العثور على Oracle في PATH")
    else:
        print("⚠️ لم يتم العثور على Oracle في PATH")

def create_sample_tnsnames():
    """إنشاء ملف tnsnames.ora نموذجي"""
    print("\n📝 إنشاء ملف tnsnames.ora نموذجي...")
    
    tnsnames_content = """# ملف tnsnames.ora للاتصال بـ Oracle
# Oracle tnsnames.ora file

YEMENSOFT =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = XE)
    )
  )

# إعداد بديل للاختبار
YEMENSOFT_ALT =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = 127.0.0.1)(PORT = 1521))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SID = ORCL)
    )
  )
"""
    
    # إنشاء مجلد network/admin
    network_dir = Path("network/admin")
    network_dir.mkdir(parents=True, exist_ok=True)
    
    tnsnames_file = network_dir / "tnsnames.ora"
    with open(tnsnames_file, 'w', encoding='utf-8') as f:
        f.write(tnsnames_content)
    
    print(f"✅ تم إنشاء ملف tnsnames.ora في: {tnsnames_file.absolute()}")
    print("💡 قم بتعيين متغير البيئة TNS_ADMIN إلى مجلد network/admin")
    print(f"   set TNS_ADMIN={network_dir.absolute()}")

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة تشخيص اتصال Oracle")
    print("=" * 50)
    
    # فحص المكونات
    oracle_client_ok = check_oracle_client()
    
    if oracle_client_ok:
        check_environment()
        tnsnames_ok = check_tnsnames()
        
        if not tnsnames_ok:
            create_sample_tnsnames()
        
        # اختبار الاتصال
        connection_ok = test_simple_connection()
        
        if connection_ok:
            print("\n🎉 التشخيص مكتمل - الاتصال يعمل!")
        else:
            print("\n💥 التشخيص مكتمل - يوجد مشاكل في الاتصال")
            print("\n🔧 خطوات الإصلاح المقترحة:")
            print("1. تأكد من تشغيل خادم Oracle")
            print("2. تأكد من صحة إعدادات tnsnames.ora")
            print("3. تأكد من صحة اسم المستخدم وكلمة المرور")
            print("4. تأكد من إعدادات الشبكة والمنافذ")
    else:
        print("\n💥 يجب تثبيت Oracle Client أولاً")
        print("\n📥 خطوات التثبيت:")
        print("1. تحميل Oracle Instant Client من موقع Oracle")
        print("2. استخراج الملفات في مجلد مناسب")
        print("3. إضافة المجلد إلى PATH")
        print("4. تثبيت cx_Oracle: pip install cx_Oracle")

if __name__ == "__main__":
    main()
