#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ البيانات للجداول التي تحتوي على بيانات فعلية
Copy data for tables with actual content
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class DataCopierWithContent:
    """ناسخ البيانات للجداول التي تحتوي على محتوى"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'tables_copied': 0,
            'rows_copied': 0,
            'tables_failed': 0,
            'tables_skipped': 0,
            'failed_list': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def get_tables_with_data(self):
        """الحصول على الجداول التي تحتوي على بيانات"""
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على الجداول التي تحتوي على بيانات
            cursor.execute("""
                SELECT table_name, NVL(num_rows, 0) as row_count
                FROM user_tables
                WHERE table_name NOT LIKE 'BIN$%'
                AND NVL(num_rows, 0) > 0
                ORDER BY NVL(num_rows, 0) ASC
            """)
            
            tables = cursor.fetchall()
            cursor.close()
            
            print(f"📊 تم العثور على {len(tables)} جدول يحتوي على بيانات")
            
            return tables
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على قائمة الجداول: {e}")
            return []
    
    def copy_table_data(self, table_name, estimated_rows):
        """نسخ بيانات جدول واحد"""
        try:
            source_cursor = self.source_conn.cursor()
            target_cursor = self.target_conn.cursor()
            
            # التحقق من وجود الجدول في الهدف
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
            """, {'table_name': table_name})
            
            if target_cursor.fetchone()[0] == 0:
                print(f"   ⚠️ الجدول {table_name} غير موجود في الهدف - تخطي")
                source_cursor.close()
                target_cursor.close()
                return 'skipped'
            
            # التحقق من وجود بيانات في الهدف
            target_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            target_count = target_cursor.fetchone()[0]
            
            if target_count > 0:
                print(f"   ⚠️ الجدول {table_name} يحتوي على بيانات ({target_count:,} صف) - تخطي")
                source_cursor.close()
                target_cursor.close()
                return 'skipped'
            
            # عد الصفوف في المصدر
            source_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            source_count = source_cursor.fetchone()[0]
            
            if source_count == 0:
                print(f"   ⚠️ الجدول {table_name} فارغ في المصدر - تخطي")
                source_cursor.close()
                target_cursor.close()
                return 'skipped'
            
            # تحديد حجم الدفعة حسب حجم الجدول
            if source_count <= 1000:
                batch_size = source_count  # نسخ دفعة واحدة للجداول الصغيرة
            elif source_count <= 10000:
                batch_size = 1000
            elif source_count <= 100000:
                batch_size = 5000
            else:
                batch_size = 10000
            
            # الحصول على أعمدة الجدول
            source_cursor.execute("""
                SELECT column_name 
                FROM user_tab_columns 
                WHERE table_name = :table_name 
                ORDER BY column_id
            """, {'table_name': table_name})
            
            columns = [row[0] for row in source_cursor.fetchall()]
            columns_str = ", ".join(columns)
            placeholders = ", ".join([f":{i+1}" for i in range(len(columns))])
            
            # نسخ البيانات
            copied_rows = 0
            
            # استعلام المصدر
            source_cursor.execute(f"SELECT {columns_str} FROM {table_name}")
            
            # إعداد استعلام الإدراج
            insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
            
            while True:
                rows = source_cursor.fetchmany(batch_size)
                if not rows:
                    break
                
                # إدراج الدفعة
                target_cursor.executemany(insert_sql, rows)
                self.target_conn.commit()
                
                copied_rows += len(rows)
                
                if copied_rows % 5000 == 0:
                    print(f"      تم نسخ {copied_rows:,} صف...")
            
            source_cursor.close()
            target_cursor.close()
            
            self.stats['tables_copied'] += 1
            self.stats['rows_copied'] += copied_rows
            
            return 'copied'
            
        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ خطأ في نسخ الجدول {table_name}: {error_msg}")
            
            # تسجيل أنواع الأخطاء الشائعة
            if 'ORA-00001' in error_msg:
                print(f"      السبب: انتهاك قيد فريد")
            elif 'ORA-02291' in error_msg:
                print(f"      السبب: انتهاك قيد مفتاح خارجي")
            elif 'ORA-01400' in error_msg:
                print(f"      السبب: قيمة NULL في عمود مطلوب")
            
            self.stats['tables_failed'] += 1
            self.stats['failed_list'].append(table_name)
            return 'failed'
    
    def copy_data_with_content(self, max_tables=100):
        """نسخ البيانات للجداول التي تحتوي على محتوى"""
        print("🚀 بدء نسخ البيانات للجداول التي تحتوي على محتوى")
        print("=" * 70)
        
        start_time = datetime.now()
        
        # الحصول على قائمة الجداول
        tables = self.get_tables_with_data()
        if not tables:
            print("❌ لم يتم العثور على جداول تحتوي على بيانات")
            return False
        
        # تحديد عدد الجداول للنسخ
        tables_to_copy = tables[:max_tables]
        print(f"📊 سيتم نسخ أول {len(tables_to_copy)} جدول")
        
        # نسخ الجداول
        for i, (table_name, estimated_rows) in enumerate(tables_to_copy, 1):
            print(f"[{i:3d}/{len(tables_to_copy)}] نسخ الجدول: {table_name} ({estimated_rows:,} صف)")
            
            result = self.copy_table_data(table_name, estimated_rows)
            
            if result == 'copied':
                print(f"   ✅ تم نسخ الجدول {table_name} ({self.stats['rows_copied']:,} صف إجمالي)")
            elif result == 'skipped':
                self.stats['tables_skipped'] += 1
            # failed تم حسابه في الدالة
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # النتائج النهائية
        print("\n" + "=" * 70)
        print("🎉 تم إكمال نسخ البيانات!")
        print(f"✅ الجداول المنسوخة: {self.stats['tables_copied']}")
        print(f"📊 الصفوف المنسوخة: {self.stats['rows_copied']:,}")
        print(f"❌ الجداول الفاشلة: {self.stats['tables_failed']}")
        print(f"⚠️ الجداول المتخطاة: {self.stats['tables_skipped']}")
        print(f"⏱️ المدة: {duration}")
        
        if self.stats['failed_list']:
            print(f"\n❌ الجداول الفاشلة:")
            for table in self.stats['failed_list'][:10]:
                print(f"   - {table}")
            if len(self.stats['failed_list']) > 10:
                print(f"   ... و {len(self.stats['failed_list']) - 10} جدول آخر")
        
        print("=" * 70)
        
        return self.stats['tables_copied'] > 0
    
    def verify_data(self):
        """التحقق من البيانات المنسوخة"""
        print("\n🔍 التحقق من البيانات المنسوخة...")
        
        try:
            # حساب إجمالي الصفوف في الهدف
            target_cursor = self.target_conn.cursor()
            
            # عد الجداول التي تحتوي على بيانات
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_tables t
                WHERE EXISTS (
                    SELECT 1 FROM dual 
                    WHERE EXISTS (
                        SELECT 1 FROM user_tab_columns c 
                        WHERE c.table_name = t.table_name
                    )
                )
                AND t.table_name NOT LIKE 'BIN$%'
                AND NVL(t.num_rows, 0) > 0
            """)
            tables_with_data = target_cursor.fetchone()[0]
            
            print(f"📊 الجداول التي تحتوي على بيانات: {tables_with_data}")
            print(f"📊 إجمالي الصفوف المنسوخة: {self.stats['rows_copied']:,}")
            
            target_cursor.close()
            
            return self.stats['rows_copied'] > 0
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_copy_process(self):
        """تشغيل عملية نسخ البيانات"""
        try:
            if not self.connect():
                return False
            
            # نسخ البيانات
            success = self.copy_data_with_content()
            
            if success:
                # التحقق من النتائج
                self.verify_data()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في عملية النسخ: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = DataCopierWithContent()
    success = copier.run_copy_process()
    
    if success:
        print("\n✅ نسخ البيانات مكتمل!")
    else:
        print("\n❌ فشل في نسخ البيانات!")
    
    return success


if __name__ == "__main__":
    main()
