#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 9: نسخ المشغلات
Step 9: Copy triggers
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class TriggerCopier:
    """ناسخ المشغلات"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'before_triggers': 0,
            'after_triggers': 0,
            'instead_of_triggers': 0,
            'failed_triggers': 0,
            'skipped_triggers': 0,
            'failed_list': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def get_triggers_info(self):
        """الحصول على معلومات المشغلات"""
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على المشغلات
            cursor.execute("""
                SELECT trigger_name, trigger_type, triggering_event, table_name,
                       base_object_type, status, description, when_clause,
                       referencing_names, action_type
                FROM user_triggers
                WHERE trigger_name NOT LIKE 'BIN$%'
                AND status = 'ENABLED'
                ORDER BY table_name, trigger_name
            """)
            
            triggers = cursor.fetchall()
            
            print(f"📊 تم العثور على {len(triggers)} مشغل للنسخ")
            
            # تصنيف المشغلات
            trigger_types = {}
            for trigger in triggers:
                trigger_type = trigger[1]
                if trigger_type not in trigger_types:
                    trigger_types[trigger_type] = 0
                trigger_types[trigger_type] += 1
            
            print("📋 تصنيف المشغلات:")
            for trigger_type, count in trigger_types.items():
                print(f"   {trigger_type}: {count}")
            
            # الحصول على نص كل مشغل
            triggers_info = []
            for trigger_data in triggers:
                trigger_name = trigger_data[0]
                
                try:
                    # الحصول على نص المشغل
                    cursor.execute("""
                        SELECT text
                        FROM user_source
                        WHERE name = :trigger_name
                        AND type = 'TRIGGER'
                        ORDER BY line
                    """, {'trigger_name': trigger_name})
                    
                    source_lines = cursor.fetchall()
                    
                    if source_lines:
                        trigger_text = ''.join([line[0] for line in source_lines if line[0]])
                        
                        trigger_info = {
                            'name': trigger_name,
                            'type': trigger_data[1],
                            'triggering_event': trigger_data[2],
                            'table_name': trigger_data[3],
                            'base_object_type': trigger_data[4],
                            'status': trigger_data[5],
                            'description': trigger_data[6],
                            'when_clause': trigger_data[7],
                            'referencing_names': trigger_data[8],
                            'action_type': trigger_data[9],
                            'trigger_text': trigger_text
                        }
                        
                        triggers_info.append(trigger_info)
                    else:
                        print(f"   ⚠️ لا يمكن الحصول على نص المشغل: {trigger_name}")
                        
                except Exception as e:
                    print(f"   ⚠️ خطأ في الحصول على نص المشغل {trigger_name}: {e}")
                    continue
            
            cursor.close()
            return triggers_info
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات المشغلات: {e}")
            return []
    
    def create_trigger(self, trigger_info):
        """إنشاء مشغل في الهدف"""
        try:
            trigger_name = trigger_info['name']
            table_name = trigger_info['table_name']
            trigger_text = trigger_info['trigger_text']
            
            target_cursor = self.target_conn.cursor()
            
            # التحقق من وجود الجدول في الهدف
            if table_name:
                target_cursor.execute("""
                    SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
                """, {'table_name': table_name})
                
                if target_cursor.fetchone()[0] == 0:
                    print(f"   ⚠️ الجدول {table_name} غير موجود - تخطي المشغل {trigger_name}")
                    target_cursor.close()
                    return 'skipped'
            
            # التحقق من وجود المشغل
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_triggers WHERE trigger_name = :trigger_name
            """, {'trigger_name': trigger_name})
            
            if target_cursor.fetchone()[0] > 0:
                print(f"   ⚠️ المشغل {trigger_name} موجود بالفعل - سيتم تخطيه")
                target_cursor.close()
                return 'skipped'
            
            # تنظيف نص المشغل
            cleaned_text = trigger_text.strip()
            if not cleaned_text:
                print(f"   ❌ نص المشغل {trigger_name} فارغ")
                target_cursor.close()
                return 'failed'
            
            # إنشاء المشغل
            target_cursor.execute(cleaned_text)
            target_cursor.close()
            
            # تحديد نوع المشغل للإحصائيات
            trigger_type = trigger_info['type']
            if 'BEFORE' in trigger_type:
                self.stats['before_triggers'] += 1
            elif 'AFTER' in trigger_type:
                self.stats['after_triggers'] += 1
            elif 'INSTEAD OF' in trigger_type:
                self.stats['instead_of_triggers'] += 1
            
            return 'created'
            
        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ خطأ في إنشاء المشغل {trigger_info['name']}: {error_msg}")
            
            # تسجيل الأخطاء الشائعة
            if 'ORA-00942' in error_msg:
                print(f"      السبب: جدول غير موجود")
            elif 'ORA-00904' in error_msg:
                print(f"      السبب: عمود غير موجود")
            elif 'ORA-04080' in error_msg:
                print(f"      السبب: المشغل موجود بالفعل")
            
            self.stats['failed_triggers'] += 1
            self.stats['failed_list'].append(trigger_name)
            return 'failed'
    
    def copy_triggers(self, batch_size=10):
        """نسخ جميع المشغلات"""
        print("🚀 بدء نسخ المشغلات")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # الحصول على معلومات المشغلات
        triggers_info = self.get_triggers_info()
        if not triggers_info:
            print("❌ لم يتم العثور على مشغلات للنسخ")
            return False
        
        print(f"📊 سيتم نسخ {len(triggers_info)} مشغل")
        
        # نسخ المشغلات على دفعات
        total_created = 0
        total_skipped = 0
        
        for i in range(0, len(triggers_info), batch_size):
            batch = triggers_info[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(triggers_info) + batch_size - 1) // batch_size
            
            print(f"\n📦 الدفعة {batch_num}/{total_batches} ({len(batch)} مشغل)")
            print("-" * 40)
            
            for j, trigger_info in enumerate(batch, 1):
                trigger_name = trigger_info['name']
                table_name = trigger_info['table_name']
                trigger_type = trigger_info['type']
                
                print(f"[{i + j:4d}/{len(triggers_info)}] إنشاء مشغل {trigger_type}: {trigger_name} على {table_name}")
                
                result = self.create_trigger(trigger_info)
                
                if result == 'created':
                    print(f"   ✅ تم إنشاء المشغل {trigger_name}")
                    total_created += 1
                elif result == 'skipped':
                    print(f"   ⚠️ تم تخطي المشغل {trigger_name}")
                    total_skipped += 1
                    self.stats['skipped_triggers'] += 1
                # failed تم حسابه في الدالة
            
            # عرض تقدم الدفعة
            print(f"✅ تمت الدفعة {batch_num}: {total_created} نجح، {self.stats['failed_triggers']} فشل، {total_skipped} تخطي")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("🎉 تم إكمال نسخ المشغلات!")
        print(f"⏰ مشغلات BEFORE: {self.stats['before_triggers']}")
        print(f"⏰ مشغلات AFTER: {self.stats['after_triggers']}")
        print(f"🔄 مشغلات INSTEAD OF: {self.stats['instead_of_triggers']}")
        print(f"❌ المشغلات الفاشلة: {self.stats['failed_triggers']}")
        print(f"⚠️ المشغلات المتخطاة: {self.stats['skipped_triggers']}")
        print(f"⏱️ المدة: {duration}")
        
        if self.stats['failed_list']:
            print(f"\n❌ المشغلات الفاشلة:")
            for trigger in self.stats['failed_list'][:10]:  # أول 10
                print(f"   - {trigger}")
            if len(self.stats['failed_list']) > 10:
                print(f"   ... و {len(self.stats['failed_list']) - 10} مشغل آخر")
        
        print("=" * 60)
        
        return total_created > 0
    
    def verify_triggers(self):
        """التحقق من المشغلات المنسوخة"""
        print("\n🔍 التحقق من المشغلات المنسوخة...")
        
        try:
            # عد المشغلات في الهدف
            target_cursor = self.target_conn.cursor()
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_triggers WHERE trigger_name NOT LIKE 'BIN$%'
            """)
            target_count = target_cursor.fetchone()[0]
            
            # عد المشغلات في المصدر
            source_cursor = self.source_conn.cursor()
            source_cursor.execute("""
                SELECT COUNT(*) FROM user_triggers 
                WHERE trigger_name NOT LIKE 'BIN$%' AND status = 'ENABLED'
            """)
            source_count = source_cursor.fetchone()[0]
            
            print("📊 مقارنة المشغلات:")
            print(f"   المصدر: {source_count}")
            print(f"   الهدف: {target_count}")
            if source_count > 0:
                percentage = (target_count / source_count) * 100
                print(f"   النسبة: {percentage:.1f}%")
            
            target_cursor.close()
            source_cursor.close()
            
            return target_count > 0
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_copy_process(self):
        """تشغيل عملية نسخ المشغلات"""
        try:
            if not self.connect():
                return False
            
            # نسخ المشغلات
            success = self.copy_triggers()
            
            if success:
                # التحقق من النتائج
                self.verify_triggers()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في عملية النسخ: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = TriggerCopier()
    success = copier.run_copy_process()
    
    if success:
        print("\n✅ الخطوة 9 مكتملة - تم نسخ المشغلات")
    else:
        print("\n❌ فشل في الخطوة 9")
    
    return success


if __name__ == "__main__":
    main()
