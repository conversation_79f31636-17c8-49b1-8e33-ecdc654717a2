#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج تجريبي للواجهة الرئيسية مطابق للصورة
Main Window Prototype Based on Image Design
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QLabel, QPushButton, QFrame,
    QScrollArea, QSplitter, QTextEdit, QTreeWidget, QTreeWidgetItem,
    QSizePolicy, QSpacerItem, QMessageBox
)
from PySide6.QtCore import Qt, QSize, QTimer
from PySide6.QtGui import (
    <PERSON><PERSON><PERSON><PERSON>p, Q<PERSON><PERSON><PERSON>, QLinearGradient, QColor, QFont, QIcon,
    QPalette, QBrush, QPen
)

# استيراد الأنظمة من src/ui
try:
    from src.ui.settings.settings_window import SettingsWindow
    from src.ui.items.items_window import ItemsWindow
    from src.ui.suppliers.suppliers_window import SuppliersWindow
    from src.ui.shipments.shipments_window import ShipmentsWindow
    from src.ui.shipments.live_tracking_window import LiveTrackingWindow
    from src.ui.shipments.shipment_maps_window import ShipmentMapsWindow
    from src.ui.shipments.shipping_routes_window import ShippingRoutesWindow
    from src.ui.remittances.remittances_window import RemittancesWindow
    from src.ui.remittances.banks_management_window import BanksManagementWindow
    from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
    from src.ui.remittances.supplier_accounts_management_window import SupplierAccountsManagementWindow
    from src.ui.database.database_settings_window import DatabaseSettingsWindow
    from src.utils.arabic_support import setup_arabic_support
    SYSTEMS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: بعض الأنظمة غير متاحة: {e}")
    SYSTEMS_AVAILABLE = False

class GradientWidget(QWidget):
    """ويدجت مع خلفية متدرجة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(800, 600)
    
    def paintEvent(self, event):
        painter = QPainter(self)
        if not painter.isActive():
            return

        painter.setRenderHint(QPainter.Antialiasing)

        # إنشاء التدرج اللوني
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(240, 248, 255))  # أزرق فاتح
        gradient.setColorAt(0.3, QColor(255, 255, 255))  # أبيض
        gradient.setColorAt(0.7, QColor(255, 255, 255))  # أبيض
        gradient.setColorAt(1, QColor(255, 240, 245))  # وردي فاتح

        painter.fillRect(self.rect(), gradient)
        
        # رسم الخطوط المنحنية الزخرفية
        pen = QPen(QColor(100, 150, 255, 50), 2)
        painter.setPen(pen)
        
        # خطوط منحنية في الأسفل (أزرق)
        for i in range(20):
            x = i * 40
            y = self.height() - 100 + (i % 3) * 20
            painter.drawLine(x, y, x + 200, y - 50)
        
        # خطوط منحنية في الأعلى (أحمر)
        pen.setColor(QColor(255, 100, 100, 50))
        painter.setPen(pen)
        for i in range(15):
            x = self.width() - i * 50
            y = 100 + (i % 2) * 30
            painter.drawLine(x, y, x - 150, y + 40)

class LogoWidget(QLabel):
    """ويدجت عرض الشعار"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlignment(Qt.AlignCenter)
        self.setMinimumSize(400, 200)
        
        # إنشاء نص الشعار
        self.setText("CnX ERP")
        font = QFont("Arial", 64, QFont.Bold)
        self.setFont(font)

        # تلوين النص
        self.setStyleSheet("""
            QLabel {
                color: #2d3748;
                background: transparent;
                font-weight: bold;
            }
        """)

class SystemTreeWidget(QFrame):
    """شجرة أنظمة التطبيق"""

    def __init__(self, title, main_window=None, parent=None):
        super().__init__(parent)
        self.main_window = main_window  # مرجع للنافذة الرئيسية
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumWidth(350)  # زيادة العرض
        self.setMinimumWidth(320)  # زيادة العرض الأدنى

        layout = QVBoxLayout(self)

        # عنوان القائمة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #E8F4FD;
                padding: 8px;
                border: 1px solid #B0D4F1;
                border-radius: 4px;
                color: #2E86AB;
            }
        """)
        layout.addWidget(title_label)

        # إنشاء شجرة الأنظمة
        self.tree = QTreeWidget()
        self.tree.setHeaderHidden(True)
        self.tree.setRootIsDecorated(True)
        self.tree.setIndentation(20)

        # تطبيق أنماط CnX ERP على الشجرة
        self.tree.setStyleSheet("""
            QTreeWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                            stop:0 #2b6cb0, stop:1 #3182ce);
                border: none;
                border-radius: 8px;
                color: #f8fafc;
                font-size: 14px;
                font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
                font-weight: 500;
            }
            QTreeWidget::item {
                padding: 10px 15px;
                border: none;
                min-height: 38px;
                border-radius: 6px;
                margin: 2px 8px;
                color: #f1f5f9;
                background-color: rgba(255, 255, 255, 0.05);
            }
            QTreeWidget::item:hover {
                background-color: rgba(255, 255, 255, 0.15);
                border-right: 4px solid #60a5fa;
                color: #ffffff;
                font-weight: 600;
            }
            QTreeWidget::item:selected {
                background-color: rgba(255, 255, 255, 0.25);
                color: #ffffff;
                border-right: 4px solid #3b82f6;
                font-weight: bold;
            }
            QTreeWidget::item:has-children {
                font-weight: bold;
                font-size: 15px;
                background-color: rgba(255, 255, 255, 0.1);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                color: #ffffff;
                padding: 12px 15px;
                margin: 3px 5px;
            }
            QTreeWidget::item:has-children:hover {
                background-color: rgba(255, 255, 255, 0.2);
                color: #ffffff;
                border-right: 4px solid #fbbf24;
            }
            QTreeWidget::item:!has-children {
                color: #e2e8f0;
                font-size: 13px;
                font-weight: normal;
                padding-right: 25px;
            }
            QTreeWidget::item:!has-children:hover {
                color: #f8fafc;
                background-color: rgba(255, 255, 255, 0.12);
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: none;
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: none;
            }
            QTreeWidget::branch {
                background: transparent;
            }
        """)

        self.setup_tree_items()
        layout.addWidget(self.tree)

        # ربط الأحداث
        self.tree.itemClicked.connect(self.on_item_clicked)

    def setup_tree_items(self):
        """إعداد عناصر الشجرة مطابقة للأنظمة الموجودة"""

        # 🚢 إدارة الشحنات
        shipments_system = QTreeWidgetItem(self.tree, ["🚢 إدارة الشحنات"])
        shipments_system.setExpanded(True)
        shipments_system.setData(0, Qt.UserRole, "shipments_system")

        shipment_items = [
            ("📋 قائمة الشحنات", "shipments_list"),
            ("➕ إضافة شحنة جديدة", "new_shipment"),
            ("🔍 تتبع الشحنات", "track_shipments"),
            ("📍 التتبع المباشر", "live_tracking"),
            ("🗺️ خرائط الشحنات", "shipment_maps"),
            ("🛣️ طرق الشحن", "shipping_routes"),
            ("📦 إدارة الحاويات", "containers"),
            ("📈 تقارير الشحنات", "shipment_reports")
        ]

        for text, data in shipment_items:
            item = QTreeWidgetItem(shipments_system, [text])
            item.setData(0, Qt.UserRole, data)

        # 📦 إدارة الأصناف
        items_system = QTreeWidgetItem(self.tree, ["📦 إدارة الأصناف"])
        items_system.setExpanded(False)
        items_system.setData(0, Qt.UserRole, "items_system")

        items_list = [
            ("📋 قائمة الأصناف", "items_list"),
            ("➕ إضافة صنف جديد", "new_item"),
            ("📂 مجموعات الأصناف", "item_groups"),
            ("📏 وحدات القياس", "units"),
            ("🔍 البحث في الأصناف", "search_items"),
            ("📊 تقارير الأصناف", "items_reports")
        ]

        for text, data in items_list:
            item = QTreeWidgetItem(items_system, [text])
            item.setData(0, Qt.UserRole, data)

        # 🏭 إدارة الموردين
        suppliers_system = QTreeWidgetItem(self.tree, ["🏭 إدارة الموردين"])
        suppliers_system.setExpanded(False)
        suppliers_system.setData(0, Qt.UserRole, "suppliers_system")

        suppliers_list = [
            ("📋 قائمة الموردين", "suppliers_list"),
            ("➕ إضافة مورد جديد", "new_supplier"),
            ("💰 حسابات الموردين", "supplier_accounts"),
            ("💱 عملات الموردين", "supplier_currencies"),
            ("👥 ممثلي المشتريات", "purchase_representatives"),
            ("📄 أوامر الشراء", "purchase_orders"),
            ("📊 تقارير الموردين", "suppliers_reports")
        ]

        for text, data in suppliers_list:
            item = QTreeWidgetItem(suppliers_system, [text])
            item.setData(0, Qt.UserRole, data)

        # 💰 إدارة الحوالات
        remittances_system = QTreeWidgetItem(self.tree, ["💰 إدارة الحوالات"])
        remittances_system.setExpanded(False)
        remittances_system.setData(0, Qt.UserRole, "remittances_system")

        remittances_list = [
            ("📋 قائمة الحوالات", "remittances_list"),
            ("➕ إنشاء حوالة جديدة", "new_remittance"),
            ("🏦 إدارة البنوك", "banks_management"),
            ("🏢 إدارة الفروع", "branches_management"),
            ("💱 شركات الصرافة", "exchange_companies"),
            ("🔍 تتبع الحوالات", "track_remittances"),
            ("📊 التقارير المالية", "financial_reports"),
            ("🧾 مطابقة الحسابات", "account_reconciliation")
        ]

        for text, data in remittances_list:
            item = QTreeWidgetItem(remittances_system, [text])
            item.setData(0, Qt.UserRole, data)

        # 📊 التقارير والإحصائيات
        reports_system = QTreeWidgetItem(self.tree, ["📊 التقارير والإحصائيات"])
        reports_system.setExpanded(False)
        reports_system.setData(0, Qt.UserRole, "reports_system")

        reports_list = [
            ("📈 تقارير الشحنات", "shipments_reports"),
            ("📦 تقارير الأصناف", "items_reports_detailed"),
            ("🏭 تقارير الموردين", "suppliers_reports_detailed"),
            ("💰 التقارير المالية", "financial_reports_detailed"),
            ("📊 الإحصائيات العامة", "general_statistics"),
            ("🖨️ طباعة التقارير", "print_reports")
        ]

        for text, data in reports_list:
            item = QTreeWidgetItem(reports_system, [text])
            item.setData(0, Qt.UserRole, data)

        # ⚙️ الإعدادات
        settings_system = QTreeWidgetItem(self.tree, ["⚙️ الإعدادات"])
        settings_system.setExpanded(False)
        settings_system.setData(0, Qt.UserRole, "settings_system")

        settings_list = [
            ("🏢 إعدادات الشركة", "company_settings"),
            ("💱 إعدادات العملات", "currency_settings"),
            ("📅 السنة المالية", "fiscal_year"),
            ("👤 إدارة المستخدمين", "user_management"),
            ("🔐 الصلاحيات", "permissions"),
            ("🌐 الإعدادات العامة", "general_settings")
        ]

        for text, data in settings_list:
            item = QTreeWidgetItem(settings_system, [text])
            item.setData(0, Qt.UserRole, data)

        # 🗄️ قاعدة البيانات
        database_system = QTreeWidgetItem(self.tree, ["🗄️ قاعدة البيانات"])
        database_system.setExpanded(False)
        database_system.setData(0, Qt.UserRole, "database_system")

        database_list = [
            ("⚙️ إعدادات قاعدة البيانات", "database_settings"),
            ("💾 النسخ الاحتياطي", "backup"),
            ("🔄 استعادة البيانات", "restore"),
            ("🔧 أدوات الصيانة", "maintenance_tools")
        ]

        for text, data in database_list:
            item = QTreeWidgetItem(database_system, [text])
            item.setData(0, Qt.UserRole, data)

    def on_item_clicked(self, item, column):
        """معالج النقر على عنصر في الشجرة"""
        item_text = item.text(0)
        item_data = item.data(0, Qt.UserRole)

        print(f"تم النقر على: {item_text} (البيانات: {item_data})")

        # التحقق من أن العنصر ليس عقدة رئيسية (له أطفال)
        if item.childCount() > 0:
            # العقد الرئيسية - السماح بالتوسيع/الطي الطبيعي
            return

        # العناصر الفرعية - تنفيذ الإجراء
        if item_data and SYSTEMS_AVAILABLE and self.main_window:
            self.main_window.handle_menu_action(item_data, item_text)
        else:
            QMessageBox.information(self, "تحت التطوير", f"الوظيفة '{item_text}' قيد التطوير")

class SideMenuWidget(QFrame):
    """القائمة الجانبية البسيطة"""

    def __init__(self, title, items, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumWidth(200)
        self.setMinimumWidth(180)

        layout = QVBoxLayout(self)

        # عنوان القائمة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #E8F4FD;
                padding: 8px;
                border: 1px solid #B0D4F1;
                border-radius: 4px;
                color: #2E86AB;
            }
        """)
        layout.addWidget(title_label)

        # عناصر القائمة
        for item in items:
            btn = QPushButton(item)
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 8px 12px;
                    border: none;
                    background-color: transparent;
                    color: #333;
                }
                QPushButton:hover {
                    background-color: #F0F8FF;
                    color: #2E86AB;
                }
                QPushButton:pressed {
                    background-color: #E8F4FD;
                }
            """)
            layout.addWidget(btn)

        layout.addStretch()

class MainWindowPrototype(QMainWindow):
    """النافذة الرئيسية للنموذج التجريبي"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CnX ERP - شركة الأنظمة الذكية والمعلوماتية المتقدمة")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # متغيرات النوافذ المنفصلة
        self.settings_window = None
        self.items_window = None
        self.suppliers_window = None
        self.shipments_window = None
        self.live_tracking_window = None
        self.shipment_maps_window = None
        self.shipping_routes_window = None
        self.remittances_window = None
        self.banks_management_window = None
        self.supplier_accounts_management_window = None
        self.database_settings_window = None

        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء المحتوى المركزي
        self.create_central_widget()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        file_menu.addAction("جديد")
        file_menu.addAction("فتح")
        file_menu.addAction("حفظ")
        file_menu.addSeparator()
        file_menu.addAction("خروج")
        
        # قائمة تحرير
        edit_menu = menubar.addMenu("تحرير")
        edit_menu.addAction("تراجع")
        edit_menu.addAction("إعادة")
        edit_menu.addSeparator()
        edit_menu.addAction("نسخ")
        edit_menu.addAction("لصق")
        
        # قائمة عرض
        view_menu = menubar.addMenu("عرض")
        view_menu.addAction("شريط الأدوات")
        view_menu.addAction("شريط الحالة")
        view_menu.addAction("ملء الشاشة")
        
        # قائمة أدوات
        tools_menu = menubar.addMenu("أدوات")
        tools_menu.addAction("إعدادات")
        tools_menu.addAction("خيارات")
        
        # قائمة مساعدة
        help_menu = menubar.addMenu("مساعدة")
        help_menu.addAction("حول البرنامج")
        help_menu.addAction("دليل المستخدم")
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # أزرار شريط الأدوات
        actions = [
            ("جديد", "إنشاء مستند جديد"),
            ("فتح", "فتح مستند موجود"),
            ("حفظ", "حفظ المستند الحالي"),
            ("طباعة", "طباعة المستند"),
            ("بحث", "البحث في النظام"),
            ("تقارير", "عرض التقارير"),
            ("إعدادات", "إعدادات النظام"),
            ("مساعدة", "الحصول على المساعدة")
        ]
        
        for text, tooltip in actions:
            action = toolbar.addAction(text)
            action.setToolTip(tooltip)
            if text in ["حفظ", "تقارير"]:
                toolbar.addSeparator()
    
    def create_central_widget(self):
        """إنشاء المحتوى المركزي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # القائمة الجانبية اليسرى - شجرة الأنظمة
        left_menu = SystemTreeWidget("🏢 أنظمة التطبيق", main_window=self)
        splitter.addWidget(left_menu)
        
        # المنطقة المركزية
        center_widget = GradientWidget()
        center_layout = QVBoxLayout(center_widget)
        
        # إضافة مساحة فارغة في الأعلى
        center_layout.addStretch(1)
        
        # إضافة الشعار
        logo = LogoWidget()
        center_layout.addWidget(logo)

        # إضافة النص التوضيحي
        subtitle = QLabel("Enterprise Resource Planning Solutions")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setFont(QFont("Arial", 18))
        subtitle.setStyleSheet("""
            color: #4a5568;
            background: transparent;
            font-style: italic;
            margin-top: 10px;
        """)
        center_layout.addWidget(subtitle)
        
        # إضافة مساحة فارغة في الأسفل
        center_layout.addStretch(1)
        
        splitter.addWidget(center_widget)
        
        # القائمة الجانبية اليمنى
        right_menu_items = [
            "إدارة المستخدمين",
            "صلاحيات النظام",
            "سجل العمليات",
            "النسخ الاحتياطية",
            "إعدادات الشبكة",
            "تحديثات النظام",
            "الدعم الفني",
            "اتصل بنا",
            "حول النظام"
        ]
        right_menu = SideMenuWidget("إعدادات النظام", right_menu_items)
        splitter.addWidget(right_menu)
        
        # تحديد أحجام المقاسم - زيادة عرض الشجرة
        splitter.setSizes([400, 800, 200])
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        
        # معلومات المستخدم
        user_label = QLabel("المستخدم: مدير النظام")
        status_bar.addWidget(user_label)
        
        # فاصل
        status_bar.addPermanentWidget(QLabel(" | "))
        
        # تاريخ ووقت
        from datetime import datetime
        time_label = QLabel(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        status_bar.addPermanentWidget(time_label)
        
        # فاصل
        status_bar.addPermanentWidget(QLabel(" | "))
        
        # حالة الاتصال
        connection_label = QLabel("متصل بقاعدة البيانات")
        connection_label.setStyleSheet("color: green;")
        status_bar.addPermanentWidget(connection_label)
    
    def setup_style(self):
        """إعداد أنماط CnX ERP"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f7fa;
                color: #2d3748;
                font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                            stop:0 #4299e1, stop:1 #3182ce);
                border-bottom: 2px solid #2b6cb0;
                padding: 4px;
                color: white;
                font-weight: bold;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 16px;
                border-radius: 6px;
                color: white;
            }
            QMenuBar::item:selected {
                background-color: rgba(255, 255, 255, 0.2);
            }
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                            stop:0 #4299e1, stop:1 #3182ce);
                border: none;
                spacing: 3px;
                padding: 8px;
            }
            QToolBar QToolButton {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 80px;
            }
            QToolBar QToolButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            QStatusBar {
                background-color: #edf2f7;
                border-top: 1px solid #e2e8f0;
                color: #4a5568;
                padding: 4px;
            }
            QSplitter::handle {
                background-color: #e2e8f0;
                width: 2px;
            }
            QSplitter::handle:hover {
                background-color: #cbd5e0;
            }
        """)

    def handle_menu_action(self, action_data, item_text):
        """معالجة إجراءات القائمة بناءً على البيانات"""

        try:
            # أنظمة الشحنات
            if action_data == "shipments_list":
                self.open_shipments_window()
            elif action_data == "new_shipment":
                self.open_shipments_window()
            elif action_data == "track_shipments":
                self.open_shipments_window()
            elif action_data == "live_tracking":
                self.open_live_tracking_window()
            elif action_data == "shipment_maps":
                self.open_shipment_maps_window()
            elif action_data == "shipping_routes":
                self.open_shipping_routes_window()
            elif action_data in ["containers", "shipment_reports"]:
                QMessageBox.information(self, "تحت التطوير", f"نظام {item_text} قيد التطوير")

            # أنظمة الأصناف
            elif action_data == "items_list":
                self.open_items_window()
            elif action_data == "new_item":
                self.open_items_window()
            elif action_data in ["item_groups", "units", "search_items", "items_reports"]:
                QMessageBox.information(self, "تحت التطوير", f"نظام {item_text} قيد التطوير")

            # أنظمة الموردين
            elif action_data == "suppliers_list":
                self.open_suppliers_window()
            elif action_data == "new_supplier":
                self.open_suppliers_window()
            elif action_data == "supplier_accounts":
                self.open_supplier_accounts_management_window()
            elif action_data in ["supplier_currencies", "purchase_representatives", "purchase_orders", "suppliers_reports"]:
                QMessageBox.information(self, "تحت التطوير", f"نظام {item_text} قيد التطوير")

            # أنظمة الحوالات
            elif action_data == "remittances_list":
                self.open_remittances_window()
            elif action_data == "new_remittance":
                self.open_new_remittance_dialog()
            elif action_data == "banks_management":
                self.open_banks_management_window()
            elif action_data in ["branches_management", "exchange_companies", "track_remittances", "financial_reports", "account_reconciliation"]:
                QMessageBox.information(self, "تحت التطوير", f"نظام {item_text} قيد التطوير")

            # أنظمة التقارير
            elif action_data in ["shipments_reports", "items_reports_detailed", "suppliers_reports_detailed",
                               "financial_reports_detailed", "general_statistics", "print_reports"]:
                QMessageBox.information(self, "التقارير", f"نظام {item_text} قيد التطوير")

            # أنظمة الإعدادات
            elif action_data == "general_settings":
                self.open_settings_window()
            elif action_data in ["company_settings", "currency_settings", "fiscal_year", "user_management", "permissions"]:
                QMessageBox.information(self, "الإعدادات", f"نظام {item_text} قيد التطوير")

            # أنظمة قاعدة البيانات
            elif action_data == "database_settings":
                self.open_database_settings_window()
            elif action_data in ["backup", "restore", "maintenance_tools"]:
                QMessageBox.information(self, "قاعدة البيانات", f"نظام {item_text} قيد التطوير")

            else:
                QMessageBox.information(self, "تحت التطوير", f"الوظيفة '{item_text}' قيد التطوير")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح {item_text}:\n{str(e)}")

    # دوال فتح النوافذ
    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام الإعدادات غير متاح")
            return

        try:
            if self.settings_window is None or not self.settings_window.isVisible():
                self.settings_window = SettingsWindow(self)
            self.settings_window.show()
            self.settings_window.raise_()
            self.settings_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات: {str(e)}")

    def open_items_window(self):
        """فتح نافذة الأصناف"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام الأصناف غير متاح")
            return

        try:
            if self.items_window is None or not self.items_window.isVisible():
                self.items_window = ItemsWindow(self)
            self.items_window.show()
            self.items_window.raise_()
            self.items_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الأصناف: {str(e)}")

    def open_suppliers_window(self):
        """فتح نافذة الموردين"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام الموردين غير متاح")
            return

        try:
            if self.suppliers_window is None or not self.suppliers_window.isVisible():
                self.suppliers_window = SuppliersWindow(self)
            self.suppliers_window.show()
            self.suppliers_window.raise_()
            self.suppliers_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الموردين: {str(e)}")

    def open_shipments_window(self):
        """فتح نافذة الشحنات"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام الشحنات غير متاح")
            return

        try:
            if self.shipments_window is None or not self.shipments_window.isVisible():
                self.shipments_window = ShipmentsWindow(self)
            self.shipments_window.show()
            self.shipments_window.raise_()
            self.shipments_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الشحنات: {str(e)}")

    def open_live_tracking_window(self):
        """فتح نافذة التتبع المباشر"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام التتبع المباشر غير متاح")
            return

        try:
            if self.live_tracking_window is None or not self.live_tracking_window.isVisible():
                self.live_tracking_window = LiveTrackingWindow()
            self.live_tracking_window.show()
            self.live_tracking_window.raise_()
            self.live_tracking_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التتبع المباشر: {str(e)}")

    def open_shipment_maps_window(self):
        """فتح نافذة خرائط الشحنات"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام خرائط الشحنات غير متاح")
            return

        try:
            if self.shipment_maps_window is None or not self.shipment_maps_window.isVisible():
                self.shipment_maps_window = ShipmentMapsWindow()
            self.shipment_maps_window.show()
            self.shipment_maps_window.raise_()
            self.shipment_maps_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة خرائط الشحنات: {str(e)}")

    def open_shipping_routes_window(self):
        """فتح نافذة طرق الشحن"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام طرق الشحن غير متاح")
            return

        try:
            if self.shipping_routes_window is None or not self.shipping_routes_window.isVisible():
                self.shipping_routes_window = ShippingRoutesWindow()
            self.shipping_routes_window.show()
            self.shipping_routes_window.raise_()
            self.shipping_routes_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة طرق الشحن: {str(e)}")

    def open_remittances_window(self):
        """فتح نافذة الحوالات"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام الحوالات غير متاح")
            return

        try:
            if self.remittances_window is None or not self.remittances_window.isVisible():
                self.remittances_window = RemittancesWindow(self)
            self.remittances_window.show()
            self.remittances_window.raise_()
            self.remittances_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الحوالات: {str(e)}")

    def open_new_remittance_dialog(self):
        """فتح حوار إنشاء حوالة جديدة"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام الحوالات غير متاح")
            return

        try:
            dialog = NewRemittanceDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح حوار الحوالة الجديدة: {str(e)}")

    def open_banks_management_window(self):
        """فتح نافذة إدارة البنوك"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام إدارة البنوك غير متاح")
            return

        try:
            if self.banks_management_window is None or not self.banks_management_window.isVisible():
                self.banks_management_window = BanksManagementWindow(self)
            self.banks_management_window.show()
            self.banks_management_window.raise_()
            self.banks_management_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة البنوك: {str(e)}")

    def open_supplier_accounts_management_window(self):
        """فتح نافذة إدارة حسابات الموردين"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام حسابات الموردين غير متاح")
            return

        try:
            if self.supplier_accounts_management_window is None or not self.supplier_accounts_management_window.isVisible():
                self.supplier_accounts_management_window = SupplierAccountsManagementWindow(self)
            self.supplier_accounts_management_window.show()
            self.supplier_accounts_management_window.raise_()
            self.supplier_accounts_management_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة حسابات الموردين: {str(e)}")

    def open_database_settings_window(self):
        """فتح نافذة إعدادات قاعدة البيانات"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام إعدادات قاعدة البيانات غير متاح")
            return

        try:
            if self.database_settings_window is None or not self.database_settings_window.isVisible():
                self.database_settings_window = DatabaseSettingsWindow(self)
            self.database_settings_window.show()
            self.database_settings_window.raise_()
            self.database_settings_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إعدادات قاعدة البيانات: {str(e)}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)

    # إعداد دعم اللغة العربية
    if SYSTEMS_AVAILABLE:
        try:
            setup_arabic_support(app)
        except:
            app.setLayoutDirection(Qt.RightToLeft)
    else:
        app.setLayoutDirection(Qt.RightToLeft)

    # إنشاء النافذة الرئيسية
    window = MainWindowPrototype()
    window.showMaximized()  # فتح في وضع ملء الشاشة

    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
