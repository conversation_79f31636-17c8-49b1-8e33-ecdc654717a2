#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ سريع لبنية الجداول
Fast table structure copy
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class FastTableCopier:
    """ناسخ سريع للجداول"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def get_sample_tables(self, limit=10):
        """الحصول على عينة من الجداول للاختبار"""
        try:
            cursor = self.source_conn.cursor()
            
            cursor.execute(f"""
                SELECT table_name, num_rows 
                FROM user_tables 
                WHERE table_name NOT LIKE 'BIN$%'
                AND ROWNUM <= {limit}
                ORDER BY NVL(num_rows, 0) DESC
            """)
            
            tables = cursor.fetchall()
            cursor.close()
            
            print(f"📊 تم اختيار {len(tables)} جدول للاختبار:")
            for table_name, num_rows in tables:
                print(f"   📋 {table_name}: {num_rows or 0} صف")
            
            return [table[0] for table in tables]
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على الجداول: {e}")
            return []
    
    def create_table_as_select(self, table_name):
        """إنشاء جدول باستخدام CREATE TABLE AS SELECT"""
        try:
            target_cursor = self.target_conn.cursor()
            
            # التحقق من وجود الجدول
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
            """, {'table_name': table_name})
            
            if target_cursor.fetchone()[0] > 0:
                print(f"   ⚠️ الجدول {table_name} موجود - سيتم حذفه وإعادة إنشاؤه")
                target_cursor.execute(f"DROP TABLE {table_name}")
            
            # إنشاء الجدول بالبنية فقط (بدون بيانات)
            create_sql = f"""
                CREATE TABLE {table_name} AS 
                SELECT * FROM ias20241.{table_name} WHERE 1=0
            """
            
            target_cursor.execute(create_sql)
            self.target_conn.commit()
            target_cursor.close()
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الجدول {table_name}: {e}")
            return False
    
    def copy_tables_fast(self, table_limit=50):
        """نسخ سريع للجداول"""
        print("🚀 بدء النسخ السريع للجداول")
        print("=" * 50)
        
        start_time = datetime.now()
        
        # الحصول على قائمة الجداول
        tables = self.get_sample_tables(table_limit)
        if not tables:
            print("❌ لم يتم العثور على جداول")
            return False
        
        # نسخ الجداول
        success_count = 0
        failed_count = 0
        failed_tables = []
        
        for i, table_name in enumerate(tables, 1):
            print(f"[{i:2d}/{len(tables)}] نسخ جدول: {table_name}")
            
            if self.create_table_as_select(table_name):
                print(f"   ✅ تم إنشاء الجدول {table_name}")
                success_count += 1
            else:
                print(f"   ❌ فشل في إنشاء الجدول {table_name}")
                failed_count += 1
                failed_tables.append(table_name)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # النتائج
        print("\n" + "=" * 50)
        print("🎉 تم إكمال النسخ السريع!")
        print(f"✅ نجح: {success_count} جدول")
        print(f"❌ فشل: {failed_count} جدول")
        print(f"⏱️ المدة: {duration}")
        
        if failed_tables:
            print(f"\n❌ الجداول الفاشلة:")
            for table in failed_tables:
                print(f"   - {table}")
        
        print("=" * 50)
        
        return success_count > 0
    
    def verify_tables(self):
        """التحقق من الجداول المنسوخة"""
        print("\n🔍 التحقق من الجداول المنسوخة...")
        
        try:
            target_cursor = self.target_conn.cursor()
            
            # عد الجداول
            target_cursor.execute("SELECT COUNT(*) FROM user_tables")
            table_count = target_cursor.fetchone()[0]
            
            print(f"📊 عدد الجداول في ship2025: {table_count}")
            
            # عرض أول 10 جداول
            target_cursor.execute("""
                SELECT table_name, num_rows 
                FROM user_tables 
                ORDER BY table_name
            """)
            
            tables = target_cursor.fetchall()
            
            print("📋 الجداول المنسوخة:")
            for i, (table_name, num_rows) in enumerate(tables[:10]):
                print(f"   {i+1:2d}. {table_name}: {num_rows or 0} صف")
            
            if len(tables) > 10:
                print(f"   ... و {len(tables) - 10} جدول آخر")
            
            target_cursor.close()
            return True
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_fast_copy(self):
        """تشغيل النسخ السريع"""
        try:
            if not self.connect():
                return False
            
            # تحديد عدد الجداول للنسخ
            table_limit = input("\nكم جدول تريد نسخه؟ (افتراضي: 50): ").strip()
            if table_limit:
                try:
                    table_limit = int(table_limit)
                except:
                    table_limit = 50
            else:
                table_limit = 50
            
            # نسخ الجداول
            success = self.copy_tables_fast(table_limit)
            
            if success:
                # التحقق من النتائج
                self.verify_tables()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = FastTableCopier()
    success = copier.run_fast_copy()
    
    if success:
        print("\n✅ تم النسخ السريع بنجاح!")
    else:
        print("\n❌ فشل النسخ السريع!")
    
    return success


if __name__ == "__main__":
    main()
