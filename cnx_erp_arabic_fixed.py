#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CnX ERP - واجهة محسنة مع دعم كامل للعربية
Enhanced CnX ERP Interface with Full Arabic Support
"""

import sys
import math
import os
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTreeWidget, QTreeWidgetItem, QFrame,
    QLineEdit, QComboBox, QDateEdit, QToolBar
)
from PySide6.QtCore import Qt, QDate, QLocale
from PySide6.QtGui import (
    QFont, QPainter, QLinearGradient, QColor, 
    QAction, QPen, QPainterPath, QFontDatabase
)

# محاولة تحميل مكتبات دعم العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
    print("✅ تم تحميل مكتبات دعم العربية بنجاح")
except ImportError:
    ARABIC_SUPPORT = False
    print("⚠️ مكتبات دعم العربية غير متوفرة")
    print("لتثبيتها: pip install arabic-reshaper python-bidi")

class ArtisticBackgroundWidget(QWidget):
    """ويدجت الخلفية الفنية مع الخطوط المنحنية المتدرجة"""
    
    def __init__(self):
        super().__init__()
        self.setAutoFillBackground(True)
        
    def paintEvent(self, event):
        """رسم الخلفية الفنية"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية بيضاء مع تدرج خفيف
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(255, 255, 255))
        gradient.setColorAt(1, QColor(248, 250, 252))
        painter.fillRect(self.rect(), gradient)
        
        # رسم الخطوط المنحنية الفنية
        self.draw_artistic_curves(painter)
        
    def draw_artistic_curves(self, painter):
        """رسم الخطوط المنحنية الملونة بأسلوب فني"""
        width = self.width()
        height = self.height()
        
        if width <= 0 or height <= 0:
            return
            
        # الخطوط الحمراء المتدرجة (أعلى يمين)
        self.draw_flowing_curves(painter, 
                               start_color=QColor(229, 62, 62, 40),
                               start_x=width * 0.7, start_y=0,
                               direction="down_left", num_curves=8)
        
        # الخطوط الزرقاء المتدرجة (وسط)
        self.draw_flowing_curves(painter,
                               start_color=QColor(49, 130, 206, 35),
                               start_x=width * 0.1, start_y=height * 0.4,
                               direction="horizontal", num_curves=6)
        
        # الخطوط الخضراء المتدرجة (أسفل يسار)
        self.draw_flowing_curves(painter,
                               start_color=QColor(56, 161, 105, 30),
                               start_x=0, start_y=height * 0.8,
                               direction="up_right", num_curves=5)
                               
    def draw_flowing_curves(self, painter, start_color, start_x, start_y, direction, num_curves):
        """رسم مجموعة من المنحنيات المتدفقة"""
        
        for i in range(num_curves):
            # تدرج اللون
            alpha_ratio = 1 - (i / num_curves)
            current_color = QColor(start_color)
            current_color.setAlpha(int(start_color.alpha() * alpha_ratio))
            
            # سمك الخط متدرج
            pen_width = max(1, 3 - (i // 3))
            painter.setPen(QPen(current_color, pen_width))
            
            # إنشاء المسار المنحني
            path = QPainterPath()
            
            # حساب نقاط المنحنى حسب الاتجاه
            if direction == "down_left":
                x1 = start_x - (i * 12)
                y1 = start_y + (i * 6)
                x2 = x1 - 150 - (i * 8)
                y2 = y1 + 120 + (i * 10)
                
                # نقاط التحكم للمنحنى
                ctrl1_x = x1 - 60 + math.sin(i * 0.3) * 15
                ctrl1_y = y1 + 30 + math.cos(i * 0.2) * 10
                ctrl2_x = x2 + 40 + math.sin(i * 0.4) * 20
                ctrl2_y = y2 - 60 + math.cos(i * 0.3) * 15
                
            elif direction == "horizontal":
                x1 = start_x + (i * 10)
                y1 = start_y + math.sin(i * 0.5) * 20
                x2 = x1 + 250 + (i * 6)
                y2 = y1 + math.sin(i * 0.3) * 30
                
                ctrl1_x = x1 + 80 + math.cos(i * 0.4) * 20
                ctrl1_y = y1 - 40 + math.sin(i * 0.6) * 15
                ctrl2_x = x2 - 80 + math.cos(i * 0.3) * 25
                ctrl2_y = y2 + 20 + math.sin(i * 0.4) * 15
                
            else:  # up_right
                x1 = start_x + (i * 15)
                y1 = start_y - (i * 8)
                x2 = x1 + 140 + (i * 10)
                y2 = y1 - 100 - (i * 6)
                
                ctrl1_x = x1 + 50 + math.sin(i * 0.4) * 20
                ctrl1_y = y1 - 25 + math.cos(i * 0.5) * 15
                ctrl2_x = x2 - 40 + math.sin(i * 0.3) * 25
                ctrl2_y = y2 + 50 + math.cos(i * 0.4) * 10
            
            # رسم المنحنى
            path.moveTo(x1, y1)
            path.cubicTo(ctrl1_x, ctrl1_y, ctrl2_x, ctrl2_y, x2, y2)
            painter.drawPath(path)

class CnXERPMainWindow(QMainWindow):
    """النافذة الرئيسية لنظام CnX ERP مع دعم كامل للعربية"""
    
    def __init__(self):
        super().__init__()
        self.setup_arabic_support()
        self.init_ui()
        self.setup_styles()
        
    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية"""
        # تحديد اتجاه التخطيط للعربية
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تحديد الخط العربي
        self.arabic_font = QFont()
        
        # محاولة تحميل خطوط عربية مناسبة
        arabic_fonts = [
            "Tahoma",
            "Arial Unicode MS", 
            "Segoe UI",
            "Microsoft Sans Serif",
            "Arial"
        ]
        
        font_found = False
        for font_name in arabic_fonts:
            font = QFont(font_name)
            if QFontDatabase.hasFamily(font_name):
                self.arabic_font = font
                font_found = True
                print(f"✅ تم العثور على الخط: {font_name}")
                break
                
        if not font_found:
            print("⚠️ لم يتم العثور على خط عربي مناسب، سيتم استخدام الخط الافتراضي")
            
    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        if not ARABIC_SUPPORT:
            return text
            
        try:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # تطبيق خوارزمية الاتجاه الثنائي
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except Exception as e:
            print(f"خطأ في تنسيق النص العربي: {e}")
            return text
        
    def init_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        # إعداد النافذة الرئيسية لملء الشاشة
        self.setWindowTitle(self.format_arabic_text("CnX ERP - شركة القدس للتجارة والتوريدات المحدودة - الإدارة المالية 1.7/2024"))
        
        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # تعيين الحد الأدنى لحجم النافذة
        self.setMinimumSize(1024, 768)
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي مع اتجاه RTL
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        main_layout.setDirection(QHBoxLayout.RightToLeft)
        
        # إنشاء المكونات الرئيسية
        self.create_toolbar()
        
        # الشريط الجانبي الأيسر (القوائم)
        left_sidebar = self.create_left_sidebar()
        
        # المنطقة المركزية مع الخلفية الفنية
        central_area = self.create_central_area()
        
        # الشريط الجانبي الأيمن (التحكم)
        right_sidebar = self.create_right_sidebar()
        
        # ترتيب المكونات بشكل صحيح للعربية (من اليمين لليسار)
        main_layout.addWidget(left_sidebar)      # القوائم على اليسار
        main_layout.addWidget(central_area, 1)  # المنطقة المركزية
        main_layout.addWidget(right_sidebar)    # التحكم على اليمين
        
        # شريط الحالة
        status_message = self.format_arabic_text("مرحباً بك في نظام CnX ERP - جاهز للعمل")
        self.statusBar().showMessage(status_message)

    def create_toolbar(self):
        """إنشاء شريط الأدوات العلوي"""
        toolbar = self.addToolBar(self.format_arabic_text("الأدوات الرئيسية"))
        toolbar.setMovable(False)
        toolbar.setFloatable(False)
        toolbar.setLayoutDirection(Qt.RightToLeft)

        # إضافة الأدوات مع النصوص العربية
        tools = [
            ("ℹ️", "معلومات"),
            ("🔊", "الصوت"),
            ("🔔", "التنبيهات"),
            ("⚙️", "الإعدادات"),
            ("🖨️", "طباعة"),
            ("💾", "حفظ"),
            ("⭐", "المفضلة"),
            ("🔧", "الأدوات"),
            ("📧", "البريد الإلكتروني")
        ]

        for icon, tooltip in tools:
            formatted_text = self.format_arabic_text(f"{icon} {tooltip}")
            action = QAction(formatted_text, self)
            action.setToolTip(self.format_arabic_text(tooltip))
            action.setFont(self.arabic_font)
            toolbar.addAction(action)

    def create_left_sidebar(self):
        """إنشاء الشريط الجانبي الأيسر (القوائم الرئيسية)"""
        sidebar = QFrame()
        sidebar.setFixedWidth(280)
        sidebar.setFrameStyle(QFrame.StyledPanel)
        sidebar.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(10, 10, 10, 10)

        # عنوان القسم
        title_text = self.format_arabic_text("📋 القوائم الرئيسية")
        title = QLabel(title_text)
        title.setFont(QFont(self.arabic_font.family(), 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                padding: 12px;
                border-radius: 8px;
                color: #1976D2;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)

        # شجرة القوائم
        self.menu_tree = QTreeWidget()
        self.menu_tree.setHeaderHidden(True)
        self.menu_tree.setRootIsDecorated(True)
        self.menu_tree.setAlternatingRowColors(True)
        self.menu_tree.setLayoutDirection(Qt.RightToLeft)
        self.menu_tree.setFont(QFont(self.arabic_font.family(), 11))

        # إضافة القوائم مع أيقونات
        menu_items = [
            ("📊", "التقرير الإحصائي"),
            ("🏢", "مركز التكلفة"),
            ("📋", "أوامر الشراء"),
            ("📦", "بيانات الأصناف"),
            ("📈", "بيانات وحسابات"),
            ("💰", "سجل الأرصدة"),
            ("📋", "قائمة الجرد والعمل"),
            ("📊", "تقرير الأرصدة الحالية"),
            ("📈", "تقرير حركة المخزون"),
            ("📋", "تقارير الحركات المالية")
        ]

        for icon, text in menu_items:
            formatted_text = self.format_arabic_text(f"{icon} {text}")
            item = QTreeWidgetItem([formatted_text])
            item.setFont(0, QFont(self.arabic_font.family(), 11))
            self.menu_tree.addTopLevelItem(item)

        layout.addWidget(self.menu_tree)
        return sidebar

    def create_central_area(self):
        """إنشاء المنطقة المركزية مع الخلفية الفنية"""
        # استخدام الويدجت الفني كخلفية
        central_area = ArtisticBackgroundWidget()
        central_area.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(central_area)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(50, 50, 50, 50)

        # شعار CnX ERP مع تأثيرات
        logo_label = QLabel("CnX ERP")
        logo_font = QFont("Arial", 56, QFont.Bold)
        logo_label.setFont(logo_font)
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E53E3E, stop:0.3 #3182CE, stop:0.7 #38A169, stop:1 #E53E3E);
                background: transparent;
                margin: 30px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
        """)
        layout.addWidget(logo_label)

        # النص التوضيحي الإنجليزي
        subtitle = QLabel("Enterprise Resource Planning Solutions")
        subtitle.setFont(QFont("Arial", 18, QFont.Normal))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: #555555;
                background: transparent;
                margin: 20px;
                font-style: italic;
            }
        """)
        layout.addWidget(subtitle)

        # النص العربي
        arabic_text_content = self.format_arabic_text("حلول تخطيط موارد المؤسسات المتكاملة")
        arabic_text = QLabel(arabic_text_content)
        arabic_text.setFont(QFont(self.arabic_font.family(), 16, QFont.Normal))
        arabic_text.setAlignment(Qt.AlignCenter)
        arabic_text.setStyleSheet("""
            QLabel {
                color: #777777;
                background: transparent;
                margin: 15px;
            }
        """)
        layout.addWidget(arabic_text)

        # مساحة مرنة
        layout.addStretch()

        return central_area

    def create_right_sidebar(self):
        """إنشاء الشريط الجانبي الأيمن (لوحة التحكم)"""
        sidebar = QFrame()
        sidebar.setFixedWidth(250)
        sidebar.setFrameStyle(QFrame.StyledPanel)
        sidebar.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(10, 10, 10, 10)

        # عنوان قسم التحكم
        control_title_text = self.format_arabic_text("🔧 لوحة التحكم")
        control_title = QLabel(control_title_text)
        control_title.setFont(QFont(self.arabic_font.family(), 14, QFont.Bold))
        control_title.setAlignment(Qt.AlignCenter)
        control_title.setStyleSheet("""
            QLabel {
                background-color: #FFF3E0;
                padding: 12px;
                border-radius: 8px;
                color: #F57C00;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(control_title)

        # عناصر التحكم
        controls = [
            ("🔍", "البحث:", QLineEdit()),
            ("📅", "التاريخ:", QDateEdit(QDate.currentDate())),
            ("📂", "النوع:", QComboBox()),
            ("📊", "الحالة:", QComboBox()),
        ]

        for icon, label_text, widget in controls:
            formatted_label = self.format_arabic_text(f"{icon} {label_text}")
            label = QLabel(formatted_label)
            label.setFont(QFont(self.arabic_font.family(), 11, QFont.Bold))
            label.setStyleSheet("color: #444444; margin-top: 10px;")
            layout.addWidget(label)

            # إعداد الويدجت
            widget.setLayoutDirection(Qt.RightToLeft)
            widget.setFont(QFont(self.arabic_font.family(), 10))

            if isinstance(widget, QComboBox):
                if "النوع" in label_text:
                    items = ["الكل", "مبيعات", "مشتريات", "مخزون", "حسابات"]
                    formatted_items = [self.format_arabic_text(item) for item in items]
                    widget.addItems(formatted_items)
                elif "الحالة" in label_text:
                    items = ["الكل", "نشط", "معلق", "مكتمل", "ملغي"]
                    formatted_items = [self.format_arabic_text(item) for item in items]
                    widget.addItems(formatted_items)
            elif isinstance(widget, QLineEdit):
                widget.setPlaceholderText(self.format_arabic_text("ابحث هنا..."))

            widget.setStyleSheet("""
                QLineEdit, QComboBox, QDateEdit {
                    padding: 10px;
                    border: 2px solid #E0E0E0;
                    border-radius: 8px;
                    background-color: white;
                    margin-bottom: 8px;
                    font-size: 11px;
                }
                QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                    border-color: #2196F3;
                }
            """)
            layout.addWidget(widget)

        # مساحة مرنة
        layout.addStretch()

        # أزرار العمليات
        buttons_data = [
            ("🔍", "بحث", "#4CAF50"),
            ("🔽", "تصفية", "#2196F3"),
            ("📤", "تصدير", "#FF9800"),
            ("🖨️", "طباعة", "#9C27B0")
        ]

        for icon, text, color in buttons_data:
            formatted_text = self.format_arabic_text(f"{icon} {text}")
            btn = QPushButton(formatted_text)
            btn.setFont(QFont(self.arabic_font.family(), 11, QFont.Bold))
            btn.setMinimumHeight(40)
            btn.setLayoutDirection(Qt.RightToLeft)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    margin: 4px;
                    font-size: 11px;
                }}
                QPushButton:hover {{
                    background-color: {color}DD;
                    transform: translateY(-1px);
                }}
                QPushButton:pressed {{
                    background-color: {color}BB;
                }}
            """)
            layout.addWidget(btn)

        return sidebar

    def setup_styles(self):
        """إعداد الأنماط والألوان مع دعم العربية"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #FAFAFA;
                font-family: "{self.arabic_font.family()}";
                font-size: 11px;
            }}

            QFrame {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 10px;
                margin: 2px;
            }}

            QTreeWidget {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                font-size: 12px;
                padding: 8px;
                outline: none;
                font-family: "{self.arabic_font.family()}";
            }}

            QTreeWidget::item {{
                padding: 12px 10px;
                border-bottom: 1px solid #F5F5F5;
                border-radius: 6px;
                margin: 2px;
                text-align: right;
            }}

            QTreeWidget::item:hover {{
                background-color: #E3F2FD;
                color: #1976D2;
                font-weight: bold;
            }}

            QTreeWidget::item:selected {{
                background-color: #2196F3;
                color: white;
                font-weight: bold;
            }}

            QToolBar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                border: none;
                spacing: 10px;
                padding: 10px;
                font-size: 10px;
                font-family: "{self.arabic_font.family()}";
            }}

            QToolBar QToolButton {{
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 8px;
                padding: 8px;
                margin: 3px;
                font-family: "{self.arabic_font.family()}";
            }}

            QToolBar QToolButton:hover {{
                background-color: rgba(33, 150, 243, 0.15);
                border-color: #2196F3;
            }}

            QStatusBar {{
                background-color: #F5F5F5;
                border-top: 1px solid #E0E0E0;
                color: #666666;
                font-weight: bold;
                font-family: "{self.arabic_font.family()}";
                padding: 5px;
            }}

            QLabel {{
                font-family: "{self.arabic_font.family()}";
            }}

            QPushButton {{
                font-family: "{self.arabic_font.family()}";
            }}

            QLineEdit {{
                font-family: "{self.arabic_font.family()}";
                text-align: right;
            }}

            QComboBox {{
                font-family: "{self.arabic_font.family()}";
                text-align: right;
            }}

            QDateEdit {{
                font-family: "{self.arabic_font.family()}";
                text-align: right;
            }}
        """)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)

    # إعداد اللغة والاتجاه للعربية
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين اللغة العربية
    locale = QLocale(QLocale.Arabic, QLocale.SaudiArabia)
    QLocale.setDefault(locale)

    # تعيين خط افتراضي يدعم العربية
    arabic_fonts = ["Tahoma", "Arial Unicode MS", "Segoe UI", "Arial"]
    font_set = False

    for font_name in arabic_fonts:
        if QFontDatabase.hasFamily(font_name):
            font = QFont(font_name, 11)
            app.setFont(font)
            font_set = True
            print(f"✅ تم تعيين الخط: {font_name}")
            break

    if not font_set:
        print("⚠️ لم يتم العثور على خط عربي مناسب")

    # إنشاء النافذة الرئيسية
    window = CnXERPMainWindow()
    window.show()

    print("🚀 تم تشغيل نظام CnX ERP بنجاح!")
    print("📱 النافذة مفتوحة في وضع ملء الشاشة")
    print("🌐 دعم كامل للغة العربية مُفعل")

    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
