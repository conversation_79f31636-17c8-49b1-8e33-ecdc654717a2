# نظام إدارة قاعدة بيانات Oracle المتقدم
## Advanced Oracle Database Management System

### 🎯 نظرة عامة
نظام شامل ومتقدم لإدارة قاعدة بيانات Oracle مع واجهة مستخدم حديثة وأدوات احترافية.

### 🚀 الميزات الرئيسية

#### 🏠 لوحة التحكم (Dashboard)
- **معلومات شاملة** عن قاعدة البيانات والاتصال
- **إحصائيات فورية** للجداول والمشاهد والكائنات
- **إجراءات سريعة** للعمليات الشائعة
- **مراقبة حالة الاتصال** في الوقت الفعلي

#### 📝 محرر SQL المتقدم
- **تلوين بناء الجملة** للكلمات المحجوزة والنصوص
- **تنفيذ الاستعلامات** مع عرض النتائج في جداول
- **حفظ وتحميل** الاستعلامات
- **تاريخ الاستعلامات** مع إمكانية البحث
- **اختصارات لوحة المفاتيح** (F5 للتنفيذ)

#### 🗂️ إدارة الكائنات
- **شجرة تفاعلية** لجميع كائنات قاعدة البيانات
- **تصفح وبحث** في الجداول والمشاهد والفهارس
- **عرض DDL** للكائنات المحددة
- **تحليل وتصدير** الكائنات
- **فلترة متقدمة** للبحث السريع

#### 📊 مراقبة الأداء
- **مؤشرات الأداء الرئيسية** (CPU، الذاكرة، الجلسات)
- **مراقبة الجلسات النشطة** مع تفاصيل كل جلسة
- **إحصائيات الاستعلامات** وأوقات التنفيذ
- **تحديث تلقائي** للبيانات

#### 👥 إدارة المستخدمين
- **إنشاء وتعديل** المستخدمين
- **إدارة الصلاحيات** والأدوار
- **مراقبة نشاط المستخدمين**
- **تقارير الوصول** والاستخدام

#### 💾 النسخ الاحتياطية
- **إنشاء نسخ احتياطية** للمخطط أو جداول محددة
- **جدولة النسخ التلقائية**
- **استعادة النسخ** مع خيارات متقدمة
- **ضغط وتشفير** النسخ الاحتياطية

#### 🔒 الأمان والمراجعة
- **تفعيل نظام المراجعة** لجميع العمليات
- **سجل شامل** للعمليات والتغييرات
- **فلترة متقدمة** لسجل المراجعة
- **تقارير أمنية** مفصلة

#### ⚙️ الإعدادات المتقدمة
- **إعدادات الاتصال** مع اختبار فوري
- **تخصيص الواجهة** والسلوك
- **حفظ واستيراد** الإعدادات
- **إعدادات الأداء** والتحسين

### 🛠️ المتطلبات التقنية

#### البرمجيات المطلوبة
- **Python 3.8+**
- **PySide6** للواجهة الرسومية
- **cx_Oracle** للاتصال بقاعدة البيانات
- **psutil** لمراقبة النظام
- **Oracle Client** مثبت ومُعد

#### إعداد قاعدة البيانات
- **Oracle Database 11g+** (يُفضل 19c أو أحدث)
- **مستخدم قاعدة البيانات** مع الصلاحيات المناسبة
- **TNS Names** مُعد بشكل صحيح

### 📁 هيكل الملفات

```
src/ui/database/
├── oracle_database_manager.py    # النظام الرئيسي
├── database_settings_window.py   # نظام SQLite القديم
├── __init__.py                   # ملف التهيئة
└── README_ORACLE_SYSTEM.md      # هذا الملف
```

### 🚀 طريقة الاستخدام

#### 1. من النافذة الرئيسية
```python
# في main_window_prototype.py
from src.ui.database.oracle_database_manager import OracleDatabaseManager

# فتح النظام
oracle_manager = OracleDatabaseManager(parent)
oracle_manager.show()
```

#### 2. تشغيل مستقل
```bash
# تشغيل النظام مباشرة
python test_oracle_system.py

# أو تشغيل الملف الرئيسي
python src/ui/database/oracle_database_manager.py
```

#### 3. إعداد الاتصال
1. افتح تبويب **الإعدادات**
2. أدخل بيانات الاتصال:
   - **المستخدم**: ship2025
   - **كلمة المرور**: ys123
   - **DSN**: yemensoft
3. اضغط **اختبار الاتصال**
4. احفظ الإعدادات

### 🔧 الإعدادات المتقدمة

#### ملف الإعدادات
```json
{
  "connection": {
    "user": "ship2025",
    "password": "ys123",
    "dsn": "yemensoft",
    "encoding": "UTF-8"
  },
  "ui": {
    "auto_refresh_interval": 30,
    "max_rows": 1000,
    "save_query_history": true
  }
}
```

#### متغيرات البيئة
```bash
# مسار ملفات TNS
export TNS_ADMIN=/path/to/network/admin

# مسار Oracle Client
export ORACLE_HOME=/path/to/oracle/client
```

### 🎨 التخصيص والثيمات

النظام يدعم ثيم CnX ERP الحديث مع:
- **ألوان متدرجة** للعناوين
- **أيقونات تعبيرية** للوضوح
- **جداول متناوبة الألوان**
- **أزرار تفاعلية** مع تأثيرات hover

### 🔍 استكشاف الأخطاء

#### مشاكل الاتصال الشائعة
1. **ORA-12154**: TNS غير مُعد بشكل صحيح
2. **ORA-01017**: اسم المستخدم أو كلمة المرور خاطئة
3. **ORA-12541**: خدمة Oracle غير متاحة

#### الحلول
- تأكد من تشغيل خدمة Oracle
- تحقق من ملف tnsnames.ora
- تأكد من صحة بيانات الاتصال
- تحقق من متغيرات البيئة

### 📈 التطوير المستقبلي

#### الميزات المخططة
- **محرر PL/SQL** متقدم مع IntelliSense
- **مصمم الاستعلامات** المرئي
- **تقارير تفاعلية** مع الرسوم البيانية
- **إدارة الفهارس** التلقائية
- **تحليل الأداء** المتقدم
- **دعم Oracle Cloud**

#### المساهمة
- اتبع معايير الكود الموجودة
- أضف تعليقات باللغة العربية
- اختبر جميع الميزات قبل الإرسال
- حدث الوثائق عند الحاجة

### 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملف README الرئيسي
- تحقق من سجل الأخطاء
- تأكد من إعدادات قاعدة البيانات

---

**تم تطوير هذا النظام كجزء من مشروع CnX ERP**  
**© 2024 شركة الأنظمة الذكية والمعلوماتية المتقدمة**
