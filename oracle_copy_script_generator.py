#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منشئ سكريبت نسخ جداول Oracle
Oracle Table Copy Script Generator
"""

import json
from pathlib import Path
from datetime import datetime


class OracleCopyScriptGenerator:
    """منشئ سكريبت نسخ جداول Oracle"""
    
    def __init__(self):
        self.source_user = "ias20241"
        self.target_user = "ship2025"
        self.tables_info = {}
        self.load_tables_info()
    
    def load_tables_info(self):
        """تحميل معلومات الجداول من ملف الفحص"""
        try:
            json_file = Path("oracle_inspection_results/tables_inspection.json")
            if json_file.exists():
                with open(json_file, 'r', encoding='utf-8') as f:
                    self.tables_info = json.load(f)
                print(f"✅ تم تحميل معلومات {len(self.tables_info)} جدول")
            else:
                print("⚠️ لم يتم العثور على ملف الفحص، سيتم استخدام قائمة افتراضية")
                self.tables_info = self.get_default_tables()
        except Exception as e:
            print(f"❌ خطأ في تحميل معلومات الجداول: {e}")
            self.tables_info = self.get_default_tables()
    
    def get_default_tables(self):
        """الحصول على قائمة الجداول الافتراضية"""
        return {
            'CUSTOMERS': {'name': 'CUSTOMERS', 'num_rows': 15000},
            'REMITTANCES': {'name': 'REMITTANCES', 'num_rows': 45000},
            'BRANCHES': {'name': 'BRANCHES', 'num_rows': 25},
            'CURRENCIES': {'name': 'CURRENCIES', 'num_rows': 50},
            'EXCHANGE_RATES': {'name': 'EXCHANGE_RATES', 'num_rows': 2000},
            'TRANSACTIONS': {'name': 'TRANSACTIONS', 'num_rows': 120000},
            'USERS': {'name': 'USERS', 'num_rows': 100},
            'SUPPLIERS': {'name': 'SUPPLIERS', 'num_rows': 500},
            'BANKS': {'name': 'BANKS', 'num_rows': 200},
            'REPORTS': {'name': 'REPORTS', 'num_rows': 5000}
        }
    
    def generate_create_tables_script(self):
        """إنشاء سكريبت إنشاء الجداول"""
        script_lines = [
            "-- سكريبت إنشاء الجداول في المستخدم ship2025",
            "-- Create Tables Script for ship2025 User",
            f"-- تم إنشاؤه في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "-- الاتصال بالمستخدم الجديد",
            "CONNECT ship2025/ys123@yemensoft;",
            "",
            "-- تفعيل عرض النتائج",
            "SET SERVEROUTPUT ON;",
            "",
            "BEGIN",
            "    DBMS_OUTPUT.PUT_LINE('🚀 بدء إنشاء الجداول...');",
            "END;",
            "/",
            ""
        ]
        
        # إنشاء الجداول الأساسية أولاً
        priority_tables = ['BRANCHES', 'CURRENCIES', 'USERS', 'CUSTOMERS', 'SUPPLIERS', 'BANKS']
        other_tables = [name for name in self.tables_info.keys() if name not in priority_tables]
        
        all_tables = priority_tables + other_tables
        
        for table_name in all_tables:
            if table_name in self.tables_info:
                script_lines.extend(self.generate_table_ddl(table_name))
                script_lines.append("")
        
        # إنشاء المتسلسلات
        script_lines.extend([
            "-- إنشاء المتسلسلات",
            "-- Create Sequences",
            ""
        ])
        
        for table_name in all_tables:
            if table_name in self.tables_info:
                script_lines.extend([
                    f"CREATE SEQUENCE SEQ_{table_name}",
                    "    START WITH 1",
                    "    INCREMENT BY 1",
                    "    NOCACHE",
                    "    NOCYCLE;",
                    ""
                ])
        
        # رسالة النهاية
        script_lines.extend([
            "BEGIN",
            "    DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء جميع الجداول والمتسلسلات بنجاح!');",
            "END;",
            "/",
            "",
            "COMMIT;"
        ])
        
        return "\n".join(script_lines)
    
    def generate_table_ddl(self, table_name):
        """إنشاء DDL لجدول محدد"""
        lines = [
            f"-- إنشاء جدول {table_name}",
            f"CREATE TABLE {table_name} ("
        ]
        
        # الحصول على معلومات الأعمدة
        table_info = self.tables_info[table_name]
        columns = table_info.get('columns', [])
        
        if not columns:
            # استخدام أعمدة افتراضية
            columns = self.get_default_columns(table_name)
        
        # إضافة الأعمدة
        for i, col in enumerate(columns):
            col_def = f"    {col['name']} {self.format_data_type(col)}"
            if col.get('nullable', 'Y') == 'N':
                col_def += " NOT NULL"
            
            if i < len(columns) - 1:
                col_def += ","
            
            lines.append(col_def)
        
        lines.append(");")
        
        # إضافة المفتاح الأساسي
        pk_name = f"PK_{table_name}"
        id_column = f"{table_name[:-1]}_ID" if table_name.endswith('S') else f"{table_name}_ID"
        lines.extend([
            "",
            f"-- إضافة المفتاح الأساسي",
            f"ALTER TABLE {table_name} ADD CONSTRAINT {pk_name} PRIMARY KEY ({id_column});"
        ])
        
        return lines
    
    def format_data_type(self, col):
        """تنسيق نوع البيانات"""
        data_type = col['data_type']
        
        if data_type == 'NUMBER':
            if col.get('data_precision') and col.get('data_scale'):
                return f"NUMBER({col['data_precision']},{col['data_scale']})"
            elif col.get('data_precision'):
                return f"NUMBER({col['data_precision']})"
            else:
                return "NUMBER"
        elif data_type == 'VARCHAR2':
            length = col.get('data_length', 100)
            return f"VARCHAR2({length})"
        elif data_type == 'DATE':
            return "DATE"
        elif data_type == 'CLOB':
            return "CLOB"
        else:
            return data_type
    
    def get_default_columns(self, table_name):
        """الحصول على أعمدة افتراضية للجدول"""
        id_column = f"{table_name[:-1]}_ID" if table_name.endswith('S') else f"{table_name}_ID"
        
        base_columns = [
            {'name': id_column, 'data_type': 'NUMBER', 'nullable': 'N'},
            {'name': 'NAME', 'data_type': 'VARCHAR2', 'data_length': 100, 'nullable': 'N'},
            {'name': 'CREATED_DATE', 'data_type': 'DATE', 'nullable': 'N'},
            {'name': 'STATUS', 'data_type': 'VARCHAR2', 'data_length': 10, 'nullable': 'N'}
        ]
        
        # إضافة أعمدة خاصة حسب الجدول
        if table_name == 'CUSTOMERS':
            base_columns.extend([
                {'name': 'CUSTOMER_NAME_EN', 'data_type': 'VARCHAR2', 'data_length': 100, 'nullable': 'Y'},
                {'name': 'ID_NUMBER', 'data_type': 'VARCHAR2', 'data_length': 20, 'nullable': 'Y'},
                {'name': 'PHONE', 'data_type': 'VARCHAR2', 'data_length': 20, 'nullable': 'Y'},
                {'name': 'EMAIL', 'data_type': 'VARCHAR2', 'data_length': 100, 'nullable': 'Y'},
                {'name': 'ADDRESS', 'data_type': 'VARCHAR2', 'data_length': 200, 'nullable': 'Y'}
            ])
        elif table_name == 'REMITTANCES':
            base_columns.extend([
                {'name': 'REMITTANCE_NUMBER', 'data_type': 'VARCHAR2', 'data_length': 20, 'nullable': 'N'},
                {'name': 'CUSTOMER_ID', 'data_type': 'NUMBER', 'nullable': 'N'},
                {'name': 'SENDER_NAME', 'data_type': 'VARCHAR2', 'data_length': 100, 'nullable': 'N'},
                {'name': 'RECEIVER_NAME', 'data_type': 'VARCHAR2', 'data_length': 100, 'nullable': 'N'},
                {'name': 'AMOUNT', 'data_type': 'NUMBER', 'data_precision': 15, 'data_scale': 2, 'nullable': 'N'},
                {'name': 'CURRENCY_ID', 'data_type': 'NUMBER', 'nullable': 'N'},
                {'name': 'REMITTANCE_DATE', 'data_type': 'DATE', 'nullable': 'N'}
            ])
        
        return base_columns
    
    def generate_copy_data_script(self):
        """إنشاء سكريبت نسخ البيانات"""
        script_lines = [
            "-- سكريبت نسخ البيانات من ias20241 إلى ship2025",
            "-- Copy Data Script from ias20241 to ship2025",
            f"-- تم إنشاؤه في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "-- الاتصال بالمستخدم الجديد",
            "CONNECT ship2025/ys123@yemensoft;",
            "",
            "SET SERVEROUTPUT ON;",
            "",
            "BEGIN",
            "    DBMS_OUTPUT.PUT_LINE('🚀 بدء نسخ البيانات...');",
            "END;",
            "/",
            ""
        ]
        
        # ترتيب الجداول حسب التبعيات
        ordered_tables = ['BRANCHES', 'CURRENCIES', 'USERS', 'CUSTOMERS', 'SUPPLIERS', 'BANKS', 
                         'EXCHANGE_RATES', 'REMITTANCES', 'TRANSACTIONS', 'REPORTS']
        
        for table_name in ordered_tables:
            if table_name in self.tables_info:
                num_rows = self.tables_info[table_name].get('num_rows', 0)
                script_lines.extend([
                    f"-- نسخ جدول {table_name} ({num_rows:,} صف)",
                    f"INSERT INTO {table_name}",
                    f"SELECT * FROM ias20241.{table_name};",
                    "",
                    "BEGIN",
                    f"    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول {table_name}: ' || SQL%ROWCOUNT || ' صف');",
                    "END;",
                    "/",
                    "",
                    "COMMIT;",
                    ""
                ])
        
        script_lines.extend([
            "-- تحديث المتسلسلات",
            "-- Update Sequences",
            ""
        ])
        
        for table_name in ordered_tables:
            if table_name in self.tables_info:
                id_column = f"{table_name[:-1]}_ID" if table_name.endswith('S') else f"{table_name}_ID"
                script_lines.extend([
                    f"DECLARE",
                    f"    v_max_id NUMBER;",
                    f"BEGIN",
                    f"    SELECT NVL(MAX({id_column}), 0) + 1 INTO v_max_id FROM {table_name};",
                    f"    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_{table_name}';",
                    f"    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_{table_name} START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';",
                    f"    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة {table_name}: ' || v_max_id);",
                    f"EXCEPTION",
                    f"    WHEN OTHERS THEN",
                    f"        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة {table_name}: ' || SQLERRM);",
                    f"END;",
                    f"/",
                    ""
                ])
        
        script_lines.extend([
            "BEGIN",
            "    DBMS_OUTPUT.PUT_LINE('🎉 تم إكمال نسخ جميع البيانات بنجاح!');",
            "END;",
            "/",
            "",
            "COMMIT;"
        ])
        
        return "\n".join(script_lines)
    
    def generate_all_scripts(self):
        """إنشاء جميع السكريبتات"""
        output_dir = Path("oracle_scripts")
        output_dir.mkdir(exist_ok=True)
        
        # سكريبت إنشاء الجداول
        create_script = self.generate_create_tables_script()
        create_file = output_dir / "create_tables_ship2025.sql"
        with open(create_file, 'w', encoding='utf-8') as f:
            f.write(create_script)
        
        # سكريبت نسخ البيانات
        copy_script = self.generate_copy_data_script()
        copy_file = output_dir / "copy_data_to_ship2025.sql"
        with open(copy_file, 'w', encoding='utf-8') as f:
            f.write(copy_script)
        
        print(f"✅ تم إنشاء السكريبتات:")
        print(f"   📄 إنشاء الجداول: {create_file}")
        print(f"   📄 نسخ البيانات: {copy_file}")
        
        return create_file, copy_file


def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء سكريبتات النسخ")
    print("=" * 60)
    
    generator = OracleCopyScriptGenerator()
    create_file, copy_file = generator.generate_all_scripts()
    
    print(f"\n🎉 تم إنشاء السكريبتات بنجاح!")
    print(f"\n📋 خطوات التنفيذ:")
    print(f"   1. تشغيل: {create_file}")
    print(f"   2. تشغيل: {copy_file}")
    
    return True


if __name__ == "__main__":
    main()
