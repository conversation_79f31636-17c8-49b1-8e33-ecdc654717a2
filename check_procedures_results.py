#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص نتائج نسخ الإجراءات والدوال
Check procedures and functions copy results
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def check_procedures_functions():
    """فحص الإجراءات والدوال في المصدر والهدف"""
    setup_environment()
    
    print("🔍 فحص الإجراءات والدوال والحزم")
    print("=" * 60)
    
    try:
        # الاتصال بالمصدر
        print("📊 فحص الكائنات في المصدر (ias20241)...")
        source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        source_cursor = source_conn.cursor()
        
        # عد الكائنات في المصدر
        source_cursor.execute("""
            SELECT object_type, COUNT(*) 
            FROM user_objects 
            WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
            AND object_name NOT LIKE 'BIN$%'
            AND status = 'VALID'
            GROUP BY object_type
            ORDER BY object_type
        """)
        source_counts = dict(source_cursor.fetchall())
        
        # الاتصال بالهدف
        print("📊 فحص الكائنات في الهدف (ship2025)...")
        target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        target_cursor = target_conn.cursor()
        
        # عد الكائنات في الهدف
        target_cursor.execute("""
            SELECT object_type, COUNT(*) 
            FROM user_objects 
            WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
            AND object_name NOT LIKE 'BIN$%'
            GROUP BY object_type
            ORDER BY object_type
        """)
        target_counts = dict(target_cursor.fetchall())
        
        # عرض النتائج
        print("\n📋 مقارنة الكائنات:")
        print("-" * 60)
        
        type_names = {
            'FUNCTION': 'دوال (Functions)',
            'PACKAGE': 'حزم (Packages)',
            'PACKAGE BODY': 'أجسام الحزم (Package Bodies)',
            'PROCEDURE': 'إجراءات مخزنة (Procedures)'
        }
        
        print(f"{'نوع الكائن':<35} {'المصدر':<10} {'الهدف':<10} {'النسبة':<10}")
        print("-" * 65)
        
        total_source = 0
        total_target = 0
        
        for obj_type in ['FUNCTION', 'PACKAGE', 'PACKAGE BODY', 'PROCEDURE']:
            source_count = source_counts.get(obj_type, 0)
            target_count = target_counts.get(obj_type, 0)
            percentage = (target_count / source_count * 100) if source_count > 0 else 0
            
            total_source += source_count
            total_target += target_count
            
            print(f"{type_names[obj_type]:<35} {source_count:<10} {target_count:<10} {percentage:<10.1f}%")
        
        print("-" * 65)
        print(f"{'الإجمالي':<35} {total_source:<10} {total_target:<10} {(total_target/total_source*100):.1f}%")
        
        # فحص حالة الكائنات
        print(f"\n📊 حالة الكائنات في الهدف:")
        target_cursor.execute("""
            SELECT status, COUNT(*)
            FROM user_objects
            WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
            AND object_name NOT LIKE 'BIN$%'
            GROUP BY status
            ORDER BY status
        """)
        
        status_counts = target_cursor.fetchall()
        
        if status_counts:
            for status, count in status_counts:
                status_icon = "✅" if status == "VALID" else "⚠️"
                percentage = (count / total_target * 100) if total_target > 0 else 0
                print(f"   {status_icon} {status}: {count} كائن ({percentage:.1f}%)")
        else:
            print("   ❌ لم يتم العثور على معلومات حالة الكائنات")
        
        # فحص عينة من الكائنات
        print(f"\n📋 عينة من الكائنات في الهدف:")
        target_cursor.execute("""
            SELECT object_name, object_type, status, created
            FROM user_objects
            WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
            AND object_name NOT LIKE 'BIN$%'
            AND ROWNUM <= 20
            ORDER BY object_type, object_name
        """)
        
        objects = target_cursor.fetchall()
        
        if objects:
            print(f"   تم العثور على {total_target} كائن")
            
            # عرض أول 20 كائن
            for i, (obj_name, obj_type, status, created) in enumerate(objects, 1):
                # تحديد الرمز حسب نوع الكائن
                if obj_type == 'FUNCTION':
                    symbol = '⚙️'
                elif obj_type == 'PROCEDURE':
                    symbol = '📋'
                elif obj_type == 'PACKAGE':
                    symbol = '📦'
                elif obj_type == 'PACKAGE BODY':
                    symbol = '🔧'
                else:
                    symbol = '❓'
                
                status_icon = "✅" if status == "VALID" else "⚠️"
                print(f"   {i:2d}. {symbol} {obj_name} ({obj_type}) {status_icon}")
            
            if total_target > 20:
                print(f"   ... و {total_target - 20} كائن آخر")
        else:
            print("   ❌ لم يتم العثور على كائنات")
        
        # فحص الحزم والأجسام
        print(f"\n📦 فحص الحزم وأجسامها:")
        target_cursor.execute("""
            SELECT p.object_name as package_name,
                   p.status as package_status,
                   pb.status as body_status
            FROM user_objects p
            LEFT JOIN user_objects pb ON p.object_name = pb.object_name 
                                      AND pb.object_type = 'PACKAGE BODY'
            WHERE p.object_type = 'PACKAGE'
            AND p.object_name NOT LIKE 'BIN$%'
            ORDER BY p.object_name
        """)
        
        packages = target_cursor.fetchall()
        
        if packages:
            print(f"   تم العثور على {len(packages)} حزمة")
            
            valid_packages = 0
            packages_with_bodies = 0
            
            for i, (pkg_name, pkg_status, body_status) in enumerate(packages[:15], 1):
                pkg_icon = "✅" if pkg_status == "VALID" else "⚠️"
                
                if body_status:
                    body_icon = "✅" if body_status == "VALID" else "⚠️"
                    body_text = f"+ جسم {body_icon}"
                    packages_with_bodies += 1
                else:
                    body_text = "بدون جسم"
                
                if pkg_status == "VALID":
                    valid_packages += 1
                
                print(f"   {i:2d}. 📦 {pkg_name} {pkg_icon} ({body_text})")
            
            if len(packages) > 15:
                print(f"   ... و {len(packages) - 15} حزمة أخرى")
            
            print(f"\n   📊 إحصائيات الحزم:")
            print(f"      ✅ حزم صالحة: {valid_packages}/{len(packages)}")
            print(f"      🔧 حزم لها أجسام: {packages_with_bodies}/{len(packages)}")
        else:
            print("   ❌ لم يتم العثور على حزم")
        
        # فحص الكائنات غير الصالحة
        print(f"\n⚠️ فحص الكائنات غير الصالحة:")
        target_cursor.execute("""
            SELECT object_name, object_type, status
            FROM user_objects
            WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
            AND object_name NOT LIKE 'BIN$%'
            AND status != 'VALID'
            ORDER BY object_type, object_name
        """)
        
        invalid_objects = target_cursor.fetchall()
        
        if invalid_objects:
            print(f"   تم العثور على {len(invalid_objects)} كائن غير صالح:")
            for obj_name, obj_type, status in invalid_objects[:10]:
                print(f"   ⚠️ {obj_name} ({obj_type}) - {status}")
            if len(invalid_objects) > 10:
                print(f"   ... و {len(invalid_objects) - 10} كائن غير صالح آخر")
        else:
            print("   ✅ جميع الكائنات صالحة!")
        
        # إغلاق الاتصالات
        source_cursor.close()
        source_conn.close()
        target_cursor.close()
        target_conn.close()
        
        print("\n" + "=" * 60)
        
        if total_target > 0:
            print("✅ تم نسخ الإجراءات والدوال والحزم بنجاح!")
        else:
            print("❌ لم يتم نسخ أي كائنات")
        
        return total_target > 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص الكائنات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    success = check_procedures_functions()
    
    if success:
        print("🎉 فحص الإجراءات والدوال مكتمل!")
    else:
        print("💥 فشل في فحص الإجراءات والدوال!")
    
    return success


if __name__ == "__main__":
    main()
