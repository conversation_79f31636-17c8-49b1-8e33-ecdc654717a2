-- سكريبت فحص شامل لجداول المستخدم ias20241
-- Comprehensive Inspection Script for ias20241 Tables

-- الاتصال بالمستخدم ias20241
CONNECT ias20241/ys123@yemensoft;

-- عرض جميع الجداول
SELECT 'قائمة الجداول في المستخدم ias20241' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    table_name AS "اسم الجدول",
    num_rows AS "عدد الصفوف",
    blocks AS "عدد البلوكات",
    avg_row_len AS "متوسط طول الصف",
    last_analyzed AS "آخر تحليل"
FROM user_tables
ORDER BY table_name;

-- عرض تفاصيل الأعمدة لكل جدول
SELECT 'تفاصيل أعمدة الجداول' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    table_name AS "الجدول",
    column_name AS "العمود",
    data_type AS "نوع البيانات",
    data_length AS "الطول",
    data_precision AS "الدقة",
    data_scale AS "المقياس",
    nullable AS "يقبل NULL",
    column_id AS "ترتيب العمود"
FROM user_tab_columns
ORDER BY table_name, column_id;

-- عرض الفهارس
SELECT 'قائمة الفهارس' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    index_name AS "اسم الفهرس",
    table_name AS "الجدول",
    index_type AS "نوع الفهرس",
    uniqueness AS "الفرادة",
    status AS "الحالة"
FROM user_indexes
ORDER BY table_name, index_name;

-- عرض القيود
SELECT 'قائمة القيود' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    constraint_name AS "اسم القيد",
    table_name AS "الجدول",
    constraint_type AS "نوع القيد",
    status AS "الحالة",
    validated AS "مُتحقق"
FROM user_constraints
ORDER BY table_name, constraint_type;

-- عرض المتسلسلات
SELECT 'قائمة المتسلسلات' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    sequence_name AS "اسم المتسلسلة",
    min_value AS "القيمة الدنيا",
    max_value AS "القيمة العليا",
    increment_by AS "الزيادة",
    cycle_flag AS "دورية",
    cache_size AS "حجم التخزين المؤقت",
    last_number AS "آخر رقم"
FROM user_sequences
ORDER BY sequence_name;

-- عرض المشاهدات
SELECT 'قائمة المشاهدات' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    view_name AS "اسم المشاهدة",
    text_length AS "طول النص"
FROM user_views
ORDER BY view_name;

-- عرض الإجراءات والدوال
SELECT 'قائمة الإجراءات والدوال' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    object_name AS "اسم الكائن",
    object_type AS "نوع الكائن",
    status AS "الحالة",
    created AS "تاريخ الإنشاء",
    last_ddl_time AS "آخر تعديل"
FROM user_objects
WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'TRIGGER')
ORDER BY object_type, object_name;

-- عرض المرادفات
SELECT 'قائمة المرادفات' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    synonym_name AS "اسم المرادف",
    table_owner AS "مالك الجدول",
    table_name AS "اسم الجدول"
FROM user_synonyms
ORDER BY synonym_name;

-- إحصائيات عامة
SELECT 'إحصائيات عامة' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    'عدد الجداول' AS "النوع",
    COUNT(*) AS "العدد"
FROM user_tables
UNION ALL
SELECT 
    'عدد الفهارس' AS "النوع",
    COUNT(*) AS "العدد"
FROM user_indexes
UNION ALL
SELECT 
    'عدد القيود' AS "النوع",
    COUNT(*) AS "العدد"
FROM user_constraints
UNION ALL
SELECT 
    'عدد المتسلسلات' AS "النوع",
    COUNT(*) AS "العدد"
FROM user_sequences
UNION ALL
SELECT 
    'عدد المشاهدات' AS "النوع",
    COUNT(*) AS "العدد"
FROM user_views;

-- عرض حجم البيانات لكل جدول
SELECT 'أحجام الجداول' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

SELECT 
    segment_name AS "اسم الجدول",
    ROUND(bytes/1024/1024, 2) AS "الحجم (MB)",
    blocks AS "عدد البلوكات",
    extents AS "عدد الامتدادات"
FROM user_segments
WHERE segment_type = 'TABLE'
ORDER BY bytes DESC;

-- عرض عينة من البيانات لكل جدول (أول 5 صفوف)
SELECT 'عينة من البيانات' AS INFO FROM DUAL;
SELECT '=' || LPAD('=', 50, '=') || '=' AS SEPARATOR FROM DUAL;

-- سيتم إنشاء استعلامات ديناميكية لكل جدول
DECLARE
    v_sql VARCHAR2(4000);
    v_count NUMBER;
BEGIN
    FOR rec IN (SELECT table_name FROM user_tables ORDER BY table_name) LOOP
        -- عد الصفوف
        v_sql := 'SELECT COUNT(*) FROM ' || rec.table_name;
        EXECUTE IMMEDIATE v_sql INTO v_count;
        
        DBMS_OUTPUT.PUT_LINE('جدول: ' || rec.table_name || ' - عدد الصفوف: ' || v_count);
        
        IF v_count > 0 THEN
            DBMS_OUTPUT.PUT_LINE('عينة من البيانات:');
            v_sql := 'SELECT * FROM ' || rec.table_name || ' WHERE ROWNUM <= 3';
            -- يمكن تنفيذ هذا الاستعلام حسب الحاجة
        END IF;
        
        DBMS_OUTPUT.PUT_LINE('---');
    END LOOP;
END;
/

-- تصدير DDL للجداول الرئيسية
SELECT 'انتهى فحص قاعدة البيانات' AS INFO FROM DUAL;
SELECT 'يمكن الآن البدء في عملية النسخ' AS NEXT_STEP FROM DUAL;
