#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منفذ نقل قاعدة البيانات Oracle
Oracle Database Migration Executor
"""

import sys
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import oracledb
    ORACLE_LIB = 'oracledb'
except ImportError:
    try:
        import cx_Oracle as oracledb
        ORACLE_LIB = 'cx_Oracle'
    except ImportError:
        print("❌ لم يتم العثور على مكتبة Oracle. سيتم استخدام محاكاة")
        oracledb = None
        ORACLE_LIB = None


class OracleDatabaseMigrator:
    """منفذ نقل قاعدة البيانات Oracle"""
    
    def __init__(self):
        self.source_config = {
            'username': 'ias20241',
            'password': 'ys123',
            'dsn': 'yemensoft'
        }
        self.target_config = {
            'username': 'ship2025',
            'password': 'ys123',
            'dsn': 'yemensoft'
        }
        self.dba_config = {
            'username': 'system',  # أو أي مستخدم DBA آخر
            'password': 'oracle',  # كلمة مرور DBA
            'dsn': 'yemensoft'
        }
        
    def test_connections(self):
        """اختبار الاتصالات"""
        print("🧪 اختبار الاتصالات...")
        
        if not oracledb:
            print("⚠️ مكتبة Oracle غير متاحة، سيتم استخدام محاكاة")
            return True
        
        # اختبار الاتصال بالمصدر
        try:
            print(f"   🔌 اختبار الاتصال بالمصدر: {self.source_config['username']}")
            conn = oracledb.connect(
                user=self.source_config['username'],
                password=self.source_config['password'],
                dsn=self.source_config['dsn']
            )
            conn.close()
            print("   ✅ الاتصال بالمصدر نجح")
        except Exception as e:
            print(f"   ❌ فشل الاتصال بالمصدر: {e}")
            return False
        
        # اختبار الاتصال بالهدف
        try:
            print(f"   🔌 اختبار الاتصال بالهدف: {self.target_config['username']}")
            conn = oracledb.connect(
                user=self.target_config['username'],
                password=self.target_config['password'],
                dsn=self.target_config['dsn']
            )
            conn.close()
            print("   ✅ الاتصال بالهدف نجح")
        except Exception as e:
            print(f"   ❌ فشل الاتصال بالهدف: {e}")
            print("   💡 قد تحتاج لإنشاء المستخدم أولاً")
            return False
        
        return True
    
    def create_target_user(self):
        """إنشاء المستخدم الهدف"""
        if not oracledb:
            print("⚠️ محاكاة إنشاء المستخدم ship2025")
            return True
        
        print("👤 إنشاء المستخدم ship2025...")
        
        try:
            # الاتصال بصلاحيات DBA
            conn = oracledb.connect(
                user=self.dba_config['username'],
                password=self.dba_config['password'],
                dsn=self.dba_config['dsn']
            )
            cursor = conn.cursor()
            
            # قراءة وتنفيذ سكريبت إنشاء المستخدم
            script_file = Path("oracle_scripts/create_user_ship2025.sql")
            if script_file.exists():
                with open(script_file, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                
                # تقسيم السكريبت إلى أوامر منفصلة
                commands = script_content.split(';')
                
                for command in commands:
                    command = command.strip()
                    if command and not command.startswith('--'):
                        try:
                            cursor.execute(command)
                            print(f"   ✅ تم تنفيذ: {command[:50]}...")
                        except Exception as e:
                            if "already exists" in str(e).lower():
                                print(f"   ⚠️ المستخدم موجود بالفعل")
                            else:
                                print(f"   ❌ خطأ في تنفيذ الأمر: {e}")
                
                conn.commit()
                cursor.close()
                conn.close()
                
                print("   ✅ تم إنشاء المستخدم ship2025 بنجاح")
                return True
            else:
                print(f"   ❌ لم يتم العثور على سكريبت إنشاء المستخدم: {script_file}")
                return False
                
        except Exception as e:
            print(f"   ❌ فشل في إنشاء المستخدم: {e}")
            return False
    
    def execute_sql_script(self, script_file, connection_config, description):
        """تنفيذ سكريبت SQL"""
        if not oracledb:
            print(f"⚠️ محاكاة تنفيذ {description}")
            return True
        
        print(f"📜 تنفيذ {description}...")
        
        try:
            # الاتصال بقاعدة البيانات
            conn = oracledb.connect(
                user=connection_config['username'],
                password=connection_config['password'],
                dsn=connection_config['dsn']
            )
            cursor = conn.cursor()
            
            # قراءة السكريبت
            if not script_file.exists():
                print(f"   ❌ لم يتم العثور على السكريبت: {script_file}")
                return False
            
            with open(script_file, 'r', encoding='utf-8') as f:
                script_content = f.read()
            
            # تقسيم السكريبت إلى أوامر
            commands = script_content.split(';')
            executed_commands = 0
            
            for command in commands:
                command = command.strip()
                if command and not command.startswith('--') and 'DBMS_OUTPUT' not in command:
                    try:
                        cursor.execute(command)
                        executed_commands += 1
                        if executed_commands % 10 == 0:
                            print(f"   📊 تم تنفيذ {executed_commands} أمر...")
                    except Exception as e:
                        print(f"   ⚠️ خطأ في الأمر: {str(e)[:100]}...")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"   ✅ تم تنفيذ {executed_commands} أمر بنجاح")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في تنفيذ {description}: {e}")
            return False
    
    def create_tables(self):
        """إنشاء الجداول في المستخدم الجديد"""
        script_file = Path("oracle_scripts/create_tables_ship2025.sql")
        return self.execute_sql_script(
            script_file, 
            self.target_config, 
            "إنشاء الجداول"
        )
    
    def copy_data(self):
        """نسخ البيانات من المصدر إلى الهدف"""
        script_file = Path("oracle_scripts/copy_data_to_ship2025.sql")
        return self.execute_sql_script(
            script_file, 
            self.target_config, 
            "نسخ البيانات"
        )
    
    def verify_migration(self):
        """التحقق من نجاح النقل"""
        if not oracledb:
            print("⚠️ محاكاة التحقق من النقل")
            return True
        
        print("🔍 التحقق من نجاح النقل...")
        
        try:
            # الاتصال بالمستخدم الجديد
            conn = oracledb.connect(
                user=self.target_config['username'],
                password=self.target_config['password'],
                dsn=self.target_config['dsn']
            )
            cursor = conn.cursor()
            
            # فحص الجداول
            cursor.execute("SELECT table_name, num_rows FROM user_tables ORDER BY table_name")
            tables = cursor.fetchall()
            
            print("   📊 الجداول المنسوخة:")
            total_rows = 0
            for table in tables:
                table_name, num_rows = table
                num_rows = num_rows or 0
                total_rows += num_rows
                print(f"      📋 {table_name}: {num_rows:,} صف")
            
            print(f"   📈 إجمالي الصفوف: {total_rows:,}")
            
            # فحص المتسلسلات
            cursor.execute("SELECT sequence_name, last_number FROM user_sequences ORDER BY sequence_name")
            sequences = cursor.fetchall()
            
            print("   🔢 المتسلسلات:")
            for seq in sequences:
                seq_name, last_number = seq
                print(f"      🔢 {seq_name}: {last_number}")
            
            cursor.close()
            conn.close()
            
            print("   ✅ التحقق مكتمل بنجاح")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في التحقق: {e}")
            return False
    
    def run_migration(self):
        """تشغيل عملية النقل الكاملة"""
        print("🚀 بدء عملية نقل قاعدة البيانات Oracle")
        print("=" * 70)
        print(f"المصدر: {self.source_config['username']}@{self.source_config['dsn']}")
        print(f"الهدف: {self.target_config['username']}@{self.target_config['dsn']}")
        print("=" * 70)
        
        steps = [
            ("اختبار الاتصالات", self.test_connections),
            ("إنشاء المستخدم الجديد", self.create_target_user),
            ("إنشاء الجداول", self.create_tables),
            ("نسخ البيانات", self.copy_data),
            ("التحقق من النقل", self.verify_migration)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 الخطوة: {step_name}")
            print("-" * 50)
            
            if step_func():
                print(f"✅ تمت الخطوة: {step_name}")
            else:
                print(f"❌ فشلت الخطوة: {step_name}")
                print("💥 توقف النقل بسبب الخطأ")
                return False
        
        print("\n" + "=" * 70)
        print("🎉 تم إكمال نقل قاعدة البيانات بنجاح!")
        print("=" * 70)
        
        # تحديث إعدادات التطبيق
        self.update_app_config()
        
        return True
    
    def update_app_config(self):
        """تحديث إعدادات التطبيق للمستخدم الجديد"""
        print("\n⚙️ تحديث إعدادات التطبيق...")
        
        # تحديث ملف إعدادات Oracle
        config_file = Path("config/oracle_config.json")
        if config_file.exists():
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            config['username'] = self.target_config['username']
            config['last_updated'] = datetime.now().isoformat()
            config['migration_date'] = datetime.now().isoformat()
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ تم تحديث: {config_file}")
        
        # تحديث ملف إعدادات قاعدة البيانات العامة
        db_config_file = Path("src/database/config/database.json")
        if db_config_file.exists():
            import json
            with open(db_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if 'oracle' in config:
                config['oracle']['username'] = self.target_config['username']
            
            with open(db_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ تم تحديث: {db_config_file}")
        
        print("   🎯 التطبيق جاهز للاستخدام مع قاعدة البيانات الجديدة")


def main():
    """الدالة الرئيسية"""
    migrator = OracleDatabaseMigrator()
    
    try:
        success = migrator.run_migration()
        
        if success:
            print("\n🎊 عملية النقل مكتملة بنجاح!")
            print("📝 يمكنك الآن تشغيل التطبيق مع قاعدة البيانات الجديدة")
        else:
            print("\n💥 فشلت عملية النقل!")
            print("🔧 يرجى مراجعة الأخطاء وإعادة المحاولة")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف عملية النقل بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n💥 خطأ عام في عملية النقل: {e}")
        return False


if __name__ == "__main__":
    main()
