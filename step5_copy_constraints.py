#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 5: نسخ القيود والعلاقات
Step 5: Copy constraints and relationships
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class ConstraintCopier:
    """ناسخ القيود والعلاقات"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'primary_keys': 0,
            'foreign_keys': 0,
            'unique_constraints': 0,
            'check_constraints': 0,
            'not_null_constraints': 0,
            'failed_constraints': 0,
            'failed_list': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def get_constraints_info(self):
        """الحصول على معلومات القيود"""
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على القيود (باستثناء NOT NULL)
            cursor.execute("""
                SELECT c.constraint_name, c.constraint_type, c.table_name,
                       c.search_condition, c.r_owner, c.r_constraint_name,
                       c.delete_rule, c.status, c.deferrable, c.deferred,
                       c.validated, c.generated
                FROM user_constraints c
                WHERE c.constraint_type IN ('P', 'R', 'U', 'C')
                AND c.table_name NOT LIKE 'BIN$%'
                AND (c.constraint_type != 'C' OR c.search_condition NOT LIKE '%IS NOT NULL%')
                ORDER BY 
                    CASE c.constraint_type 
                        WHEN 'P' THEN 1  -- Primary Key أولاً
                        WHEN 'U' THEN 2  -- Unique ثانياً
                        WHEN 'C' THEN 3  -- Check ثالثاً
                        WHEN 'R' THEN 4  -- Foreign Key أخيراً
                    END,
                    c.table_name, c.constraint_name
            """)
            
            constraints = cursor.fetchall()
            
            print(f"📊 تم العثور على {len(constraints)} قيد للنسخ")
            
            # تصنيف القيود
            constraint_types = {}
            for cons in constraints:
                cons_type = cons[1]
                if cons_type not in constraint_types:
                    constraint_types[cons_type] = 0
                constraint_types[cons_type] += 1
            
            print("📋 تصنيف القيود:")
            type_names = {'P': 'مفاتيح أساسية', 'R': 'مفاتيح خارجية', 'U': 'قيود فريدة', 'C': 'قيود فحص'}
            for cons_type, count in constraint_types.items():
                print(f"   {type_names.get(cons_type, cons_type)}: {count}")
            
            # الحصول على أعمدة كل قيد
            constraints_info = []
            for cons_data in constraints:
                constraint_name = cons_data[0]
                
                # الحصول على أعمدة القيد
                cursor.execute("""
                    SELECT column_name, position
                    FROM user_cons_columns
                    WHERE constraint_name = :constraint_name
                    ORDER BY position
                """, {'constraint_name': constraint_name})
                
                columns = cursor.fetchall()
                
                constraint_info = {
                    'name': constraint_name,
                    'type': cons_data[1],
                    'table_name': cons_data[2],
                    'search_condition': cons_data[3],
                    'r_owner': cons_data[4],
                    'r_constraint_name': cons_data[5],
                    'delete_rule': cons_data[6],
                    'status': cons_data[7],
                    'deferrable': cons_data[8],
                    'deferred': cons_data[9],
                    'validated': cons_data[10],
                    'generated': cons_data[11],
                    'columns': [col[0] for col in columns]
                }
                
                constraints_info.append(constraint_info)
            
            cursor.close()
            return constraints_info
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات القيود: {e}")
            return []
    
    def generate_constraint_ddl(self, constraint_info):
        """إنشاء DDL للقيد"""
        try:
            constraint_name = constraint_info['name']
            constraint_type = constraint_info['type']
            table_name = constraint_info['table_name']
            columns = constraint_info['columns']
            
            if not columns:
                return None
            
            columns_str = ", ".join(columns)
            
            # بناء DDL حسب نوع القيد
            if constraint_type == 'P':  # Primary Key
                ddl = f"ALTER TABLE {table_name} ADD CONSTRAINT {constraint_name} PRIMARY KEY ({columns_str})"
                
            elif constraint_type == 'U':  # Unique
                ddl = f"ALTER TABLE {table_name} ADD CONSTRAINT {constraint_name} UNIQUE ({columns_str})"
                
            elif constraint_type == 'C':  # Check
                search_condition = constraint_info['search_condition']
                if search_condition:
                    ddl = f"ALTER TABLE {table_name} ADD CONSTRAINT {constraint_name} CHECK ({search_condition})"
                else:
                    return None
                    
            elif constraint_type == 'R':  # Foreign Key
                r_constraint_name = constraint_info['r_constraint_name']
                if r_constraint_name:
                    # الحصول على الجدول والأعمدة المرجعية
                    try:
                        cursor = self.source_conn.cursor()
                        cursor.execute("""
                            SELECT c.table_name, cc.column_name
                            FROM user_constraints c, user_cons_columns cc
                            WHERE c.constraint_name = cc.constraint_name
                            AND c.constraint_name = :r_constraint_name
                            ORDER BY cc.position
                        """, {'r_constraint_name': r_constraint_name})
                        
                        ref_info = cursor.fetchall()
                        cursor.close()
                        
                        if ref_info:
                            ref_table = ref_info[0][0]
                            ref_columns = [row[1] for row in ref_info]
                            ref_columns_str = ", ".join(ref_columns)
                            
                            ddl = f"ALTER TABLE {table_name} ADD CONSTRAINT {constraint_name} FOREIGN KEY ({columns_str}) REFERENCES {ref_table} ({ref_columns_str})"
                            
                            # إضافة قاعدة الحذف إذا وجدت
                            delete_rule = constraint_info['delete_rule']
                            if delete_rule and delete_rule != 'NO ACTION':
                                if delete_rule == 'CASCADE':
                                    ddl += " ON DELETE CASCADE"
                                elif delete_rule == 'SET NULL':
                                    ddl += " ON DELETE SET NULL"
                        else:
                            return None
                    except Exception as e:
                        print(f"   ❌ خطأ في الحصول على معلومات المرجع للقيد {constraint_name}: {e}")
                        return None
                else:
                    return None
            else:
                return None
            
            # إضافة خصائص إضافية
            if constraint_info.get('deferrable') == 'DEFERRABLE':
                ddl += " DEFERRABLE"
                if constraint_info.get('deferred') == 'DEFERRED':
                    ddl += " INITIALLY DEFERRED"
                else:
                    ddl += " INITIALLY IMMEDIATE"
            
            return ddl
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء DDL للقيد {constraint_info['name']}: {e}")
            return None
    
    def create_constraint(self, constraint_info):
        """إنشاء قيد في الهدف"""
        try:
            constraint_name = constraint_info['name']
            table_name = constraint_info['table_name']
            constraint_type = constraint_info['type']
            
            target_cursor = self.target_conn.cursor()
            
            # التحقق من وجود الجدول في الهدف
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
            """, {'table_name': table_name})
            
            if target_cursor.fetchone()[0] == 0:
                print(f"   ⚠️ الجدول {table_name} غير موجود - تخطي القيد {constraint_name}")
                target_cursor.close()
                return 'skipped'
            
            # التحقق من وجود القيد
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_constraints WHERE constraint_name = :constraint_name
            """, {'constraint_name': constraint_name})
            
            if target_cursor.fetchone()[0] > 0:
                print(f"   ⚠️ القيد {constraint_name} موجود بالفعل - سيتم تخطيه")
                target_cursor.close()
                return 'skipped'
            
            # إنشاء DDL
            ddl = self.generate_constraint_ddl(constraint_info)
            if not ddl:
                target_cursor.close()
                return 'failed'
            
            # إنشاء القيد
            target_cursor.execute(ddl)
            self.target_conn.commit()
            target_cursor.close()
            
            # تحديث الإحصائيات
            if constraint_type == 'P':
                self.stats['primary_keys'] += 1
            elif constraint_type == 'R':
                self.stats['foreign_keys'] += 1
            elif constraint_type == 'U':
                self.stats['unique_constraints'] += 1
            elif constraint_type == 'C':
                self.stats['check_constraints'] += 1
            
            return 'created'
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء القيد {constraint_info['name']}: {e}")
            self.stats['failed_constraints'] += 1
            self.stats['failed_list'].append(constraint_name)
            return 'failed'
    
    def copy_constraints(self, batch_size=50):
        """نسخ جميع القيود"""
        print("🚀 بدء نسخ القيود والعلاقات")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # الحصول على معلومات القيود
        constraints_info = self.get_constraints_info()
        if not constraints_info:
            print("❌ لم يتم العثور على قيود للنسخ")
            return False
        
        print(f"📊 سيتم نسخ {len(constraints_info)} قيد")
        
        # نسخ القيود على دفعات
        total_created = 0
        total_skipped = 0
        
        for i in range(0, len(constraints_info), batch_size):
            batch = constraints_info[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(constraints_info) + batch_size - 1) // batch_size
            
            print(f"\n📦 الدفعة {batch_num}/{total_batches} ({len(batch)} قيد)")
            print("-" * 40)
            
            for j, constraint_info in enumerate(batch, 1):
                constraint_name = constraint_info['name']
                table_name = constraint_info['table_name']
                constraint_type = constraint_info['type']
                
                type_names = {'P': 'مفتاح أساسي', 'R': 'مفتاح خارجي', 'U': 'قيد فريد', 'C': 'قيد فحص'}
                type_name = type_names.get(constraint_type, constraint_type)
                
                print(f"[{i + j:4d}/{len(constraints_info)}] إنشاء {type_name}: {constraint_name} على {table_name}")
                
                result = self.create_constraint(constraint_info)
                
                if result == 'created':
                    print(f"   ✅ تم إنشاء القيد {constraint_name}")
                    total_created += 1
                elif result == 'skipped':
                    total_skipped += 1
                # failed تم حسابه في الدالة
            
            # عرض تقدم الدفعة
            print(f"✅ تمت الدفعة {batch_num}: {total_created} نجح، {self.stats['failed_constraints']} فشل، {total_skipped} تخطي")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("🎉 تم إكمال نسخ القيود!")
        print(f"✅ المفاتيح الأساسية: {self.stats['primary_keys']}")
        print(f"🔗 المفاتيح الخارجية: {self.stats['foreign_keys']}")
        print(f"🔒 القيود الفريدة: {self.stats['unique_constraints']}")
        print(f"✔️ قيود الفحص: {self.stats['check_constraints']}")
        print(f"❌ القيود الفاشلة: {self.stats['failed_constraints']}")
        print(f"⚠️ القيود المتخطاة: {total_skipped}")
        print(f"⏱️ المدة: {duration}")
        
        if self.stats['failed_list']:
            print(f"\n❌ القيود الفاشلة:")
            for constraint in self.stats['failed_list'][:10]:  # أول 10
                print(f"   - {constraint}")
            if len(self.stats['failed_list']) > 10:
                print(f"   ... و {len(self.stats['failed_list']) - 10} قيد آخر")
        
        print("=" * 60)
        
        return total_created > 0
    
    def verify_constraints(self):
        """التحقق من القيود المنسوخة"""
        print("\n🔍 التحقق من القيود المنسوخة...")
        
        try:
            # عد القيود في الهدف
            target_cursor = self.target_conn.cursor()
            target_cursor.execute("""
                SELECT constraint_type, COUNT(*) 
                FROM user_constraints 
                WHERE constraint_type IN ('P', 'R', 'U', 'C')
                GROUP BY constraint_type
                ORDER BY constraint_type
            """)
            target_counts = dict(target_cursor.fetchall())
            
            # عد القيود في المصدر
            source_cursor = self.source_conn.cursor()
            source_cursor.execute("""
                SELECT constraint_type, COUNT(*) 
                FROM user_constraints 
                WHERE constraint_type IN ('P', 'R', 'U', 'C')
                AND table_name NOT LIKE 'BIN$%'
                AND (constraint_type != 'C' OR search_condition NOT LIKE '%IS NOT NULL%')
                GROUP BY constraint_type
                ORDER BY constraint_type
            """)
            source_counts = dict(source_cursor.fetchall())
            
            type_names = {'P': 'مفاتيح أساسية', 'R': 'مفاتيح خارجية', 'U': 'قيود فريدة', 'C': 'قيود فحص'}
            
            print("📊 مقارنة القيود:")
            for cons_type in ['P', 'R', 'U', 'C']:
                source_count = source_counts.get(cons_type, 0)
                target_count = target_counts.get(cons_type, 0)
                percentage = (target_count / source_count * 100) if source_count > 0 else 0
                
                print(f"   {type_names[cons_type]}: {target_count}/{source_count} ({percentage:.1f}%)")
            
            target_cursor.close()
            source_cursor.close()
            
            return sum(target_counts.values()) > 0
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_copy_process(self):
        """تشغيل عملية نسخ القيود"""
        try:
            if not self.connect():
                return False
            
            # نسخ القيود
            success = self.copy_constraints()
            
            if success:
                # التحقق من النتائج
                self.verify_constraints()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في عملية النسخ: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = ConstraintCopier()
    success = copier.run_copy_process()
    
    if success:
        print("\n✅ الخطوة 5 مكتملة - تم نسخ القيود والعلاقات")
    else:
        print("\n❌ فشل في الخطوة 5")
    
    return success


if __name__ == "__main__":
    main()
