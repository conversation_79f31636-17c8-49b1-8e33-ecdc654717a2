#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منشئ قاعدة البيانات للتطبيق
Database Creator for Application
"""

import sqlite3
import json
from pathlib import Path
from datetime import datetime


class DatabaseCreator:
    """منشئ قاعدة البيانات"""
    
    def __init__(self, db_path="data/cnx_erp.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(exist_ok=True)
        self.connection = None
        self.cursor = None
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(str(self.db_path))
            self.cursor = self.connection.cursor()
            
            # تفعيل Foreign Keys
            self.cursor.execute("PRAGMA foreign_keys = ON")
            
            print(f"✅ تم الاتصال بقاعدة البيانات: {self.db_path}")
            return True
            
        except Exception as e:
            print(f"❌ فشل في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def create_customers_table(self):
        """إنشاء جدول العملاء"""
        sql = """
        CREATE TABLE IF NOT EXISTS customers (
            customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_name TEXT NOT NULL,
            customer_name_en TEXT,
            id_number TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            nationality TEXT,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED'))
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول العملاء (customers)")
    
    def create_branches_table(self):
        """إنشاء جدول الفروع"""
        sql = """
        CREATE TABLE IF NOT EXISTS branches (
            branch_id INTEGER PRIMARY KEY AUTOINCREMENT,
            branch_name TEXT NOT NULL,
            branch_code TEXT UNIQUE NOT NULL,
            address TEXT,
            phone TEXT,
            manager_name TEXT,
            status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE'))
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول الفروع (branches)")
    
    def create_currencies_table(self):
        """إنشاء جدول العملات"""
        sql = """
        CREATE TABLE IF NOT EXISTS currencies (
            currency_id INTEGER PRIMARY KEY AUTOINCREMENT,
            currency_code TEXT UNIQUE NOT NULL,
            currency_name TEXT NOT NULL,
            currency_name_en TEXT,
            symbol TEXT,
            status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE'))
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول العملات (currencies)")
    
    def create_users_table(self):
        """إنشاء جدول المستخدمين"""
        sql = """
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            role TEXT DEFAULT 'USER' CHECK (role IN ('ADMIN', 'MANAGER', 'USER', 'CASHIER')),
            branch_id INTEGER,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED')),
            FOREIGN KEY (branch_id) REFERENCES branches (branch_id)
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول المستخدمين (users)")
    
    def create_exchange_rates_table(self):
        """إنشاء جدول أسعار الصرف"""
        sql = """
        CREATE TABLE IF NOT EXISTS exchange_rates (
            rate_id INTEGER PRIMARY KEY AUTOINCREMENT,
            from_currency_id INTEGER NOT NULL,
            to_currency_id INTEGER NOT NULL,
            rate DECIMAL(15,6) NOT NULL,
            effective_date DATE NOT NULL,
            created_by INTEGER,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE')),
            FOREIGN KEY (from_currency_id) REFERENCES currencies (currency_id),
            FOREIGN KEY (to_currency_id) REFERENCES currencies (currency_id),
            FOREIGN KEY (created_by) REFERENCES users (user_id)
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول أسعار الصرف (exchange_rates)")
    
    def create_remittances_table(self):
        """إنشاء جدول الحوالات"""
        sql = """
        CREATE TABLE IF NOT EXISTS remittances (
            remittance_id INTEGER PRIMARY KEY AUTOINCREMENT,
            remittance_number TEXT UNIQUE NOT NULL,
            customer_id INTEGER NOT NULL,
            sender_name TEXT NOT NULL,
            sender_id_number TEXT,
            sender_phone TEXT,
            sender_address TEXT,
            receiver_name TEXT NOT NULL,
            receiver_id_number TEXT,
            receiver_phone TEXT,
            receiver_account TEXT,
            receiver_bank TEXT,
            receiver_branch TEXT,
            receiver_swift TEXT,
            receiver_country TEXT,
            receiver_city TEXT,
            receiver_address TEXT,
            amount DECIMAL(15,2) NOT NULL,
            currency_id INTEGER NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            equivalent_amount DECIMAL(15,2),
            fees DECIMAL(15,2) DEFAULT 0.00,
            total_amount DECIMAL(15,2),
            branch_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            remittance_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            purpose TEXT,
            notes TEXT,
            status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'SENT', 'RECEIVED', 'CANCELLED', 'REJECTED')),
            priority TEXT DEFAULT 'NORMAL' CHECK (priority IN ('NORMAL', 'URGENT', 'VERY_URGENT')),
            sms_notification INTEGER DEFAULT 1,
            email_notification INTEGER DEFAULT 0,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_date DATETIME,
            FOREIGN KEY (customer_id) REFERENCES customers (customer_id),
            FOREIGN KEY (currency_id) REFERENCES currencies (currency_id),
            FOREIGN KEY (branch_id) REFERENCES branches (branch_id),
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول الحوالات (remittances)")
    
    def create_remittance_requests_table(self):
        """إنشاء جدول طلبات الحوالات"""
        sql = """
        CREATE TABLE IF NOT EXISTS remittance_requests (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            request_number TEXT UNIQUE NOT NULL,
            request_date DATE NOT NULL,
            branch TEXT,
            exchanger TEXT,
            amount REAL NOT NULL,
            currency TEXT NOT NULL,
            exchange_rate REAL DEFAULT 1.0,
            priority TEXT DEFAULT 'عادي',
            purpose TEXT NOT NULL,
            sender_name TEXT NOT NULL,
            sender_id TEXT,
            sender_nationality TEXT,
            sender_id_type TEXT,
            sender_phone TEXT,
            sender_landline TEXT,
            sender_email TEXT,
            sender_pobox TEXT,
            sender_address TEXT,
            receiver_name TEXT NOT NULL,
            receiver_id TEXT,
            receiver_phone TEXT,
            receiver_account TEXT,
            receiver_bank TEXT,
            receiver_branch TEXT,
            receiver_swift TEXT,
            receiver_country TEXT,
            receiver_city TEXT,
            receiver_bank_country TEXT,
            receiver_address TEXT,
            notes TEXT,
            sms_notification INTEGER DEFAULT 1,
            email_notification INTEGER DEFAULT 0,
            auto_create_remittance INTEGER DEFAULT 1,
            print_receipt INTEGER DEFAULT 0,
            status TEXT DEFAULT 'معلق',
            created_at TEXT NOT NULL,
            updated_at TEXT
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول طلبات الحوالات (remittance_requests)")
    
    def create_banks_table(self):
        """إنشاء جدول البنوك"""
        sql = """
        CREATE TABLE IF NOT EXISTS banks (
            bank_id INTEGER PRIMARY KEY AUTOINCREMENT,
            bank_name TEXT NOT NULL,
            bank_code TEXT UNIQUE,
            swift_code TEXT,
            country TEXT,
            address TEXT,
            phone TEXT,
            website TEXT,
            status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE'))
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول البنوك (banks)")
    
    def create_suppliers_table(self):
        """إنشاء جدول الموردين"""
        sql = """
        CREATE TABLE IF NOT EXISTS suppliers (
            supplier_id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_name TEXT NOT NULL,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            country TEXT,
            bank_details TEXT,
            commission_rate DECIMAL(5,2) DEFAULT 0.00,
            status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE'))
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول الموردين (suppliers)")
    
    def create_transactions_table(self):
        """إنشاء جدول المعاملات"""
        sql = """
        CREATE TABLE IF NOT EXISTS transactions (
            transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_number TEXT UNIQUE NOT NULL,
            transaction_type TEXT NOT NULL CHECK (transaction_type IN ('REMITTANCE', 'EXCHANGE', 'DEPOSIT', 'WITHDRAWAL', 'TRANSFER')),
            reference_id INTEGER,
            reference_type TEXT,
            amount DECIMAL(15,2) NOT NULL,
            currency_id INTEGER NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            equivalent_amount DECIMAL(15,2),
            fees DECIMAL(15,2) DEFAULT 0.00,
            branch_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            customer_id INTEGER,
            transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            description TEXT,
            status TEXT DEFAULT 'COMPLETED' CHECK (status IN ('PENDING', 'COMPLETED', 'CANCELLED', 'FAILED')),
            FOREIGN KEY (currency_id) REFERENCES currencies (currency_id),
            FOREIGN KEY (branch_id) REFERENCES branches (branch_id),
            FOREIGN KEY (user_id) REFERENCES users (user_id),
            FOREIGN KEY (customer_id) REFERENCES customers (customer_id)
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول المعاملات (transactions)")
    
    def create_reports_table(self):
        """إنشاء جدول التقارير"""
        sql = """
        CREATE TABLE IF NOT EXISTS reports (
            report_id INTEGER PRIMARY KEY AUTOINCREMENT,
            report_name TEXT NOT NULL,
            report_type TEXT NOT NULL,
            parameters TEXT,
            generated_by INTEGER NOT NULL,
            generated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            status TEXT DEFAULT 'GENERATED' CHECK (status IN ('GENERATING', 'GENERATED', 'FAILED', 'DELETED')),
            FOREIGN KEY (generated_by) REFERENCES users (user_id)
        )
        """
        self.cursor.execute(sql)
        print("✅ تم إنشاء جدول التقارير (reports)")
    
    def create_indexes(self):
        """إنشاء الفهارس"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_customers_id_number ON customers (id_number)",
            "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers (phone)",
            "CREATE INDEX IF NOT EXISTS idx_remittances_date ON remittances (remittance_date)",
            "CREATE INDEX IF NOT EXISTS idx_remittances_customer ON remittances (customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_remittances_status ON remittances (status)",
            "CREATE INDEX IF NOT EXISTS idx_remittances_number ON remittances (remittance_number)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions (transaction_date)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions (transaction_type)",
            "CREATE INDEX IF NOT EXISTS idx_exchange_rates_date ON exchange_rates (effective_date)",
            "CREATE INDEX IF NOT EXISTS idx_remittance_requests_date ON remittance_requests (request_date)",
            "CREATE INDEX IF NOT EXISTS idx_remittance_requests_status ON remittance_requests (status)"
        ]
        
        for index_sql in indexes:
            self.cursor.execute(index_sql)
        
        print("✅ تم إنشاء جميع الفهارس")
    
    def insert_initial_data(self):
        """إدراج البيانات الأولية"""
        try:
            # إدراج العملات الأساسية
            currencies = [
                ('SAR', 'ريال سعودي', 'Saudi Riyal', 'ر.س'),
                ('USD', 'دولار أمريكي', 'US Dollar', '$'),
                ('EUR', 'يورو', 'Euro', '€'),
                ('GBP', 'جنيه إسترليني', 'British Pound', '£'),
                ('AED', 'درهم إماراتي', 'UAE Dirham', 'د.إ'),
                ('KWD', 'دينار كويتي', 'Kuwaiti Dinar', 'د.ك'),
                ('QAR', 'ريال قطري', 'Qatari Riyal', 'ر.ق'),
                ('BHD', 'دينار بحريني', 'Bahraini Dinar', 'د.ب')
            ]
            
            self.cursor.executemany(
                "INSERT OR IGNORE INTO currencies (currency_code, currency_name, currency_name_en, symbol) VALUES (?, ?, ?, ?)",
                currencies
            )
            
            # إدراج فرع رئيسي
            self.cursor.execute(
                "INSERT OR IGNORE INTO branches (branch_name, branch_code, address, manager_name) VALUES (?, ?, ?, ?)",
                ('الفرع الرئيسي', 'MAIN', 'الرياض، المملكة العربية السعودية', 'مدير الفرع')
            )
            
            # إدراج مستخدم إداري
            self.cursor.execute(
                "INSERT OR IGNORE INTO users (username, password_hash, full_name, role, branch_id) VALUES (?, ?, ?, ?, ?)",
                ('admin', 'admin123', 'المدير العام', 'ADMIN', 1)
            )
            
            print("✅ تم إدراج البيانات الأولية")
            
        except Exception as e:
            print(f"⚠️ خطأ في إدراج البيانات الأولية: {e}")
    
    def create_database(self):
        """إنشاء قاعدة البيانات الكاملة"""
        print("🚀 بدء إنشاء قاعدة البيانات...")
        print("=" * 60)
        
        if not self.connect():
            return False
        
        try:
            # إنشاء الجداول
            self.create_customers_table()
            self.create_branches_table()
            self.create_currencies_table()
            self.create_users_table()
            self.create_exchange_rates_table()
            self.create_remittances_table()
            self.create_remittance_requests_table()
            self.create_banks_table()
            self.create_suppliers_table()
            self.create_transactions_table()
            self.create_reports_table()
            
            # إنشاء الفهارس
            self.create_indexes()
            
            # إدراج البيانات الأولية
            self.insert_initial_data()
            
            # حفظ التغييرات
            self.connection.commit()
            
            print("\n" + "=" * 60)
            print("✅ تم إنشاء قاعدة البيانات بنجاح!")
            print(f"📁 مسار قاعدة البيانات: {self.db_path.absolute()}")
            
            # إحصائيات قاعدة البيانات
            self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = self.cursor.fetchall()
            print(f"📊 عدد الجداول المنشأة: {len(tables)}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
        
        finally:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            print("🔌 تم إغلاق الاتصال بقاعدة البيانات")


def main():
    """الدالة الرئيسية"""
    creator = DatabaseCreator()
    
    if creator.create_database():
        print("\n🎉 تم إنشاء قاعدة البيانات للتطبيق بنجاح!")
        print("📝 يمكنك الآن استخدام التطبيق مع قاعدة البيانات الجديدة")
        return True
    else:
        print("\n💥 فشل في إنشاء قاعدة البيانات!")
        return False


if __name__ == "__main__":
    main()
