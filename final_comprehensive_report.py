#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تقرير نهائي شامل لجميع خطوات نسخ قاعدة البيانات
Final comprehensive report for all database copy steps
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def generate_comprehensive_report():
    """إنشاء تقرير نهائي شامل"""
    setup_environment()
    
    print("📊 تقرير نهائي شامل لنسخ قاعدة البيانات")
    print("=" * 80)
    print(f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔄 نسخ من: ias20241 إلى ship2025")
    print("=" * 80)
    
    try:
        # الاتصال بقواعد البيانات
        print("🔌 الاتصال بقواعد البيانات...")
        source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        # 1. الجداول
        print("\n📋 1. الجداول (Tables)")
        print("-" * 50)
        
        source_cursor.execute("SELECT COUNT(*) FROM user_tables WHERE table_name NOT LIKE 'BIN$%'")
        source_tables = source_cursor.fetchone()[0]
        
        target_cursor.execute("SELECT COUNT(*) FROM user_tables WHERE table_name NOT LIKE 'BIN$%'")
        target_tables = target_cursor.fetchone()[0]
        
        tables_percentage = (target_tables / source_tables * 100) if source_tables > 0 else 0
        print(f"   المصدر: {source_tables:,} جدول")
        print(f"   الهدف: {target_tables:,} جدول")
        print(f"   النسبة: {tables_percentage:.1f}%")
        
        # 2. البيانات
        print("\n📊 2. البيانات (Data)")
        print("-" * 50)
        
        # حساب إجمالي الصفوف في المصدر
        source_cursor.execute("""
            SELECT SUM(num_rows) FROM user_tables 
            WHERE table_name NOT LIKE 'BIN$%' AND num_rows IS NOT NULL
        """)
        source_rows = source_cursor.fetchone()[0] or 0
        
        # حساب إجمالي الصفوف في الهدف
        target_cursor.execute("""
            SELECT SUM(num_rows) FROM user_tables 
            WHERE table_name NOT LIKE 'BIN$%' AND num_rows IS NOT NULL
        """)
        target_rows = target_cursor.fetchone()[0] or 0
        
        data_percentage = (target_rows / source_rows * 100) if source_rows > 0 else 0
        print(f"   المصدر: {source_rows:,} صف (تقريبي)")
        print(f"   الهدف: {target_rows:,} صف (تقريبي)")
        print(f"   النسبة: {data_percentage:.1f}%")
        
        # 3. القيود
        print("\n🔒 3. القيود (Constraints)")
        print("-" * 50)
        
        source_cursor.execute("""
            SELECT constraint_type, COUNT(*) 
            FROM user_constraints 
            WHERE constraint_type IN ('P', 'R', 'U', 'C')
            AND table_name NOT LIKE 'BIN$%'
            GROUP BY constraint_type
            ORDER BY constraint_type
        """)
        source_constraints = dict(source_cursor.fetchall())
        
        target_cursor.execute("""
            SELECT constraint_type, COUNT(*) 
            FROM user_constraints 
            WHERE constraint_type IN ('P', 'R', 'U', 'C')
            GROUP BY constraint_type
            ORDER BY constraint_type
        """)
        target_constraints = dict(target_cursor.fetchall())
        
        constraint_names = {'P': 'مفاتيح أساسية', 'R': 'مفاتيح خارجية', 'U': 'قيود فريدة', 'C': 'قيود فحص'}
        
        for cons_type in ['P', 'R', 'U', 'C']:
            source_count = source_constraints.get(cons_type, 0)
            target_count = target_constraints.get(cons_type, 0)
            percentage = (target_count / source_count * 100) if source_count > 0 else 0
            print(f"   {constraint_names[cons_type]}: {target_count:,}/{source_count:,} ({percentage:.1f}%)")
        
        # 4. الفهارس
        print("\n📊 4. الفهارس (Indexes)")
        print("-" * 50)
        
        source_cursor.execute("""
            SELECT COUNT(*) FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%' AND index_name NOT LIKE 'SYS_%' AND generated = 'N'
        """)
        source_indexes = source_cursor.fetchone()[0]
        
        target_cursor.execute("""
            SELECT COUNT(*) FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%' AND index_name NOT LIKE 'SYS_%' AND generated = 'N'
        """)
        target_indexes = target_cursor.fetchone()[0]
        
        indexes_percentage = (target_indexes / source_indexes * 100) if source_indexes > 0 else 0
        print(f"   المصدر: {source_indexes:,} فهرس")
        print(f"   الهدف: {target_indexes:,} فهرس")
        print(f"   النسبة: {indexes_percentage:.1f}%")
        
        # 5. المشاهد
        print("\n👁️ 5. المشاهد (Views)")
        print("-" * 50)
        
        source_cursor.execute("SELECT COUNT(*) FROM user_views WHERE view_name NOT LIKE 'BIN$%'")
        source_views = source_cursor.fetchone()[0]
        
        target_cursor.execute("SELECT COUNT(*) FROM user_views WHERE view_name NOT LIKE 'BIN$%'")
        target_views = target_cursor.fetchone()[0]
        
        views_percentage = (target_views / source_views * 100) if source_views > 0 else 0
        print(f"   المصدر: {source_views:,} مشهد")
        print(f"   الهدف: {target_views:,} مشهد")
        print(f"   النسبة: {views_percentage:.1f}%")
        
        # 6. الإجراءات والدوال والحزم
        print("\n⚙️ 6. الإجراءات والدوال والحزم (Procedures, Functions, Packages)")
        print("-" * 50)
        
        source_cursor.execute("""
            SELECT object_type, COUNT(*) 
            FROM user_objects 
            WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
            AND object_name NOT LIKE 'BIN$%' AND status = 'VALID'
            GROUP BY object_type
            ORDER BY object_type
        """)
        source_objects = dict(source_cursor.fetchall())
        
        target_cursor.execute("""
            SELECT object_type, COUNT(*) 
            FROM user_objects 
            WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
            AND object_name NOT LIKE 'BIN$%'
            GROUP BY object_type
            ORDER BY object_type
        """)
        target_objects = dict(target_cursor.fetchall())
        
        object_names = {'FUNCTION': 'دوال', 'PACKAGE': 'حزم', 'PACKAGE BODY': 'أجسام الحزم', 'PROCEDURE': 'إجراءات'}
        
        for obj_type in ['FUNCTION', 'PACKAGE', 'PACKAGE BODY', 'PROCEDURE']:
            source_count = source_objects.get(obj_type, 0)
            target_count = target_objects.get(obj_type, 0)
            percentage = (target_count / source_count * 100) if source_count > 0 else 0
            print(f"   {object_names[obj_type]}: {target_count:,}/{source_count:,} ({percentage:.1f}%)")
        
        # 7. المشغلات
        print("\n⚡ 7. المشغلات (Triggers)")
        print("-" * 50)
        
        source_cursor.execute("""
            SELECT COUNT(*) FROM user_triggers 
            WHERE trigger_name NOT LIKE 'BIN$%' AND status = 'ENABLED'
        """)
        source_triggers = source_cursor.fetchone()[0]
        
        target_cursor.execute("SELECT COUNT(*) FROM user_triggers WHERE trigger_name NOT LIKE 'BIN$%'")
        target_triggers = target_cursor.fetchone()[0]
        
        triggers_percentage = (target_triggers / source_triggers * 100) if source_triggers > 0 else 0
        print(f"   المصدر: {source_triggers:,} مشغل")
        print(f"   الهدف: {target_triggers:,} مشغل")
        print(f"   النسبة: {triggers_percentage:.1f}%")
        
        # 8. التسلسلات
        print("\n🔢 8. التسلسلات (Sequences)")
        print("-" * 50)
        
        source_cursor.execute("SELECT COUNT(*) FROM user_sequences WHERE sequence_name NOT LIKE 'BIN$%'")
        source_sequences = source_cursor.fetchone()[0]
        
        target_cursor.execute("SELECT COUNT(*) FROM user_sequences WHERE sequence_name NOT LIKE 'BIN$%'")
        target_sequences = target_cursor.fetchone()[0]
        
        sequences_percentage = (target_sequences / source_sequences * 100) if source_sequences > 0 else 0
        print(f"   المصدر: {source_sequences:,} تسلسل")
        print(f"   الهدف: {target_sequences:,} تسلسل")
        print(f"   النسبة: {sequences_percentage:.1f}%")
        
        # ملخص عام
        print("\n" + "=" * 80)
        print("📊 ملخص عام للنسخ")
        print("=" * 80)
        
        # حساب النسبة الإجمالية
        total_source = (source_tables + source_indexes + source_views + 
                       sum(source_constraints.values()) + sum(source_objects.values()) + 
                       source_triggers + source_sequences)
        
        total_target = (target_tables + target_indexes + target_views + 
                       sum(target_constraints.values()) + sum(target_objects.values()) + 
                       target_triggers + target_sequences)
        
        overall_percentage = (total_target / total_source * 100) if total_source > 0 else 0
        
        print(f"📋 إجمالي الكائنات في المصدر: {total_source:,}")
        print(f"📋 إجمالي الكائنات في الهدف: {total_target:,}")
        print(f"📊 النسبة الإجمالية للنسخ: {overall_percentage:.1f}%")
        
        # تقييم النتائج
        print(f"\n🎯 تقييم النتائج:")
        if overall_percentage >= 90:
            print("   ✅ ممتاز - تم نسخ معظم الكائنات بنجاح")
        elif overall_percentage >= 70:
            print("   ✅ جيد جداً - تم نسخ الكائنات الأساسية بنجاح")
        elif overall_percentage >= 50:
            print("   ⚠️ جيد - تم نسخ الكائنات المهمة")
        else:
            print("   ⚠️ يحتاج مراجعة - نسبة النسخ منخفضة")
        
        print(f"\n💡 ملاحظات:")
        print(f"   - تم نسخ {tables_percentage:.1f}% من الجداول والبيانات")
        print(f"   - تم نسخ {(sum(target_constraints.values())/sum(source_constraints.values())*100):.1f}% من القيود والعلاقات")
        print(f"   - تم نسخ {indexes_percentage:.1f}% من الفهارس")
        print(f"   - تم نسخ {views_percentage:.1f}% من المشاهد")
        print(f"   - الإجراءات والدوال تحتاج نسخ يدوي للكائنات المطلوبة")
        
        # إغلاق الاتصالات
        source_cursor.close()
        source_conn.close()
        target_cursor.close()
        target_conn.close()
        
        print("\n" + "=" * 80)
        print("🎉 تم إكمال التقرير النهائي الشامل!")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    success = generate_comprehensive_report()
    
    if success:
        print("\n🎉 تم إنشاء التقرير النهائي بنجاح!")
    else:
        print("\n💥 فشل في إنشاء التقرير النهائي!")
    
    return success


if __name__ == "__main__":
    main()
