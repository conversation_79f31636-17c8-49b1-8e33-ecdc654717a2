# -*- coding: utf-8 -*-
"""
ويدجت الخطوط المنحنية الملونة
Curved Lines Widget for CnX ERP Design
"""

from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt, QPointF
from PySide6.QtGui import QPainter, QPen, QColor, QLinearGradient, QPainterPath
import math

class CurvedLinesWidget(QWidget):
    """ويدجت مخصص لرسم الخطوط المنحنية الملونة مثل الصورة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(400, 300)
        
    def paintEvent(self, event):
        """رسم الخطوط المنحنية الملونة"""
        painter = QPainter(self)
        if not painter.isActive():
            return
        painter.setRenderHint(QPainter.Antialiasing)
        
        # الحصول على أبعاد الويدجت
        width = self.width()
        height = self.height()
        
        # رسم الخط الأحمر المنحني (أعلى)
        self.draw_curved_line(painter, width, height, 
                             start_color=QColor(220, 38, 127),  # أحمر وردي
                             end_color=QColor(239, 68, 68),     # أحمر
                             y_offset=-50, curve_direction=1)
        
        # رسم الخط الأخضر المنحني (وسط)
        self.draw_curved_line(painter, width, height,
                             start_color=QColor(16, 185, 129),  # أخضر
                             end_color=QColor(34, 197, 94),     # أخضر فاتح
                             y_offset=0, curve_direction=-1)
        
        # رسم الخط الأزرق المنحني (أسفل)
        self.draw_curved_line(painter, width, height,
                             start_color=QColor(59, 130, 246),  # أزرق
                             end_color=QColor(147, 197, 253),   # أزرق فاتح
                             y_offset=50, curve_direction=1)
        
        painter.end()
    
    def draw_curved_line(self, painter, width, height, start_color, end_color, y_offset, curve_direction):
        """رسم خط منحني واحد مع تدرج لوني"""
        
        # إنشاء التدرج اللوني
        gradient = QLinearGradient(0, 0, width, 0)
        gradient.setColorAt(0, start_color)
        gradient.setColorAt(1, end_color)
        
        # إعداد القلم
        pen = QPen()
        pen.setBrush(gradient)
        pen.setWidth(3)
        pen.setCapStyle(Qt.RoundCap)
        painter.setPen(pen)
        
        # إنشاء المسار المنحني
        path = QPainterPath()
        
        # نقطة البداية
        start_x = -50
        start_y = height // 2 + y_offset
        path.moveTo(start_x, start_y)
        
        # إنشاء منحنى بيزيه
        control_points = []
        num_points = 8
        
        for i in range(num_points + 1):
            x = (width + 100) * i / num_points - 50
            
            # حساب الانحناء
            curve_factor = math.sin(i * math.pi / num_points) * 80 * curve_direction
            y = start_y + curve_factor
            
            if i == 0:
                path.moveTo(x, y)
            else:
                # استخدام منحنى بيزيه للحصول على خط ناعم
                prev_x = (width + 100) * (i-1) / num_points - 50
                control_x1 = prev_x + (x - prev_x) / 3
                control_x2 = prev_x + 2 * (x - prev_x) / 3
                
                prev_curve = math.sin((i-1) * math.pi / num_points) * 80 * curve_direction
                prev_y = start_y + prev_curve
                control_y1 = prev_y
                control_y2 = y
                
                path.cubicTo(control_x1, control_y1, control_x2, control_y2, x, y)
        
        # رسم المسار
        painter.drawPath(path)
        
        # رسم خطوط إضافية للحصول على تأثير الشفافية
        for i in range(3):
            alpha_pen = QPen()
            alpha_color = QColor(start_color)
            alpha_color.setAlpha(50 - i * 15)
            alpha_pen.setColor(alpha_color)
            alpha_pen.setWidth(2 + i)
            alpha_pen.setCapStyle(Qt.RoundCap)
            painter.setPen(alpha_pen)
            
            # رسم خطوط متوازية للحصول على تأثير الحجم
            offset_path = QPainterPath()
            for j in range(num_points + 1):
                x = (width + 100) * j / num_points - 50
                curve_factor = math.sin(j * math.pi / num_points) * 80 * curve_direction
                y = start_y + curve_factor + (i + 1) * 2
                
                if j == 0:
                    offset_path.moveTo(x, y)
                else:
                    prev_x = (width + 100) * (j-1) / num_points - 50
                    control_x1 = prev_x + (x - prev_x) / 3
                    control_x2 = prev_x + 2 * (x - prev_x) / 3
                    
                    prev_curve = math.sin((j-1) * math.pi / num_points) * 80 * curve_direction
                    prev_y = start_y + prev_curve + (i + 1) * 2
                    control_y1 = prev_y
                    control_y2 = y
                    
                    offset_path.cubicTo(control_x1, control_y1, control_x2, control_y2, x, y)
            
            painter.drawPath(offset_path)
