# -*- coding: utf-8 -*-
"""
نافذة طلب حوالة - مُعاد تصميمها بالكامل
Remittance Request Window - Completely Redesigned
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QTextEdit, QDateEdit,
    QFrame, QGroupBox, QGridLayout, QMessageBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QScrollArea, QSpacerItem,
    QSizePolicy, QDoubleSpinBox, QCheckBox
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont, QPixmap, QIcon

import sys
from pathlib import Path
from datetime import datetime
import uuid

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.database.remittance_requests_oracle import RemittanceRequestsOracle
    ORACLE_AVAILABLE = True
except ImportError:
    # استخدام النسخة المحاكاة إذا لم تكن مكتبة Oracle متاحة
    print("⚠️ مكتبة Oracle غير متاحة، سيتم استخدام النسخة المحاكاة")
    from src.database.remittance_requests_oracle_mock import RemittanceRequestsOracleMock as RemittanceRequestsOracle
    ORACLE_AVAILABLE = False


class RemittanceRequestWindow(QMainWindow):
    """نافذة طلب حوالة - مُعاد تصميمها"""
    
    # إشارات
    request_created = Signal(dict)
    request_updated = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        oracle_status = "Oracle" if ORACLE_AVAILABLE else "Oracle Mock"
        self.setWindowTitle(f"طلب حوالة - CnX ERP ({oracle_status})")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)

        # متغيرات
        self.current_request_id = None
        self.is_editing = False

        # مدير قاعدة البيانات Oracle
        self.oracle_db = RemittanceRequestsOracle()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        self.apply_styles()

        # تحديث قائمة الطلبات
        self.refresh_requests_list()

    def setup_ui(self):
        """إعداد واجهة المستخدم المبسطة"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # الجانب الأيسر - نموذج طلب الحوالة
        form_widget = self.create_request_form()
        main_layout.addWidget(form_widget, 2)
        
        # الجانب الأيمن - قائمة الطلبات
        list_widget = self.create_requests_list()
        main_layout.addWidget(list_widget, 1)

    def create_request_form(self):
        """إنشاء نموذج طلب الحوالة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("📝 طلب حوالة جديد")
        title_label.setProperty("class", "title")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                margin-bottom: 10px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة التمرير للنموذج
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        form_container = QWidget()
        form_layout = QVBoxLayout(form_container)
        form_layout.setSpacing(15)
        
        # البيانات الأساسية
        basic_group = self.create_basic_info_group()
        form_layout.addWidget(basic_group)
        
        # معلومات المرسل
        sender_group = self.create_sender_info_group()
        form_layout.addWidget(sender_group)
        
        # معلومات المستقبل
        receiver_group = self.create_receiver_info_group()
        form_layout.addWidget(receiver_group)
        
        # الملاحظات
        notes_group = self.create_notes_group()
        form_layout.addWidget(notes_group)
        
        # أزرار الإجراءات
        buttons_layout = self.create_action_buttons()
        form_layout.addLayout(buttons_layout)
        
        form_layout.addStretch()
        scroll_area.setWidget(form_container)
        layout.addWidget(scroll_area)
        
        return widget

    def create_basic_info_group(self):
        """إنشاء مجموعة البيانات الأساسية"""
        group = QGroupBox("📋 البيانات الأساسية")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # رقم الطلب (تلقائي)
        layout.addWidget(QLabel("رقم الطلب:"), 0, 0)
        self.request_number = QLineEdit()
        self.request_number.setReadOnly(True)
        self.request_number.setText(self.generate_request_number())
        layout.addWidget(self.request_number, 0, 1)

        # التاريخ
        layout.addWidget(QLabel("التاريخ:"), 0, 2)
        self.request_date = QDateEdit()
        self.request_date.setDate(QDate.currentDate())
        self.request_date.setCalendarPopup(True)
        layout.addWidget(self.request_date, 0, 3)

        # الفرع
        layout.addWidget(QLabel("الفرع:"), 1, 0)
        self.branch = QComboBox()
        self.branch.addItems(["الفرع الرئيسي", "فرع الرياض", "فرع جدة", "فرع الدمام", "فرع المدينة"])
        layout.addWidget(self.branch, 1, 1)

        # الصراف
        layout.addWidget(QLabel("الصراف:"), 1, 2)
        self.exchanger = QComboBox()
        self.exchanger.addItems(["أحمد محمد", "سارة أحمد", "محمد علي", "فاطمة حسن", "علي محمود"])
        layout.addWidget(self.exchanger, 1, 3)

        # المبلغ
        layout.addWidget(QLabel("مبلغ الحوالة:"), 2, 0)
        self.amount = QDoubleSpinBox()
        self.amount.setRange(1, 999999999)
        self.amount.setDecimals(2)
        self.amount.setSuffix(" ريال")
        layout.addWidget(self.amount, 2, 1)

        # العملة
        layout.addWidget(QLabel("العملة:"), 2, 2)
        self.currency = QComboBox()
        self.currency.addItems([
            "ريال سعودي", "دولار أمريكي", "يورو", "جنيه إسترليني",
            "درهم إماراتي", "دينار كويتي", "ريال قطري", "دينار بحريني"
        ])
        layout.addWidget(self.currency, 2, 3)

        # معدل الصرف
        layout.addWidget(QLabel("معدل الصرف:"), 3, 0)
        self.exchange_rate = QDoubleSpinBox()
        self.exchange_rate.setRange(0.0001, 9999.9999)
        self.exchange_rate.setDecimals(4)
        self.exchange_rate.setValue(1.0000)
        layout.addWidget(self.exchange_rate, 3, 1)

        # الأولوية
        layout.addWidget(QLabel("الأولوية:"), 3, 2)
        self.priority = QComboBox()
        self.priority.addItems(["عادي", "عاجل", "عاجل جداً"])
        layout.addWidget(self.priority, 3, 3)

        # الغرض من التحويل
        layout.addWidget(QLabel("الغرض من التحويل:"), 4, 0)
        self.purpose = QLineEdit()
        self.purpose.setPlaceholderText("مثال: راتب، مساعدة عائلية، تجارة...")
        layout.addWidget(self.purpose, 4, 1, 1, 3)

        return group

    def create_sender_info_group(self):
        """إنشاء مجموعة معلومات المرسل"""
        group = QGroupBox("👤 معلومات المرسل")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # اسم المرسل
        layout.addWidget(QLabel("الاسم الكامل:"), 0, 0)
        self.sender_name = QLineEdit()
        self.sender_name.setPlaceholderText("الاسم الكامل للمرسل")
        layout.addWidget(self.sender_name, 0, 1)

        # رقم الهوية
        layout.addWidget(QLabel("رقم الهوية:"), 0, 2)
        self.sender_id = QLineEdit()
        self.sender_id.setPlaceholderText("رقم الهوية أو الإقامة")
        layout.addWidget(self.sender_id, 0, 3)

        # الجنسية
        layout.addWidget(QLabel("الجنسية:"), 1, 0)
        self.sender_nationality = QComboBox()
        self.sender_nationality.addItems([
            "سعودي", "مصري", "سوري", "أردني", "لبناني", "فلسطيني",
            "عراقي", "يمني", "سوداني", "مغربي", "تونسي", "جزائري",
            "ليبي", "باكستاني", "هندي", "بنغلاديشي", "فلبيني", "أخرى"
        ])
        layout.addWidget(self.sender_nationality, 1, 1)

        # نوع الهوية
        layout.addWidget(QLabel("نوع الهوية:"), 1, 2)
        self.sender_id_type = QComboBox()
        self.sender_id_type.addItems(["هوية وطنية", "إقامة", "جواز سفر", "رخصة قيادة"])
        layout.addWidget(self.sender_id_type, 1, 3)

        # رقم الجوال
        layout.addWidget(QLabel("رقم الجوال:"), 2, 0)
        self.sender_phone = QLineEdit()
        self.sender_phone.setPlaceholderText("05xxxxxxxx")
        layout.addWidget(self.sender_phone, 2, 1)

        # رقم الهاتف الثابت
        layout.addWidget(QLabel("الهاتف الثابت:"), 2, 2)
        self.sender_landline = QLineEdit()
        self.sender_landline.setPlaceholderText("011xxxxxxx")
        layout.addWidget(self.sender_landline, 2, 3)

        # البريد الإلكتروني
        layout.addWidget(QLabel("البريد الإلكتروني:"), 3, 0)
        self.sender_email = QLineEdit()
        self.sender_email.setPlaceholderText("<EMAIL>")
        layout.addWidget(self.sender_email, 3, 1)

        # صندوق البريد
        layout.addWidget(QLabel("صندوق البريد:"), 3, 2)
        self.sender_pobox = QLineEdit()
        self.sender_pobox.setPlaceholderText("ص.ب 12345")
        layout.addWidget(self.sender_pobox, 3, 3)

        # العنوان
        layout.addWidget(QLabel("العنوان:"), 4, 0)
        self.sender_address = QLineEdit()
        self.sender_address.setPlaceholderText("العنوان الكامل")
        layout.addWidget(self.sender_address, 4, 1, 1, 3)

        return group

    def create_receiver_info_group(self):
        """إنشاء مجموعة معلومات المستقبل"""
        group = QGroupBox("🎯 معلومات المستقبل")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # اسم المستقبل
        layout.addWidget(QLabel("الاسم الكامل:"), 0, 0)
        self.receiver_name = QLineEdit()
        self.receiver_name.setPlaceholderText("الاسم الكامل للمستقبل")
        layout.addWidget(self.receiver_name, 0, 1)

        # رقم الهوية
        layout.addWidget(QLabel("رقم الهوية:"), 0, 2)
        self.receiver_id = QLineEdit()
        self.receiver_id.setPlaceholderText("رقم هوية المستقبل")
        layout.addWidget(self.receiver_id, 0, 3)

        # رقم الجوال
        layout.addWidget(QLabel("رقم الجوال:"), 1, 0)
        self.receiver_phone = QLineEdit()
        self.receiver_phone.setPlaceholderText("رقم جوال المستقبل")
        layout.addWidget(self.receiver_phone, 1, 1)

        # رقم الحساب
        layout.addWidget(QLabel("رقم الحساب:"), 1, 2)
        self.receiver_account = QLineEdit()
        self.receiver_account.setPlaceholderText("رقم الحساب البنكي")
        layout.addWidget(self.receiver_account, 1, 3)

        # اسم البنك
        layout.addWidget(QLabel("اسم البنك:"), 2, 0)
        self.receiver_bank = QLineEdit()
        self.receiver_bank.setPlaceholderText("اسم البنك")
        layout.addWidget(self.receiver_bank, 2, 1)

        # فرع البنك
        layout.addWidget(QLabel("فرع البنك:"), 2, 2)
        self.receiver_branch = QLineEdit()
        self.receiver_branch.setPlaceholderText("فرع البنك")
        layout.addWidget(self.receiver_branch, 2, 3)

        # رمز SWIFT
        layout.addWidget(QLabel("رمز SWIFT:"), 3, 0)
        self.receiver_swift = QLineEdit()
        self.receiver_swift.setPlaceholderText("SWIFT Code")
        layout.addWidget(self.receiver_swift, 3, 1)

        # البلد
        layout.addWidget(QLabel("البلد:"), 3, 2)
        self.receiver_country = QComboBox()
        self.receiver_country.addItems([
            "السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان",
            "الأردن", "لبنان", "سوريا", "العراق", "مصر", "المغرب",
            "الجزائر", "تونس", "ليبيا", "السودان", "اليمن", "تركيا",
            "إيران", "باكستان", "الهند", "بنغلاديش", "الفلبين", "إندونيسيا"
        ])
        layout.addWidget(self.receiver_country, 3, 3)

        # المدينة
        layout.addWidget(QLabel("المدينة:"), 4, 0)
        self.receiver_city = QLineEdit()
        self.receiver_city.setPlaceholderText("مدينة المستقبل")
        layout.addWidget(self.receiver_city, 4, 1)

        # بلد البنك
        layout.addWidget(QLabel("بلد البنك:"), 4, 2)
        self.receiver_bank_country = QComboBox()
        self.receiver_bank_country.addItems([
            "السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان",
            "الأردن", "لبنان", "سوريا", "العراق", "مصر", "المغرب",
            "الجزائر", "تونس", "ليبيا", "السودان", "اليمن"
        ])
        layout.addWidget(self.receiver_bank_country, 4, 3)

        # العنوان
        layout.addWidget(QLabel("العنوان:"), 5, 0)
        self.receiver_address = QLineEdit()
        self.receiver_address.setPlaceholderText("عنوان المستقبل الكامل")
        layout.addWidget(self.receiver_address, 5, 1, 1, 3)

        return group

    def create_notes_group(self):
        """إنشاء مجموعة الملاحظات والخيارات"""
        group = QGroupBox("📝 ملاحظات وخيارات إضافية")
        layout = QVBoxLayout(group)

        # الملاحظات
        notes_label = QLabel("الملاحظات:")
        layout.addWidget(notes_label)

        self.notes = QTextEdit()
        self.notes.setPlaceholderText("أي ملاحظات أو تعليمات خاصة...")
        self.notes.setMaximumHeight(80)
        layout.addWidget(self.notes)

        # خيارات الإشعارات والتحويل
        options_layout = QGridLayout()

        # إشعار SMS
        self.sms_notification = QCheckBox("إرسال إشعار SMS للمستقبل")
        self.sms_notification.setChecked(True)
        options_layout.addWidget(self.sms_notification, 0, 0)

        # إشعار بريد إلكتروني
        self.email_notification = QCheckBox("إرسال إشعار بريد إلكتروني")
        self.email_notification.setChecked(False)
        options_layout.addWidget(self.email_notification, 0, 1)

        # إنشاء حوالة تلقائياً
        self.auto_create_remittance = QCheckBox("إنشاء حوالة تلقائياً بعد الموافقة")
        self.auto_create_remittance.setChecked(True)
        options_layout.addWidget(self.auto_create_remittance, 1, 0)

        # طباعة الإيصال
        self.print_receipt = QCheckBox("طباعة إيصال الطلب")
        self.print_receipt.setChecked(False)
        options_layout.addWidget(self.print_receipt, 1, 1)

        layout.addLayout(options_layout)

        return group

    def create_action_buttons(self):
        """إنشاء أزرار الإجراءات"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # زر حفظ
        self.save_btn = QPushButton("💾 حفظ الطلب")
        self.save_btn.setStyleSheet(self.get_button_style("#27ae60", "#2ecc71"))
        layout.addWidget(self.save_btn)
        
        # زر مسح
        self.clear_btn = QPushButton("🗑️ مسح النموذج")
        self.clear_btn.setStyleSheet(self.get_button_style("#f39c12", "#f1c40f"))
        layout.addWidget(self.clear_btn)
        
        # زر إغلاق
        self.close_btn = QPushButton("❌ إغلاق")
        self.close_btn.setStyleSheet(self.get_button_style("#e74c3c", "#ec7063"))
        layout.addWidget(self.close_btn)
        
        layout.addStretch()
        return layout

    def create_requests_list(self):
        """إنشاء قائمة الطلبات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # العنوان
        title_label = QLabel("📋 قائمة الطلبات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #9b59b6, stop:1 #8e44ad);
                color: white;
                border-radius: 8px;
            }
        """)
        layout.addWidget(title_label)

        # جدول الطلبات
        self.requests_table = QTableWidget()
        self.requests_table.setColumnCount(6)
        self.requests_table.setHorizontalHeaderLabels([
            "رقم الطلب", "المرسل", "المستقبل", "المبلغ", "التاريخ", "الحالة"
        ])

        # تنسيق الجدول
        header = self.requests_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.requests_table.setAlternatingRowColors(True)
        self.requests_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.requests_table.verticalHeader().setVisible(False)

        layout.addWidget(self.requests_table)

        # أزرار إدارة الطلبات
        buttons_layout = QHBoxLayout()

        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setEnabled(False)
        self.edit_btn.setProperty("class", "primary")
        buttons_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setEnabled(False)
        self.delete_btn.setProperty("class", "danger")
        buttons_layout.addWidget(self.delete_btn)

        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setProperty("class", "secondary")
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        return widget

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # أزرار النموذج
        self.save_btn.clicked.connect(self.save_request)
        self.clear_btn.clicked.connect(self.clear_form)
        self.close_btn.clicked.connect(self.close)

        # أزرار قائمة الطلبات
        self.edit_btn.clicked.connect(self.edit_selected_request)
        self.delete_btn.clicked.connect(self.delete_selected_request)
        self.refresh_btn.clicked.connect(self.refresh_requests_list)

        # جدول الطلبات
        self.requests_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.requests_table.itemDoubleClicked.connect(self.edit_selected_request)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        # إنشاء الجدول إذا لم يكن موجوداً
        try:
            self.oracle_db.create_table_if_not_exists()
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"تعذر الاتصال بقاعدة البيانات:\n{str(e)}")

    def apply_styles(self):
        """تطبيق الأنماط العامة"""
        self.setStyleSheet("""
            /* نظام الألوان الموحد لـ CnX ERP */
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #2c3e50;
            }

            /* مجموعات الحقول */
            QGroupBox {
                font-weight: bold;
                font-size: 15px;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: rgba(255, 255, 255, 0.9);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 8px;
                font-weight: bold;
            }

            /* حقول الإدخال */
            QLineEdit, QComboBox, QDateEdit, QDoubleSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px 12px;
                font-size: 13px;
                background-color: white;
                selection-background-color: #3498db;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QDoubleSpinBox:focus {
                border-color: #3498db;
                background-color: #f8f9ff;
            }
            QLineEdit:hover, QComboBox:hover, QDateEdit:hover, QDoubleSpinBox:hover {
                border-color: #85c1e9;
            }

            /* منطقة النص */
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 13px;
                background-color: white;
                selection-background-color: #3498db;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: #f8f9ff;
            }

            /* الأزرار */
            QPushButton {
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
            QPushButton:disabled {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #bdc3c7, stop:1 #95a5a6);
                color: #7f8c8d;
            }

            /* الجداول */
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                background-color: white;
                gridline-color: #ecf0f1;
                selection-background-color: #3498db;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #ebf3fd;
            }

            /* رأس الجدول */
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34495e, stop:1 #2c3e50);
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 13px;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5d6d7e, stop:1 #34495e);
            }

            /* مربعات الاختيار */
            QCheckBox {
                font-size: 13px;
                color: #2c3e50;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-color: #2980b9;
            }
            QCheckBox::indicator:checked:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }

            /* التسميات */
            QLabel {
                color: #2c3e50;
                font-size: 13px;
            }

            /* منطقة التمرير */
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #ecf0f1;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #bdc3c7;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #95a5a6;
            }
        """)

    def get_button_style(self, color1, color2):
        """إنشاء نمط الأزرار"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color2}, stop:1 {color1});
            }}
            QPushButton:pressed {{
                background: {color2};
            }}
        """

    def generate_request_number(self):
        """إنشاء رقم طلب تلقائي"""
        return f"REQ{datetime.now().strftime('%Y%m%d%H%M%S')}"



    def save_request(self):
        """حفظ طلب الحوالة"""
        if not self.validate_form():
            return

        try:
            data = self.collect_form_data()

            if self.is_editing and self.current_request_id:
                self.update_request(data)
            else:
                self.create_new_request(data)

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ الطلب:\n{str(e)}")

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if self.amount.value() <= 0:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال مبلغ صحيح")
            self.amount.setFocus()
            return False

        if not self.purpose.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال الغرض من التحويل")
            self.purpose.setFocus()
            return False

        if not self.sender_name.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المرسل")
            self.sender_name.setFocus()
            return False

        if not self.receiver_name.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المستقبل")
            self.receiver_name.setFocus()
            return False

        # التحقق من رقم الهاتف إذا تم إدخاله
        sender_phone = self.sender_phone.text().strip()
        if sender_phone and not self.validate_phone(sender_phone):
            QMessageBox.warning(self, "بيانات غير صحيحة", "رقم هاتف المرسل غير صحيح")
            self.sender_phone.setFocus()
            return False

        receiver_phone = self.receiver_phone.text().strip()
        if receiver_phone and not self.validate_phone(receiver_phone):
            QMessageBox.warning(self, "بيانات غير صحيحة", "رقم هاتف المستقبل غير صحيح")
            self.receiver_phone.setFocus()
            return False

        # التحقق من البريد الإلكتروني إذا تم إدخاله
        sender_email = self.sender_email.text().strip()
        if sender_email and not self.validate_email(sender_email):
            QMessageBox.warning(self, "بيانات غير صحيحة", "بريد المرسل الإلكتروني غير صحيح")
            self.sender_email.setFocus()
            return False

        return True

    def validate_phone(self, phone):
        """التحقق من صحة رقم الهاتف"""
        import re
        # نمط للأرقام السعودية والدولية
        phone = phone.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
        patterns = [
            r'^(\+966|966)?[5-9]\d{8}$',  # سعودي
            r'^(\+\d{1,3})?\d{7,15}$'     # دولي عام
        ]
        return any(re.match(pattern, phone) for pattern in patterns)

    def validate_email(self, email):
        """التحقق من صحة البريد الإلكتروني"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        return {
            'request_number': self.request_number.text(),
            'request_date': self.request_date.date().toString('yyyy-MM-dd'),
            'branch': self.branch.currentText(),
            'exchanger': self.exchanger.currentText(),
            'amount': self.amount.value(),
            'currency': self.currency.currentText(),
            'exchange_rate': self.exchange_rate.value(),
            'priority': self.priority.currentText(),
            'purpose': self.purpose.text().strip(),
            'sender_name': self.sender_name.text().strip(),
            'sender_id': self.sender_id.text().strip(),
            'sender_nationality': self.sender_nationality.currentText(),
            'sender_id_type': self.sender_id_type.currentText(),
            'sender_phone': self.sender_phone.text().strip(),
            'sender_landline': self.sender_landline.text().strip(),
            'sender_email': self.sender_email.text().strip(),
            'sender_pobox': self.sender_pobox.text().strip(),
            'sender_address': self.sender_address.text().strip(),
            'receiver_name': self.receiver_name.text().strip(),
            'receiver_id': self.receiver_id.text().strip(),
            'receiver_phone': self.receiver_phone.text().strip(),
            'receiver_account': self.receiver_account.text().strip(),
            'receiver_bank': self.receiver_bank.text().strip(),
            'receiver_branch': self.receiver_branch.text().strip(),
            'receiver_swift': self.receiver_swift.text().strip(),
            'receiver_country': self.receiver_country.currentText(),
            'receiver_city': self.receiver_city.text().strip(),
            'receiver_bank_country': self.receiver_bank_country.currentText(),
            'receiver_address': self.receiver_address.text().strip(),
            'notes': self.notes.toPlainText().strip(),
            'sms_notification': 1 if self.sms_notification.isChecked() else 0,
            'email_notification': 1 if self.email_notification.isChecked() else 0,
            'auto_create_remittance': 1 if self.auto_create_remittance.isChecked() else 0,
            'print_receipt': 1 if self.print_receipt.isChecked() else 0,
            'status': 'معلق',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

    def create_new_request(self, data):
        """إنشاء طلب جديد"""
        try:
            request_id = self.oracle_db.create_request(data)

            QMessageBox.information(self, "تم الحفظ", f"تم حفظ طلب الحوالة بنجاح!\nرقم الطلب: {data['request_number']}\nمعرف الطلب: {request_id}")

            # إرسال إشارة
            data['id'] = request_id
            self.request_created.emit(data)

            # مسح النموذج وتحديث القائمة
            self.clear_form()
            self.refresh_requests_list()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ الطلب:\n{str(e)}")

    def update_request(self, data):
        """تحديث طلب موجود"""
        try:
            success = self.oracle_db.update_request(self.current_request_id, data)

            if success:
                QMessageBox.information(self, "تم التحديث", "تم تحديث طلب الحوالة بنجاح!")

                # إرسال إشارة
                data['id'] = self.current_request_id
                self.request_updated.emit(data)

                # إعادة تعيين الوضع وتحديث القائمة
                self.reset_form_mode()
                self.refresh_requests_list()
            else:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على الطلب للتحديث")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التحديث", f"فشل في تحديث الطلب:\n{str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        # البيانات الأساسية
        self.request_number.setText(self.generate_request_number())
        self.request_date.setDate(QDate.currentDate())
        self.branch.setCurrentIndex(0)
        self.exchanger.setCurrentIndex(0)
        self.amount.setValue(0)
        self.currency.setCurrentIndex(0)
        self.exchange_rate.setValue(1.0000)
        self.priority.setCurrentIndex(0)
        self.purpose.clear()

        # معلومات المرسل
        self.sender_name.clear()
        self.sender_id.clear()
        self.sender_nationality.setCurrentIndex(0)
        self.sender_id_type.setCurrentIndex(0)
        self.sender_phone.clear()
        self.sender_landline.clear()
        self.sender_email.clear()
        self.sender_pobox.clear()
        self.sender_address.clear()

        # معلومات المستقبل
        self.receiver_name.clear()
        self.receiver_id.clear()
        self.receiver_phone.clear()
        self.receiver_account.clear()
        self.receiver_bank.clear()
        self.receiver_branch.clear()
        self.receiver_swift.clear()
        self.receiver_country.setCurrentIndex(0)
        self.receiver_city.clear()
        self.receiver_bank_country.setCurrentIndex(0)
        self.receiver_address.clear()

        # الملاحظات والخيارات
        self.notes.clear()
        self.sms_notification.setChecked(True)
        self.email_notification.setChecked(False)
        self.auto_create_remittance.setChecked(True)
        self.print_receipt.setChecked(False)

        self.reset_form_mode()

    def reset_form_mode(self):
        """إعادة تعيين وضع النموذج"""
        self.is_editing = False
        self.current_request_id = None
        self.save_btn.setText("💾 حفظ الطلب")

    def refresh_requests_list(self):
        """تحديث قائمة الطلبات"""
        try:
            requests = self.oracle_db.get_all_requests()

            # تحديث الجدول
            self.requests_table.setRowCount(len(requests))

            for row, request in enumerate(requests):
                # البيانات من Oracle: ID, REQUEST_NUMBER, SENDER_NAME, RECEIVER_NAME, AMOUNT, CURRENCY, REQUEST_DATE, STATUS
                request_id = request[0]
                request_number = str(request[1]) if request[1] else f"REQ{row+1:04d}"
                sender_name = str(request[2]) if request[2] else "غير محدد"
                receiver_name = str(request[3]) if request[3] else "غير محدد"
                amount = float(request[4]) if request[4] else 0.0
                currency = str(request[5]) if request[5] else "ريال سعودي"
                request_date = str(request[6]) if request[6] else datetime.now().strftime('%Y-%m-%d')
                status = str(request[7]) if request[7] else "معلق"

                self.requests_table.setItem(row, 0, QTableWidgetItem(request_number))
                self.requests_table.setItem(row, 1, QTableWidgetItem(sender_name))
                self.requests_table.setItem(row, 2, QTableWidgetItem(receiver_name))
                self.requests_table.setItem(row, 3, QTableWidgetItem(f"{amount:.2f} {currency}"))
                self.requests_table.setItem(row, 4, QTableWidgetItem(request_date))
                self.requests_table.setItem(row, 5, QTableWidgetItem(status))

                # حفظ ID في البيانات المخفية
                self.requests_table.item(row, 0).setData(Qt.UserRole, request_id)

        except Exception as e:
            print(f"خطأ في تحديث قائمة الطلبات: {e}")
            QMessageBox.warning(self, "تحذير", f"تعذر تحديث قائمة الطلبات:\n{str(e)}")
            # إنشاء جدول فارغ في حالة الخطأ
            self.requests_table.setRowCount(0)

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول"""
        selected_items = self.requests_table.selectedItems()
        has_selection = len(selected_items) > 0

        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def edit_selected_request(self):
        """تعديل الطلب المحدد"""
        current_row = self.requests_table.currentRow()
        if current_row < 0:
            return

        request_id = self.requests_table.item(current_row, 0).data(Qt.UserRole)
        if not request_id:
            return

        self.load_request_for_editing(request_id)

    def load_request_for_editing(self, request_id):
        """تحميل طلب للتعديل"""
        try:
            request = self.oracle_db.get_request_by_id(request_id)

            if not request:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الطلب")
                return

            # تحويل البيانات إلى قاموس (بناءً على ترتيب الأعمدة في Oracle)
            columns = [
                'ID', 'REQUEST_NUMBER', 'REQUEST_DATE', 'BRANCH', 'EXCHANGER', 'AMOUNT', 'CURRENCY',
                'EXCHANGE_RATE', 'PRIORITY', 'PURPOSE', 'SENDER_NAME', 'SENDER_ID', 'SENDER_NATIONALITY',
                'SENDER_ID_TYPE', 'SENDER_PHONE', 'SENDER_LANDLINE', 'SENDER_EMAIL', 'SENDER_POBOX',
                'SENDER_ADDRESS', 'RECEIVER_NAME', 'RECEIVER_ID', 'RECEIVER_PHONE', 'RECEIVER_ACCOUNT',
                'RECEIVER_BANK', 'RECEIVER_BRANCH', 'RECEIVER_SWIFT', 'RECEIVER_COUNTRY', 'RECEIVER_CITY',
                'RECEIVER_BANK_COUNTRY', 'RECEIVER_ADDRESS', 'NOTES', 'SMS_NOTIFICATION',
                'EMAIL_NOTIFICATION', 'AUTO_CREATE_REMITTANCE', 'PRINT_RECEIPT', 'STATUS', 'CREATED_AT', 'UPDATED_AT'
            ]

            data = {}
            for i, col_name in enumerate(columns):
                data[col_name.lower()] = request[i] if i < len(request) else None

            # تحميل البيانات في النموذج بأمان
            self.request_number.setText(data.get('request_number', ''))

            # التاريخ
            date_obj = data.get('request_date')
            if date_obj:
                try:
                    if hasattr(date_obj, 'strftime'):
                        # إذا كان كائن datetime
                        date_str = date_obj.strftime('%Y-%m-%d')
                        self.request_date.setDate(QDate.fromString(date_str, 'yyyy-MM-dd'))
                    else:
                        # إذا كان نص
                        self.request_date.setDate(QDate.fromString(str(date_obj), 'yyyy-MM-dd'))
                except:
                    self.request_date.setDate(QDate.currentDate())
            else:
                self.request_date.setDate(QDate.currentDate())

            # البيانات الأساسية
            self.branch.setCurrentText(data.get('branch', '') or "")
            self.exchanger.setCurrentText(data.get('exchanger', '') or "")
            self.amount.setValue(float(data.get('amount', 0)) if data.get('amount') else 0.0)
            self.currency.setCurrentText(data.get('currency', '') or "ريال سعودي")
            self.exchange_rate.setValue(float(data.get('exchange_rate', 1.0)) if data.get('exchange_rate') else 1.0)
            self.priority.setCurrentText(data.get('priority', '') or "عادي")
            self.purpose.setText(data.get('purpose', '') or "")

            # معلومات المرسل
            self.sender_name.setText(data.get('sender_name', '') or "")
            self.sender_id.setText(data.get('sender_id', '') or "")
            self.sender_nationality.setCurrentText(data.get('sender_nationality', '') or "سعودي")
            self.sender_id_type.setCurrentText(data.get('sender_id_type', '') or "هوية وطنية")
            self.sender_phone.setText(data.get('sender_phone', '') or "")
            self.sender_landline.setText(data.get('sender_landline', '') or "")
            self.sender_email.setText(data.get('sender_email', '') or "")
            self.sender_pobox.setText(data.get('sender_pobox', '') or "")
            self.sender_address.setText(data.get('sender_address', '') or "")

            # معلومات المستقبل
            self.receiver_name.setText(data.get('receiver_name', '') or "")
            self.receiver_id.setText(data.get('receiver_id', '') or "")
            self.receiver_phone.setText(data.get('receiver_phone', '') or "")
            self.receiver_account.setText(data.get('receiver_account', '') or "")
            self.receiver_bank.setText(data.get('receiver_bank', '') or "")
            self.receiver_branch.setText(data.get('receiver_branch', '') or "")
            self.receiver_swift.setText(data.get('receiver_swift', '') or "")
            self.receiver_country.setCurrentText(data.get('receiver_country', '') or "السعودية")
            self.receiver_city.setText(data.get('receiver_city', '') or "")
            self.receiver_bank_country.setCurrentText(data.get('receiver_bank_country', '') or "السعودية")
            self.receiver_address.setText(data.get('receiver_address', '') or "")

            # الملاحظات والخيارات
            self.notes.setPlainText(data.get('notes', '') or "")
            self.sms_notification.setChecked(bool(data.get('sms_notification', 1)))
            self.email_notification.setChecked(bool(data.get('email_notification', 0)))
            self.auto_create_remittance.setChecked(bool(data.get('auto_create_remittance', 1)))
            self.print_receipt.setChecked(bool(data.get('print_receipt', 0)))

            # تعيين وضع التعديل
            self.is_editing = True
            self.current_request_id = request_id
            self.save_btn.setText("💾 تحديث الطلب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التحميل", f"فشل في تحميل الطلب:\n{str(e)}")

    def delete_selected_request(self):
        """حذف الطلب المحدد"""
        current_row = self.requests_table.currentRow()
        if current_row < 0:
            return

        request_id = self.requests_table.item(current_row, 0).data(Qt.UserRole)
        request_number = self.requests_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف طلب الحوالة رقم {request_number}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.oracle_db.delete_request(request_id)

                if success:
                    QMessageBox.information(self, "تم الحذف", "تم حذف طلب الحوالة بنجاح")
                    self.refresh_requests_list()
                else:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على الطلب للحذف")

            except Exception as e:
                QMessageBox.critical(self, "خطأ في الحذف", f"فشل في حذف الطلب:\n{str(e)}")
