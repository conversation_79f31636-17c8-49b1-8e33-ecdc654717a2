# -*- coding: utf-8 -*-
"""
نافذة طلب حوالة - مُعاد تصميمها بالكامل
Remittance Request Window - Completely Redesigned
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QTextEdit, QDateEdit,
    QFrame, QGroupBox, QGridLayout, QMessageBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QScrollArea, QSpacerItem,
    QSizePolicy, QDoubleSpinBox, QCheckBox
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont, QPixmap, QIcon

import sqlite3
from pathlib import Path
from datetime import datetime
import uuid


class RemittanceRequestWindow(QMainWindow):
    """نافذة طلب حوالة - مُعاد تصميمها"""
    
    # إشارات
    request_created = Signal(dict)
    request_updated = Signal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("طلب حوالة - CnX ERP")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # متغيرات
        self.current_request_id = None
        self.is_editing = False
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        self.apply_styles()
        
        # تحديث قائمة الطلبات
        self.refresh_requests_list()

    def setup_ui(self):
        """إعداد واجهة المستخدم المبسطة"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # الجانب الأيسر - نموذج طلب الحوالة
        form_widget = self.create_request_form()
        main_layout.addWidget(form_widget, 2)
        
        # الجانب الأيمن - قائمة الطلبات
        list_widget = self.create_requests_list()
        main_layout.addWidget(list_widget, 1)

    def create_request_form(self):
        """إنشاء نموذج طلب الحوالة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("📝 طلب حوالة جديد")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة التمرير للنموذج
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        form_container = QWidget()
        form_layout = QVBoxLayout(form_container)
        form_layout.setSpacing(15)
        
        # البيانات الأساسية
        basic_group = self.create_basic_info_group()
        form_layout.addWidget(basic_group)
        
        # معلومات المرسل
        sender_group = self.create_sender_info_group()
        form_layout.addWidget(sender_group)
        
        # معلومات المستقبل
        receiver_group = self.create_receiver_info_group()
        form_layout.addWidget(receiver_group)
        
        # الملاحظات
        notes_group = self.create_notes_group()
        form_layout.addWidget(notes_group)
        
        # أزرار الإجراءات
        buttons_layout = self.create_action_buttons()
        form_layout.addLayout(buttons_layout)
        
        form_layout.addStretch()
        scroll_area.setWidget(form_container)
        layout.addWidget(scroll_area)
        
        return widget

    def create_basic_info_group(self):
        """إنشاء مجموعة البيانات الأساسية"""
        group = QGroupBox("📋 البيانات الأساسية")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # رقم الطلب (تلقائي)
        layout.addWidget(QLabel("رقم الطلب:"), 0, 0)
        self.request_number = QLineEdit()
        self.request_number.setReadOnly(True)
        self.request_number.setText(self.generate_request_number())
        layout.addWidget(self.request_number, 0, 1)

        # التاريخ
        layout.addWidget(QLabel("التاريخ:"), 0, 2)
        self.request_date = QDateEdit()
        self.request_date.setDate(QDate.currentDate())
        self.request_date.setCalendarPopup(True)
        layout.addWidget(self.request_date, 0, 3)

        # الفرع
        layout.addWidget(QLabel("الفرع:"), 1, 0)
        self.branch = QComboBox()
        self.branch.addItems(["الفرع الرئيسي", "فرع الرياض", "فرع جدة", "فرع الدمام", "فرع المدينة"])
        layout.addWidget(self.branch, 1, 1)

        # الصراف
        layout.addWidget(QLabel("الصراف:"), 1, 2)
        self.exchanger = QComboBox()
        self.exchanger.addItems(["أحمد محمد", "سارة أحمد", "محمد علي", "فاطمة حسن", "علي محمود"])
        layout.addWidget(self.exchanger, 1, 3)

        # المبلغ
        layout.addWidget(QLabel("مبلغ الحوالة:"), 2, 0)
        self.amount = QDoubleSpinBox()
        self.amount.setRange(1, 999999999)
        self.amount.setDecimals(2)
        self.amount.setSuffix(" ريال")
        layout.addWidget(self.amount, 2, 1)

        # العملة
        layout.addWidget(QLabel("العملة:"), 2, 2)
        self.currency = QComboBox()
        self.currency.addItems([
            "ريال سعودي", "دولار أمريكي", "يورو", "جنيه إسترليني",
            "درهم إماراتي", "دينار كويتي", "ريال قطري", "دينار بحريني"
        ])
        layout.addWidget(self.currency, 2, 3)

        # معدل الصرف
        layout.addWidget(QLabel("معدل الصرف:"), 3, 0)
        self.exchange_rate = QDoubleSpinBox()
        self.exchange_rate.setRange(0.0001, 9999.9999)
        self.exchange_rate.setDecimals(4)
        self.exchange_rate.setValue(1.0000)
        layout.addWidget(self.exchange_rate, 3, 1)

        # الأولوية
        layout.addWidget(QLabel("الأولوية:"), 3, 2)
        self.priority = QComboBox()
        self.priority.addItems(["عادي", "عاجل", "عاجل جداً"])
        layout.addWidget(self.priority, 3, 3)

        # الغرض من التحويل
        layout.addWidget(QLabel("الغرض من التحويل:"), 4, 0)
        self.purpose = QLineEdit()
        self.purpose.setPlaceholderText("مثال: راتب، مساعدة عائلية، تجارة...")
        layout.addWidget(self.purpose, 4, 1, 1, 3)

        return group

    def create_sender_info_group(self):
        """إنشاء مجموعة معلومات المرسل"""
        group = QGroupBox("👤 معلومات المرسل")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # اسم المرسل
        layout.addWidget(QLabel("الاسم الكامل:"), 0, 0)
        self.sender_name = QLineEdit()
        self.sender_name.setPlaceholderText("الاسم الكامل للمرسل")
        layout.addWidget(self.sender_name, 0, 1)

        # رقم الهوية
        layout.addWidget(QLabel("رقم الهوية:"), 0, 2)
        self.sender_id = QLineEdit()
        self.sender_id.setPlaceholderText("رقم الهوية أو الإقامة")
        layout.addWidget(self.sender_id, 0, 3)

        # الجنسية
        layout.addWidget(QLabel("الجنسية:"), 1, 0)
        self.sender_nationality = QComboBox()
        self.sender_nationality.addItems([
            "سعودي", "مصري", "سوري", "أردني", "لبناني", "فلسطيني",
            "عراقي", "يمني", "سوداني", "مغربي", "تونسي", "جزائري",
            "ليبي", "باكستاني", "هندي", "بنغلاديشي", "فلبيني", "أخرى"
        ])
        layout.addWidget(self.sender_nationality, 1, 1)

        # نوع الهوية
        layout.addWidget(QLabel("نوع الهوية:"), 1, 2)
        self.sender_id_type = QComboBox()
        self.sender_id_type.addItems(["هوية وطنية", "إقامة", "جواز سفر", "رخصة قيادة"])
        layout.addWidget(self.sender_id_type, 1, 3)

        # رقم الجوال
        layout.addWidget(QLabel("رقم الجوال:"), 2, 0)
        self.sender_phone = QLineEdit()
        self.sender_phone.setPlaceholderText("05xxxxxxxx")
        layout.addWidget(self.sender_phone, 2, 1)

        # رقم الهاتف الثابت
        layout.addWidget(QLabel("الهاتف الثابت:"), 2, 2)
        self.sender_landline = QLineEdit()
        self.sender_landline.setPlaceholderText("011xxxxxxx")
        layout.addWidget(self.sender_landline, 2, 3)

        # البريد الإلكتروني
        layout.addWidget(QLabel("البريد الإلكتروني:"), 3, 0)
        self.sender_email = QLineEdit()
        self.sender_email.setPlaceholderText("<EMAIL>")
        layout.addWidget(self.sender_email, 3, 1)

        # صندوق البريد
        layout.addWidget(QLabel("صندوق البريد:"), 3, 2)
        self.sender_pobox = QLineEdit()
        self.sender_pobox.setPlaceholderText("ص.ب 12345")
        layout.addWidget(self.sender_pobox, 3, 3)

        # العنوان
        layout.addWidget(QLabel("العنوان:"), 4, 0)
        self.sender_address = QLineEdit()
        self.sender_address.setPlaceholderText("العنوان الكامل")
        layout.addWidget(self.sender_address, 4, 1, 1, 3)

        return group

    def create_receiver_info_group(self):
        """إنشاء مجموعة معلومات المستقبل"""
        group = QGroupBox("🎯 معلومات المستقبل")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # اسم المستقبل
        layout.addWidget(QLabel("الاسم الكامل:"), 0, 0)
        self.receiver_name = QLineEdit()
        self.receiver_name.setPlaceholderText("الاسم الكامل للمستقبل")
        layout.addWidget(self.receiver_name, 0, 1)

        # رقم الهوية
        layout.addWidget(QLabel("رقم الهوية:"), 0, 2)
        self.receiver_id = QLineEdit()
        self.receiver_id.setPlaceholderText("رقم هوية المستقبل")
        layout.addWidget(self.receiver_id, 0, 3)

        # رقم الجوال
        layout.addWidget(QLabel("رقم الجوال:"), 1, 0)
        self.receiver_phone = QLineEdit()
        self.receiver_phone.setPlaceholderText("رقم جوال المستقبل")
        layout.addWidget(self.receiver_phone, 1, 1)

        # رقم الحساب
        layout.addWidget(QLabel("رقم الحساب:"), 1, 2)
        self.receiver_account = QLineEdit()
        self.receiver_account.setPlaceholderText("رقم الحساب البنكي")
        layout.addWidget(self.receiver_account, 1, 3)

        # اسم البنك
        layout.addWidget(QLabel("اسم البنك:"), 2, 0)
        self.receiver_bank = QLineEdit()
        self.receiver_bank.setPlaceholderText("اسم البنك")
        layout.addWidget(self.receiver_bank, 2, 1)

        # فرع البنك
        layout.addWidget(QLabel("فرع البنك:"), 2, 2)
        self.receiver_branch = QLineEdit()
        self.receiver_branch.setPlaceholderText("فرع البنك")
        layout.addWidget(self.receiver_branch, 2, 3)

        # رمز SWIFT
        layout.addWidget(QLabel("رمز SWIFT:"), 3, 0)
        self.receiver_swift = QLineEdit()
        self.receiver_swift.setPlaceholderText("SWIFT Code")
        layout.addWidget(self.receiver_swift, 3, 1)

        # البلد
        layout.addWidget(QLabel("البلد:"), 3, 2)
        self.receiver_country = QComboBox()
        self.receiver_country.addItems([
            "السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان",
            "الأردن", "لبنان", "سوريا", "العراق", "مصر", "المغرب",
            "الجزائر", "تونس", "ليبيا", "السودان", "اليمن", "تركيا",
            "إيران", "باكستان", "الهند", "بنغلاديش", "الفلبين", "إندونيسيا"
        ])
        layout.addWidget(self.receiver_country, 3, 3)

        # المدينة
        layout.addWidget(QLabel("المدينة:"), 4, 0)
        self.receiver_city = QLineEdit()
        self.receiver_city.setPlaceholderText("مدينة المستقبل")
        layout.addWidget(self.receiver_city, 4, 1)

        # بلد البنك
        layout.addWidget(QLabel("بلد البنك:"), 4, 2)
        self.receiver_bank_country = QComboBox()
        self.receiver_bank_country.addItems([
            "السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان",
            "الأردن", "لبنان", "سوريا", "العراق", "مصر", "المغرب",
            "الجزائر", "تونس", "ليبيا", "السودان", "اليمن"
        ])
        layout.addWidget(self.receiver_bank_country, 4, 3)

        # العنوان
        layout.addWidget(QLabel("العنوان:"), 5, 0)
        self.receiver_address = QLineEdit()
        self.receiver_address.setPlaceholderText("عنوان المستقبل الكامل")
        layout.addWidget(self.receiver_address, 5, 1, 1, 3)

        return group

    def create_notes_group(self):
        """إنشاء مجموعة الملاحظات والخيارات"""
        group = QGroupBox("📝 ملاحظات وخيارات إضافية")
        layout = QVBoxLayout(group)

        # الملاحظات
        notes_label = QLabel("الملاحظات:")
        layout.addWidget(notes_label)

        self.notes = QTextEdit()
        self.notes.setPlaceholderText("أي ملاحظات أو تعليمات خاصة...")
        self.notes.setMaximumHeight(80)
        layout.addWidget(self.notes)

        # خيارات الإشعارات والتحويل
        options_layout = QGridLayout()

        # إشعار SMS
        self.sms_notification = QCheckBox("إرسال إشعار SMS للمستقبل")
        self.sms_notification.setChecked(True)
        options_layout.addWidget(self.sms_notification, 0, 0)

        # إشعار بريد إلكتروني
        self.email_notification = QCheckBox("إرسال إشعار بريد إلكتروني")
        self.email_notification.setChecked(False)
        options_layout.addWidget(self.email_notification, 0, 1)

        # إنشاء حوالة تلقائياً
        self.auto_create_remittance = QCheckBox("إنشاء حوالة تلقائياً بعد الموافقة")
        self.auto_create_remittance.setChecked(True)
        options_layout.addWidget(self.auto_create_remittance, 1, 0)

        # طباعة الإيصال
        self.print_receipt = QCheckBox("طباعة إيصال الطلب")
        self.print_receipt.setChecked(False)
        options_layout.addWidget(self.print_receipt, 1, 1)

        layout.addLayout(options_layout)

        return group

    def create_action_buttons(self):
        """إنشاء أزرار الإجراءات"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # زر حفظ
        self.save_btn = QPushButton("💾 حفظ الطلب")
        self.save_btn.setStyleSheet(self.get_button_style("#27ae60", "#2ecc71"))
        layout.addWidget(self.save_btn)
        
        # زر مسح
        self.clear_btn = QPushButton("🗑️ مسح النموذج")
        self.clear_btn.setStyleSheet(self.get_button_style("#f39c12", "#f1c40f"))
        layout.addWidget(self.clear_btn)
        
        # زر إغلاق
        self.close_btn = QPushButton("❌ إغلاق")
        self.close_btn.setStyleSheet(self.get_button_style("#e74c3c", "#ec7063"))
        layout.addWidget(self.close_btn)
        
        layout.addStretch()
        return layout

    def create_requests_list(self):
        """إنشاء قائمة الطلبات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # العنوان
        title_label = QLabel("📋 قائمة الطلبات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #9b59b6, stop:1 #8e44ad);
                color: white;
                border-radius: 8px;
            }
        """)
        layout.addWidget(title_label)

        # جدول الطلبات
        self.requests_table = QTableWidget()
        self.requests_table.setColumnCount(6)
        self.requests_table.setHorizontalHeaderLabels([
            "رقم الطلب", "المرسل", "المستقبل", "المبلغ", "التاريخ", "الحالة"
        ])

        # تنسيق الجدول
        header = self.requests_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.requests_table.setAlternatingRowColors(True)
        self.requests_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.requests_table.verticalHeader().setVisible(False)

        layout.addWidget(self.requests_table)

        # أزرار إدارة الطلبات
        buttons_layout = QHBoxLayout()

        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn)

        self.refresh_btn = QPushButton("🔄 تحديث")
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        return widget

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # أزرار النموذج
        self.save_btn.clicked.connect(self.save_request)
        self.clear_btn.clicked.connect(self.clear_form)
        self.close_btn.clicked.connect(self.close)

        # أزرار قائمة الطلبات
        self.edit_btn.clicked.connect(self.edit_selected_request)
        self.delete_btn.clicked.connect(self.delete_selected_request)
        self.refresh_btn.clicked.connect(self.refresh_requests_list)

        # جدول الطلبات
        self.requests_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.requests_table.itemDoubleClicked.connect(self.edit_selected_request)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        self.init_database()

    def apply_styles(self):
        """تطبيق الأنماط العامة"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
            QLineEdit, QComboBox, QDateEdit, QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QDoubleSpinBox:focus {
                border-color: #3498db;
                background-color: #f0f8ff;
            }
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 8px;
                background-color: white;
                font-size: 12px;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: #f0f8ff;
            }
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def get_button_style(self, color1, color2):
        """إنشاء نمط الأزرار"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color2}, stop:1 {color1});
            }}
            QPushButton:pressed {{
                background: {color2};
            }}
        """

    def generate_request_number(self):
        """إنشاء رقم طلب تلقائي"""
        return f"REQ{datetime.now().strftime('%Y%m%d%H%M%S')}"

    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        try:
            db_path = Path("data/cnx_erp.db")
            db_path.parent.mkdir(exist_ok=True)

            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التحقق من وجود الجدول أولاً
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='remittance_requests'
            """)
            table_exists = cursor.fetchone()

            if not table_exists:
                # إنشاء جدول طلبات الحوالات الجديد مع جميع الحقول
                cursor.execute("""
                    CREATE TABLE remittance_requests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        request_number TEXT UNIQUE NOT NULL,
                        request_date TEXT NOT NULL,
                        branch TEXT,
                        exchanger TEXT,
                        amount REAL NOT NULL,
                        currency TEXT NOT NULL,
                        exchange_rate REAL DEFAULT 1.0,
                        priority TEXT DEFAULT 'عادي',
                        purpose TEXT NOT NULL,
                        sender_name TEXT NOT NULL,
                        sender_id TEXT,
                        sender_nationality TEXT,
                        sender_id_type TEXT,
                        sender_phone TEXT,
                        sender_landline TEXT,
                        sender_email TEXT,
                        sender_pobox TEXT,
                        sender_address TEXT,
                        receiver_name TEXT NOT NULL,
                        receiver_id TEXT,
                        receiver_phone TEXT,
                        receiver_account TEXT,
                        receiver_bank TEXT,
                        receiver_branch TEXT,
                        receiver_swift TEXT,
                        receiver_country TEXT,
                        receiver_city TEXT,
                        receiver_bank_country TEXT,
                        receiver_address TEXT,
                        notes TEXT,
                        sms_notification INTEGER DEFAULT 1,
                        email_notification INTEGER DEFAULT 0,
                        auto_create_remittance INTEGER DEFAULT 1,
                        print_receipt INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'معلق',
                        created_at TEXT NOT NULL,
                        updated_at TEXT
                    )
                """)
            else:
                # التحقق من وجود العمود currency وإضافته إذا لم يكن موجوداً
                cursor.execute("PRAGMA table_info(remittance_requests)")
                columns = [column[1] for column in cursor.fetchall()]

                if 'currency' not in columns:
                    cursor.execute("ALTER TABLE remittance_requests ADD COLUMN currency TEXT DEFAULT 'ريال سعودي'")

                if 'purpose' not in columns:
                    cursor.execute("ALTER TABLE remittance_requests ADD COLUMN purpose TEXT DEFAULT ''")

                if 'sender_id' not in columns:
                    cursor.execute("ALTER TABLE remittance_requests ADD COLUMN sender_id TEXT")

                if 'receiver_country' not in columns:
                    cursor.execute("ALTER TABLE remittance_requests ADD COLUMN receiver_country TEXT DEFAULT 'السعودية'")

                if 'notes' not in columns:
                    cursor.execute("ALTER TABLE remittance_requests ADD COLUMN notes TEXT")

                if 'status' not in columns:
                    cursor.execute("ALTER TABLE remittance_requests ADD COLUMN status TEXT DEFAULT 'معلق'")

                if 'updated_at' not in columns:
                    cursor.execute("ALTER TABLE remittance_requests ADD COLUMN updated_at TEXT")

            conn.commit()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في قاعدة البيانات", f"فشل في إنشاء قاعدة البيانات:\n{str(e)}")

    def save_request(self):
        """حفظ طلب الحوالة"""
        if not self.validate_form():
            return

        try:
            data = self.collect_form_data()

            if self.is_editing and self.current_request_id:
                self.update_request(data)
            else:
                self.create_new_request(data)

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ الطلب:\n{str(e)}")

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if self.amount.value() <= 0:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال مبلغ صحيح")
            self.amount.setFocus()
            return False

        if not self.purpose.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال الغرض من التحويل")
            self.purpose.setFocus()
            return False

        if not self.sender_name.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المرسل")
            self.sender_name.setFocus()
            return False

        if not self.receiver_name.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المستقبل")
            self.receiver_name.setFocus()
            return False

        return True

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        return {
            'request_number': self.request_number.text(),
            'request_date': self.request_date.date().toString('yyyy-MM-dd'),
            'branch': self.branch.currentText(),
            'exchanger': self.exchanger.currentText(),
            'amount': self.amount.value(),
            'currency': self.currency.currentText(),
            'exchange_rate': self.exchange_rate.value(),
            'priority': self.priority.currentText(),
            'purpose': self.purpose.text().strip(),
            'sender_name': self.sender_name.text().strip(),
            'sender_id': self.sender_id.text().strip(),
            'sender_nationality': self.sender_nationality.currentText(),
            'sender_id_type': self.sender_id_type.currentText(),
            'sender_phone': self.sender_phone.text().strip(),
            'sender_landline': self.sender_landline.text().strip(),
            'sender_email': self.sender_email.text().strip(),
            'sender_pobox': self.sender_pobox.text().strip(),
            'sender_address': self.sender_address.text().strip(),
            'receiver_name': self.receiver_name.text().strip(),
            'receiver_id': self.receiver_id.text().strip(),
            'receiver_phone': self.receiver_phone.text().strip(),
            'receiver_account': self.receiver_account.text().strip(),
            'receiver_bank': self.receiver_bank.text().strip(),
            'receiver_branch': self.receiver_branch.text().strip(),
            'receiver_swift': self.receiver_swift.text().strip(),
            'receiver_country': self.receiver_country.currentText(),
            'receiver_city': self.receiver_city.text().strip(),
            'receiver_bank_country': self.receiver_bank_country.currentText(),
            'receiver_address': self.receiver_address.text().strip(),
            'notes': self.notes.toPlainText().strip(),
            'sms_notification': 1 if self.sms_notification.isChecked() else 0,
            'email_notification': 1 if self.email_notification.isChecked() else 0,
            'auto_create_remittance': 1 if self.auto_create_remittance.isChecked() else 0,
            'print_receipt': 1 if self.print_receipt.isChecked() else 0,
            'status': 'معلق',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

    def create_new_request(self, data):
        """إنشاء طلب جديد"""
        try:
            db_path = Path("data/cnx_erp.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO remittance_requests (
                    request_number, request_date, branch, exchanger, amount, currency,
                    exchange_rate, priority, purpose, sender_name, sender_id, sender_nationality,
                    sender_id_type, sender_phone, sender_landline, sender_email, sender_pobox,
                    sender_address, receiver_name, receiver_id, receiver_phone, receiver_account,
                    receiver_bank, receiver_branch, receiver_swift, receiver_country, receiver_city,
                    receiver_bank_country, receiver_address, notes, sms_notification,
                    email_notification, auto_create_remittance, print_receipt, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['request_number'], data['request_date'], data['branch'], data['exchanger'],
                data['amount'], data['currency'], data['exchange_rate'], data['priority'],
                data['purpose'], data['sender_name'], data['sender_id'], data['sender_nationality'],
                data['sender_id_type'], data['sender_phone'], data['sender_landline'],
                data['sender_email'], data['sender_pobox'], data['sender_address'],
                data['receiver_name'], data['receiver_id'], data['receiver_phone'],
                data['receiver_account'], data['receiver_bank'], data['receiver_branch'],
                data['receiver_swift'], data['receiver_country'], data['receiver_city'],
                data['receiver_bank_country'], data['receiver_address'], data['notes'],
                data['sms_notification'], data['email_notification'], data['auto_create_remittance'],
                data['print_receipt'], data['status'], data['created_at']
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "تم الحفظ", f"تم حفظ طلب الحوالة بنجاح!\nرقم الطلب: {data['request_number']}")

            # إرسال إشارة
            self.request_created.emit(data)

            # مسح النموذج وتحديث القائمة
            self.clear_form()
            self.refresh_requests_list()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ الطلب:\n{str(e)}")

    def update_request(self, data):
        """تحديث طلب موجود"""
        try:
            db_path = Path("data/cnx_erp.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE remittance_requests SET
                    request_date=?, amount=?, currency=?, purpose=?,
                    sender_name=?, sender_id=?, sender_phone=?, sender_email=?, sender_address=?,
                    receiver_name=?, receiver_account=?, receiver_bank=?, receiver_branch=?,
                    receiver_country=?, receiver_address=?, notes=?, updated_at=?
                WHERE id=?
            """, (
                data['request_date'], data['amount'], data['currency'], data['purpose'],
                data['sender_name'], data['sender_id'], data['sender_phone'],
                data['sender_email'], data['sender_address'], data['receiver_name'],
                data['receiver_account'], data['receiver_bank'], data['receiver_branch'],
                data['receiver_country'], data['receiver_address'], data['notes'],
                data['updated_at'], self.current_request_id
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "تم التحديث", "تم تحديث طلب الحوالة بنجاح!")

            # إرسال إشارة
            data['id'] = self.current_request_id
            self.request_updated.emit(data)

            # إعادة تعيين الوضع وتحديث القائمة
            self.reset_form_mode()
            self.refresh_requests_list()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التحديث", f"فشل في تحديث الطلب:\n{str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        # البيانات الأساسية
        self.request_number.setText(self.generate_request_number())
        self.request_date.setDate(QDate.currentDate())
        self.branch.setCurrentIndex(0)
        self.exchanger.setCurrentIndex(0)
        self.amount.setValue(0)
        self.currency.setCurrentIndex(0)
        self.exchange_rate.setValue(1.0000)
        self.priority.setCurrentIndex(0)
        self.purpose.clear()

        # معلومات المرسل
        self.sender_name.clear()
        self.sender_id.clear()
        self.sender_nationality.setCurrentIndex(0)
        self.sender_id_type.setCurrentIndex(0)
        self.sender_phone.clear()
        self.sender_landline.clear()
        self.sender_email.clear()
        self.sender_pobox.clear()
        self.sender_address.clear()

        # معلومات المستقبل
        self.receiver_name.clear()
        self.receiver_id.clear()
        self.receiver_phone.clear()
        self.receiver_account.clear()
        self.receiver_bank.clear()
        self.receiver_branch.clear()
        self.receiver_swift.clear()
        self.receiver_country.setCurrentIndex(0)
        self.receiver_city.clear()
        self.receiver_bank_country.setCurrentIndex(0)
        self.receiver_address.clear()

        # الملاحظات والخيارات
        self.notes.clear()
        self.sms_notification.setChecked(True)
        self.email_notification.setChecked(False)
        self.auto_create_remittance.setChecked(True)
        self.print_receipt.setChecked(False)

        self.reset_form_mode()

    def reset_form_mode(self):
        """إعادة تعيين وضع النموذج"""
        self.is_editing = False
        self.current_request_id = None
        self.save_btn.setText("💾 حفظ الطلب")

    def refresh_requests_list(self):
        """تحديث قائمة الطلبات"""
        try:
            db_path = Path("data/cnx_erp.db")
            if not db_path.exists():
                return

            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التحقق من الأعمدة الموجودة أولاً
            cursor.execute("PRAGMA table_info(remittance_requests)")
            columns_info = cursor.fetchall()
            available_columns = [col[1] for col in columns_info]

            # بناء الاستعلام بناءً على الأعمدة المتاحة
            base_columns = ["id", "request_number", "sender_name", "receiver_name", "amount", "request_date"]
            optional_columns = {
                "currency": "ريال سعودي",
                "status": "معلق"
            }

            select_columns = []
            for col in base_columns:
                if col in available_columns:
                    select_columns.append(col)
                else:
                    select_columns.append(f"'' as {col}")

            for col, default_val in optional_columns.items():
                if col in available_columns:
                    select_columns.append(col)
                else:
                    select_columns.append(f"'{default_val}' as {col}")

            query = f"""
                SELECT {', '.join(select_columns)}
                FROM remittance_requests
                ORDER BY {"created_at" if "created_at" in available_columns else "id"} DESC
            """

            cursor.execute(query)
            requests = cursor.fetchall()
            conn.close()

            # تحديث الجدول
            self.requests_table.setRowCount(len(requests))

            for row, request in enumerate(requests):
                # التعامل مع البيانات بحذر
                request_number = str(request[1]) if request[1] else f"REQ{row+1:04d}"
                sender_name = str(request[2]) if request[2] else "غير محدد"
                receiver_name = str(request[3]) if request[3] else "غير محدد"
                amount = float(request[4]) if request[4] else 0.0
                request_date = str(request[5]) if request[5] else datetime.now().strftime('%Y-%m-%d')
                currency = str(request[6]) if len(request) > 6 and request[6] else "ريال سعودي"
                status = str(request[7]) if len(request) > 7 and request[7] else "معلق"

                self.requests_table.setItem(row, 0, QTableWidgetItem(request_number))
                self.requests_table.setItem(row, 1, QTableWidgetItem(sender_name))
                self.requests_table.setItem(row, 2, QTableWidgetItem(receiver_name))
                self.requests_table.setItem(row, 3, QTableWidgetItem(f"{amount:.2f} {currency}"))
                self.requests_table.setItem(row, 4, QTableWidgetItem(request_date))
                self.requests_table.setItem(row, 5, QTableWidgetItem(status))

                # حفظ ID في البيانات المخفية
                self.requests_table.item(row, 0).setData(Qt.UserRole, request[0])

        except Exception as e:
            print(f"خطأ في تحديث قائمة الطلبات: {e}")
            # إنشاء جدول فارغ في حالة الخطأ
            self.requests_table.setRowCount(0)

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول"""
        selected_items = self.requests_table.selectedItems()
        has_selection = len(selected_items) > 0

        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def edit_selected_request(self):
        """تعديل الطلب المحدد"""
        current_row = self.requests_table.currentRow()
        if current_row < 0:
            return

        request_id = self.requests_table.item(current_row, 0).data(Qt.UserRole)
        if not request_id:
            return

        self.load_request_for_editing(request_id)

    def load_request_for_editing(self, request_id):
        """تحميل طلب للتعديل"""
        try:
            db_path = Path("data/cnx_erp.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # الحصول على معلومات الأعمدة
            cursor.execute("PRAGMA table_info(remittance_requests)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]

            cursor.execute("SELECT * FROM remittance_requests WHERE id=?", (request_id,))
            request = cursor.fetchone()
            conn.close()

            if not request:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الطلب")
                return

            # إنشاء قاموس للبيانات
            data = {}
            for i, col_name in enumerate(column_names):
                data[col_name] = request[i] if i < len(request) else None

            # تحميل البيانات في النموذج بأمان
            self.request_number.setText(data.get('request_number', ''))

            # التاريخ
            date_str = data.get('request_date', '')
            if date_str:
                try:
                    self.request_date.setDate(QDate.fromString(date_str, 'yyyy-MM-dd'))
                except:
                    self.request_date.setDate(QDate.currentDate())
            else:
                self.request_date.setDate(QDate.currentDate())

            # المبلغ
            amount = data.get('amount', 0)
            self.amount.setValue(float(amount) if amount else 0.0)

            # العملة
            currency = data.get('currency', 'ريال سعودي')
            if currency:
                index = self.currency.findText(currency)
                if index >= 0:
                    self.currency.setCurrentIndex(index)

            # باقي البيانات
            self.purpose.setText(data.get('purpose', '') or "")
            self.sender_name.setText(data.get('sender_name', '') or "")
            self.sender_id.setText(data.get('sender_id', '') or "")
            self.sender_phone.setText(data.get('sender_phone', '') or "")
            self.sender_email.setText(data.get('sender_email', '') or "")
            self.sender_address.setText(data.get('sender_address', '') or "")
            self.receiver_name.setText(data.get('receiver_name', '') or "")
            self.receiver_account.setText(data.get('receiver_account', '') or "")
            self.receiver_bank.setText(data.get('receiver_bank', '') or "")
            self.receiver_branch.setText(data.get('receiver_branch', '') or "")

            # البلد
            country = data.get('receiver_country', 'السعودية')
            if country:
                index = self.receiver_country.findText(country)
                if index >= 0:
                    self.receiver_country.setCurrentIndex(index)

            self.receiver_address.setText(data.get('receiver_address', '') or "")
            self.notes.setPlainText(data.get('notes', '') or "")

            # تعيين وضع التعديل
            self.is_editing = True
            self.current_request_id = request_id
            self.save_btn.setText("💾 تحديث الطلب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التحميل", f"فشل في تحميل الطلب:\n{str(e)}")

    def delete_selected_request(self):
        """حذف الطلب المحدد"""
        current_row = self.requests_table.currentRow()
        if current_row < 0:
            return

        request_id = self.requests_table.item(current_row, 0).data(Qt.UserRole)
        request_number = self.requests_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف طلب الحوالة رقم {request_number}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                db_path = Path("data/cnx_erp.db")
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                cursor.execute("DELETE FROM remittance_requests WHERE id=?", (request_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", "تم حذف طلب الحوالة بنجاح")
                self.refresh_requests_list()

            except Exception as e:
                QMessageBox.critical(self, "خطأ في الحذف", f"فشل في حذف الطلب:\n{str(e)}")
