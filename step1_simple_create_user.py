#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء المستخدم ship2025 بطريقة مبسطة
Simple user creation for ship2025
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def create_user_with_sql_script():
    """إنشاء المستخدم باستخدام سكريبت SQL"""
    print("📝 إنشاء سكريبت SQL لإنشاء المستخدم...")
    
    sql_script = """
-- سكريبت إنشاء المستخدم ship2025
-- يجب تشغيله بصلاحيات DBA

-- حذف المستخدم إذا كان موجوداً
BEGIN
    EXECUTE IMMEDIATE 'DROP USER ship2025 CASCADE';
    DBMS_OUTPUT.PUT_LINE('تم حذف المستخدم الموجود');
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('المستخدم غير موجود');
END;
/

-- إنشاء المستخدم الجديد
CREATE USER ship2025 IDENTIFIED BY ys123;

-- منح الصلاحيات الأساسية
GRANT CONNECT TO ship2025;
GRANT RESOURCE TO ship2025;
GRANT CREATE SESSION TO ship2025;

-- صلاحيات الكائنات
GRANT CREATE TABLE TO ship2025;
GRANT CREATE VIEW TO ship2025;
GRANT CREATE SEQUENCE TO ship2025;
GRANT CREATE PROCEDURE TO ship2025;
GRANT CREATE FUNCTION TO ship2025;
GRANT CREATE PACKAGE TO ship2025;
GRANT CREATE TRIGGER TO ship2025;
GRANT CREATE SYNONYM TO ship2025;

-- صلاحيات متقدمة
GRANT CREATE ANY TABLE TO ship2025;
GRANT ALTER ANY TABLE TO ship2025;
GRANT DROP ANY TABLE TO ship2025;
GRANT SELECT ANY TABLE TO ship2025;
GRANT INSERT ANY TABLE TO ship2025;
GRANT UPDATE ANY TABLE TO ship2025;
GRANT DELETE ANY TABLE TO ship2025;

-- صلاحيات الفهارس
GRANT CREATE ANY INDEX TO ship2025;
GRANT ALTER ANY INDEX TO ship2025;
GRANT DROP ANY INDEX TO ship2025;

-- صلاحيات المتسلسلات
GRANT CREATE ANY SEQUENCE TO ship2025;
GRANT ALTER ANY SEQUENCE TO ship2025;
GRANT DROP ANY SEQUENCE TO ship2025;
GRANT SELECT ANY SEQUENCE TO ship2025;

-- إعدادات التخزين
ALTER USER ship2025 DEFAULT TABLESPACE USERS;
ALTER USER ship2025 TEMPORARY TABLESPACE TEMP;
ALTER USER ship2025 QUOTA UNLIMITED ON USERS;

-- صلاحيات إضافية
GRANT SELECT ANY DICTIONARY TO ship2025;
GRANT EXECUTE ANY PROCEDURE TO ship2025;

-- منح صلاحيات للوصول لجداول ias20241
GRANT SELECT ON ias20241.* TO ship2025;

COMMIT;

-- رسالة تأكيد
SELECT 'تم إنشاء المستخدم ship2025 بنجاح!' AS STATUS FROM DUAL;
"""
    
    # حفظ السكريبت
    script_file = Path("create_user_ship2025.sql")
    with open(script_file, 'w', encoding='utf-8') as f:
        f.write(sql_script)
    
    print(f"✅ تم إنشاء السكريبت: {script_file}")
    print("\n📋 لإنشاء المستخدم، قم بتشغيل الأمر التالي في SQL*Plus أو SQL Developer:")
    print(f"   sqlplus system/oracle@yemensoft @{script_file}")
    print("\nأو:")
    print("   sqlplus sys/oracle@yemensoft as sysdba")
    print(f"   SQL> @{script_file}")
    
    return script_file


def test_connection_ias20241():
    """اختبار الاتصال بـ ias20241"""
    print("\n🧪 اختبار الاتصال بـ ias20241...")
    
    setup_environment()
    
    try:
        conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        cursor = conn.cursor()
        
        cursor.execute("SELECT USER, SYSDATE FROM DUAL")
        user, sysdate = cursor.fetchone()
        
        print(f"✅ تم الاتصال بـ {user}")
        print(f"   🕐 الوقت: {sysdate}")
        
        # فحص الجداول
        cursor.execute("SELECT COUNT(*) FROM user_tables")
        table_count = cursor.fetchone()[0]
        print(f"   📊 عدد الجداول: {table_count}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاتصال: {e}")
        return False


def test_connection_ship2025():
    """اختبار الاتصال بـ ship2025"""
    print("\n🧪 اختبار الاتصال بـ ship2025...")
    
    setup_environment()
    
    try:
        conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        cursor = conn.cursor()
        
        cursor.execute("SELECT USER, SYSDATE FROM DUAL")
        user, sysdate = cursor.fetchone()
        
        print(f"✅ تم الاتصال بـ {user}")
        print(f"   🕐 الوقت: {sysdate}")
        
        # فحص الجداول
        cursor.execute("SELECT COUNT(*) FROM user_tables")
        table_count = cursor.fetchone()[0]
        print(f"   📊 عدد الجداول: {table_count}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاتصال: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء المستخدم ship2025")
    print("=" * 50)
    
    # اختبار الاتصال بـ ias20241
    if not test_connection_ias20241():
        print("💥 فشل في الاتصال بـ ias20241")
        return False
    
    # إنشاء سكريبت SQL
    script_file = create_user_with_sql_script()
    
    # اختبار الاتصال بـ ship2025
    print("\n" + "="*50)
    print("⚠️ يرجى تشغيل السكريبت أولاً ثم الضغط على Enter للمتابعة...")
    input("اضغط Enter بعد تشغيل السكريبت...")
    
    if test_connection_ship2025():
        print("\n🎉 تم إنشاء المستخدم ship2025 بنجاح!")
        return True
    else:
        print("\n❌ المستخدم ship2025 غير موجود أو لا يمكن الاتصال به")
        print("💡 تأكد من تشغيل السكريبت بصلاحيات DBA")
        return False


if __name__ == "__main__":
    main()
