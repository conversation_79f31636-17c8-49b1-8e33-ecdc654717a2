#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 3: نسخ الجداول والبنية
Step 3: Copy tables and structure
"""

import os
import cx_Oracle
import json
from pathlib import Path
from datetime import datetime


class TableStructureCopier:
    """ناسخ بنية الجداول"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        self.source_cursor = None
        self.target_cursor = None
        
        # تحميل بيانات الفحص
        self.inspection_data = self.load_inspection_data()
        
        # إحصائيات النسخ
        self.copy_stats = {
            'tables_created': 0,
            'tables_failed': 0,
            'start_time': None,
            'end_time': None,
            'failed_tables': []
        }
    
    def load_inspection_data(self):
        """تحميل بيانات الفحص"""
        try:
            # البحث عن أحدث ملف فحص
            inspection_dir = Path("inspection_results")
            if inspection_dir.exists():
                json_files = list(inspection_dir.glob("ias20241_complete_inspection_*.json"))
                if json_files:
                    latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"✅ تم تحميل بيانات الفحص من: {latest_file}")
                    return data
            
            print("⚠️ لم يتم العثور على بيانات الفحص")
            return None
            
        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الفحص: {e}")
            return None
    
    def connect_databases(self):
        """الاتصال بقواعد البيانات"""
        try:
            # الاتصال بالمصدر
            print("🔌 الاتصال بالمصدر ias20241...")
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.source_cursor = self.source_conn.cursor()
            
            # الاتصال بالهدف
            print("🔌 الاتصال بالهدف ship2025...")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            self.target_cursor = self.target_conn.cursor()
            
            print("✅ تم الاتصال بقواعد البيانات")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال بقواعد البيانات: {e}")
            return False
    
    def generate_table_ddl(self, table_name):
        """إنشاء DDL للجدول"""
        try:
            if not self.inspection_data or table_name not in self.inspection_data['tables']:
                # الحصول على DDL مباشرة من قاعدة البيانات
                return self.get_table_ddl_from_db(table_name)
            
            table_info = self.inspection_data['tables'][table_name]
            columns = table_info.get('columns', [])
            
            if not columns:
                return self.get_table_ddl_from_db(table_name)
            
            # بناء DDL
            ddl_lines = [f"CREATE TABLE {table_name} ("]
            
            for i, col in enumerate(columns):
                col_name = col['name']
                data_type = col['data_type']
                
                # تنسيق نوع البيانات
                if data_type == 'NUMBER':
                    if col.get('data_precision') and col.get('data_scale'):
                        col_type = f"NUMBER({col['data_precision']},{col['data_scale']})"
                    elif col.get('data_precision'):
                        col_type = f"NUMBER({col['data_precision']})"
                    else:
                        col_type = "NUMBER"
                elif data_type == 'VARCHAR2':
                    col_type = f"VARCHAR2({col.get('data_length', 100)})"
                elif data_type == 'CHAR':
                    col_type = f"CHAR({col.get('data_length', 1)})"
                elif data_type in ['DATE', 'CLOB', 'BLOB', 'LONG']:
                    col_type = data_type
                else:
                    col_type = data_type
                
                # إضافة DEFAULT إذا وجد
                default_clause = ""
                if col.get('data_default'):
                    default_clause = f" DEFAULT {col['data_default']}"
                
                # إضافة NOT NULL إذا لزم الأمر
                null_clause = " NOT NULL" if col.get('nullable') == 'N' else ""
                
                # إضافة فاصلة إذا لم يكن آخر عمود
                comma = "," if i < len(columns) - 1 else ""
                
                ddl_lines.append(f"    {col_name} {col_type}{default_clause}{null_clause}{comma}")
            
            ddl_lines.append(")")
            
            # إضافة خصائص الجدول
            table_props = []
            if table_info.get('tablespace_name'):
                table_props.append(f"TABLESPACE {table_info['tablespace_name']}")
            
            if table_props:
                ddl_lines.append(" " + " ".join(table_props))
            
            return "\n".join(ddl_lines)
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء DDL للجدول {table_name}: {e}")
            return self.get_table_ddl_from_db(table_name)
    
    def get_table_ddl_from_db(self, table_name):
        """الحصول على DDL مباشرة من قاعدة البيانات"""
        try:
            # الحصول على أعمدة الجدول
            self.source_cursor.execute("""
                SELECT column_name, data_type, data_length, data_precision, 
                       data_scale, nullable, data_default
                FROM user_tab_columns
                WHERE table_name = :table_name
                ORDER BY column_id
            """, {'table_name': table_name})
            
            columns = self.source_cursor.fetchall()
            
            if not columns:
                return None
            
            # بناء DDL
            ddl_lines = [f"CREATE TABLE {table_name} ("]
            
            for i, col in enumerate(columns):
                col_name, data_type, data_length, data_precision, data_scale, nullable, data_default = col
                
                # تنسيق نوع البيانات
                if data_type == 'NUMBER':
                    if data_precision and data_scale:
                        col_type = f"NUMBER({data_precision},{data_scale})"
                    elif data_precision:
                        col_type = f"NUMBER({data_precision})"
                    else:
                        col_type = "NUMBER"
                elif data_type == 'VARCHAR2':
                    col_type = f"VARCHAR2({data_length})"
                elif data_type == 'CHAR':
                    col_type = f"CHAR({data_length})"
                elif data_type in ['DATE', 'CLOB', 'BLOB', 'LONG']:
                    col_type = data_type
                else:
                    col_type = data_type
                
                # إضافة DEFAULT إذا وجد
                default_clause = f" DEFAULT {data_default}" if data_default else ""
                
                # إضافة NOT NULL إذا لزم الأمر
                null_clause = " NOT NULL" if nullable == 'N' else ""
                
                # إضافة فاصلة إذا لم يكن آخر عمود
                comma = "," if i < len(columns) - 1 else ""
                
                ddl_lines.append(f"    {col_name} {col_type}{default_clause}{null_clause}{comma}")
            
            ddl_lines.append(")")
            
            return "\n".join(ddl_lines)
            
        except Exception as e:
            print(f"   ❌ خطأ في الحصول على DDL من قاعدة البيانات للجدول {table_name}: {e}")
            return None
    
    def create_table(self, table_name, ddl):
        """إنشاء جدول في الهدف"""
        try:
            # التحقق من وجود الجدول
            self.target_cursor.execute("""
                SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
            """, {'table_name': table_name})
            
            if self.target_cursor.fetchone()[0] > 0:
                print(f"   ⚠️ الجدول {table_name} موجود بالفعل - سيتم تخطيه")
                return True
            
            # إنشاء الجدول
            self.target_cursor.execute(ddl)
            self.target_conn.commit()
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الجدول {table_name}: {e}")
            return False
    
    def copy_table_structure(self, table_name):
        """نسخ بنية جدول واحد"""
        try:
            print(f"📋 نسخ بنية جدول: {table_name}")
            
            # إنشاء DDL
            ddl = self.generate_table_ddl(table_name)
            if not ddl:
                print(f"   ❌ فشل في إنشاء DDL")
                return False
            
            # إنشاء الجدول
            if self.create_table(table_name, ddl):
                print(f"   ✅ تم إنشاء الجدول {table_name}")
                return True
            else:
                print(f"   ❌ فشل في إنشاء الجدول {table_name}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في نسخ بنية الجدول {table_name}: {e}")
            return False
    
    def get_tables_list(self):
        """الحصول على قائمة الجداول"""
        try:
            if self.inspection_data and 'tables' in self.inspection_data:
                tables = list(self.inspection_data['tables'].keys())
                print(f"📊 تم تحميل {len(tables)} جدول من بيانات الفحص")
                return tables
            
            # الحصول من قاعدة البيانات مباشرة
            self.source_cursor.execute("""
                SELECT table_name FROM user_tables 
                WHERE table_name NOT LIKE 'BIN$%'
                ORDER BY table_name
            """)
            
            tables = [row[0] for row in self.source_cursor.fetchall()]
            print(f"📊 تم الحصول على {len(tables)} جدول من قاعدة البيانات")
            return tables
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على قائمة الجداول: {e}")
            return []
    
    def copy_all_tables_structure(self, batch_size=50):
        """نسخ بنية جميع الجداول"""
        print("🚀 بدء نسخ بنية الجداول")
        print("=" * 60)
        
        self.copy_stats['start_time'] = datetime.now()
        
        # الحصول على قائمة الجداول
        tables = self.get_tables_list()
        if not tables:
            print("❌ لم يتم العثور على جداول للنسخ")
            return False
        
        print(f"📊 سيتم نسخ {len(tables)} جدول")
        
        # نسخ الجداول على دفعات
        for i in range(0, len(tables), batch_size):
            batch = tables[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(tables) + batch_size - 1) // batch_size
            
            print(f"\n📦 الدفعة {batch_num}/{total_batches} ({len(batch)} جدول)")
            print("-" * 40)
            
            for j, table_name in enumerate(batch, 1):
                print(f"[{i + j:4d}/{len(tables)}] ", end="")
                
                if self.copy_table_structure(table_name):
                    self.copy_stats['tables_created'] += 1
                else:
                    self.copy_stats['tables_failed'] += 1
                    self.copy_stats['failed_tables'].append(table_name)
            
            # عرض تقدم الدفعة
            print(f"✅ تمت الدفعة {batch_num}: {self.copy_stats['tables_created']} نجح، {self.copy_stats['tables_failed']} فشل")
        
        self.copy_stats['end_time'] = datetime.now()
        
        # النتائج النهائية
        duration = self.copy_stats['end_time'] - self.copy_stats['start_time']
        
        print("\n" + "=" * 60)
        print("🎉 تم إكمال نسخ بنية الجداول!")
        print(f"📊 الجداول المنشأة: {self.copy_stats['tables_created']}")
        print(f"❌ الجداول الفاشلة: {self.copy_stats['tables_failed']}")
        print(f"⏱️ المدة: {duration}")
        
        if self.copy_stats['failed_tables']:
            print(f"\n❌ الجداول الفاشلة:")
            for table in self.copy_stats['failed_tables'][:10]:  # أول 10
                print(f"   - {table}")
            if len(self.copy_stats['failed_tables']) > 10:
                print(f"   ... و {len(self.copy_stats['failed_tables']) - 10} جدول آخر")
        
        print("=" * 60)
        
        return self.copy_stats['tables_created'] > 0
    
    def verify_copied_tables(self):
        """التحقق من الجداول المنسوخة"""
        print("\n🔍 التحقق من الجداول المنسوخة...")
        
        try:
            # عد الجداول في الهدف
            self.target_cursor.execute("SELECT COUNT(*) FROM user_tables")
            target_count = self.target_cursor.fetchone()[0]
            
            # عد الجداول في المصدر
            self.source_cursor.execute("SELECT COUNT(*) FROM user_tables WHERE table_name NOT LIKE 'BIN$%'")
            source_count = self.source_cursor.fetchone()[0]
            
            print(f"📊 الجداول في المصدر: {source_count}")
            print(f"📊 الجداول في الهدف: {target_count}")
            print(f"📊 نسبة النجاح: {(target_count/source_count)*100:.1f}%")
            
            return target_count > 0
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_copy_process(self):
        """تشغيل عملية النسخ الكاملة"""
        try:
            # الاتصال بقواعد البيانات
            if not self.connect_databases():
                return False
            
            # نسخ بنية الجداول
            success = self.copy_all_tables_structure()
            
            if success:
                # التحقق من النتائج
                self.verify_copied_tables()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في عملية النسخ: {e}")
            return False
            
        finally:
            # إغلاق الاتصالات
            if self.source_cursor:
                self.source_cursor.close()
            if self.target_cursor:
                self.target_cursor.close()
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = TableStructureCopier()
    success = copier.run_copy_process()
    
    if success:
        print("\n✅ الخطوة 3 مكتملة - تم نسخ بنية الجداول")
    else:
        print("\n❌ فشل في الخطوة 3")
    
    return success


if __name__ == "__main__":
    main()
