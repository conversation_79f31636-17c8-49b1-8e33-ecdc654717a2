#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ محسن للفهارس
Enhanced indexes copy
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class EnhancedIndexCopier:
    """ناسخ محسن للفهارس"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'normal_indexes': 0,
            'unique_indexes': 0,
            'bitmap_indexes': 0,
            'function_indexes': 0,
            'failed_indexes': 0,
            'skipped_indexes': 0,
            'failed_list': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def copy_missing_indexes(self):
        """نسخ الفهارس المفقودة فقط"""
        print("\n🔍 البحث عن الفهارس المفقودة...")
        
        try:
            source_cursor = self.source_conn.cursor()
            target_cursor = self.target_conn.cursor()
            
            # الحصول على الفهارس في المصدر
            source_cursor.execute("""
                SELECT i.index_name, i.table_name, i.index_type, i.uniqueness
                FROM user_indexes i
                WHERE i.table_name NOT LIKE 'BIN$%'
                AND i.index_name NOT LIKE 'SYS_%'
                AND i.generated = 'N'
                AND i.index_name NOT IN (
                    SELECT c.index_name FROM user_constraints c 
                    WHERE c.index_name IS NOT NULL
                    AND c.constraint_type IN ('P', 'U')
                )
                ORDER BY i.table_name, i.index_name
            """)
            
            source_indexes = source_cursor.fetchall()
            print(f"   📊 المصدر: {len(source_indexes)} فهرس")
            
            # الحصول على الفهارس في الهدف
            target_cursor.execute("""
                SELECT index_name
                FROM user_indexes
                WHERE table_name NOT LIKE 'BIN$%'
                AND index_name NOT LIKE 'SYS_%'
                AND generated = 'N'
            """)
            
            target_indexes = {row[0] for row in target_cursor.fetchall()}
            print(f"   📊 الهدف: {len(target_indexes)} فهرس")
            
            # العثور على الفهارس المفقودة
            missing_indexes = []
            for idx_name, table_name, idx_type, uniqueness in source_indexes:
                if idx_name not in target_indexes:
                    missing_indexes.append((idx_name, table_name, idx_type, uniqueness))
            
            print(f"   📊 المفقود: {len(missing_indexes)} فهرس")
            
            if not missing_indexes:
                print("   ✅ جميع الفهارس موجودة!")
                return True
            
            # نسخ الفهارس المفقودة
            print(f"\n🚀 نسخ {len(missing_indexes)} فهرس مفقود...")
            
            created_count = 0
            failed_count = 0
            
            for i, (idx_name, table_name, idx_type, uniqueness) in enumerate(missing_indexes, 1):
                try:
                    # التحقق من وجود الجدول
                    target_cursor.execute("""
                        SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
                    """, {'table_name': table_name})
                    
                    if target_cursor.fetchone()[0] == 0:
                        print(f"[{i:4d}/{len(missing_indexes)}] ⚠️ الجدول {table_name} غير موجود - تخطي {idx_name}")
                        continue
                    
                    # الحصول على أعمدة الفهرس
                    source_cursor.execute("""
                        SELECT column_name, descend
                        FROM user_ind_columns
                        WHERE index_name = :index_name
                        ORDER BY column_position
                    """, {'index_name': idx_name})
                    
                    columns = source_cursor.fetchall()
                    
                    if not columns:
                        print(f"[{i:4d}/{len(missing_indexes)}] ⚠️ لا توجد أعمدة للفهرس {idx_name}")
                        continue
                    
                    # بناء DDL
                    ddl = "CREATE "
                    
                    if uniqueness == 'UNIQUE':
                        ddl += "UNIQUE "
                    elif idx_type == 'BITMAP':
                        ddl += "BITMAP "
                    
                    ddl += f"INDEX {idx_name} ON {table_name} ("
                    
                    column_parts = []
                    for col_name, descend in columns:
                        if col_name:
                            col_part = col_name
                            if descend == 'DESC':
                                col_part += " DESC"
                            column_parts.append(col_part)
                    
                    if not column_parts:
                        print(f"[{i:4d}/{len(missing_indexes)}] ⚠️ لا توجد أعمدة صالحة للفهرس {idx_name}")
                        continue
                    
                    ddl += ", ".join(column_parts) + ")"
                    
                    # إنشاء الفهرس
                    target_cursor.execute(ddl)
                    
                    # تحديد نوع الفهرس للعرض
                    if uniqueness == 'UNIQUE':
                        type_display = "فهرس فريد"
                        self.stats['unique_indexes'] += 1
                    elif idx_type == 'BITMAP':
                        type_display = "فهرس بت ماب"
                        self.stats['bitmap_indexes'] += 1
                    elif idx_type == 'FUNCTION-BASED NORMAL':
                        type_display = "فهرس وظيفي"
                        self.stats['function_indexes'] += 1
                    else:
                        type_display = "فهرس عادي"
                        self.stats['normal_indexes'] += 1
                    
                    print(f"[{i:4d}/{len(missing_indexes)}] ✅ تم إنشاء {type_display}: {idx_name} على {table_name}")
                    created_count += 1
                    
                except Exception as e:
                    print(f"[{i:4d}/{len(missing_indexes)}] ❌ فشل في إنشاء {idx_name}: {e}")
                    failed_count += 1
                    self.stats['failed_indexes'] += 1
                    self.stats['failed_list'].append(idx_name)
            
            print(f"\n📊 النتائج: {created_count} نجح، {failed_count} فشل")
            
            source_cursor.close()
            target_cursor.close()
            
            return created_count > 0
            
        except Exception as e:
            print(f"❌ خطأ في نسخ الفهارس: {e}")
            return False
    
    def verify_indexes(self):
        """التحقق من الفهارس المنسوخة"""
        print("\n🔍 التحقق من الفهارس المنسوخة...")
        
        try:
            # عد الفهارس في الهدف
            target_cursor = self.target_conn.cursor()
            target_cursor.execute("""
                SELECT 
                    CASE 
                        WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                        WHEN index_type = 'BITMAP' THEN 'BITMAP'
                        WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                        ELSE 'NORMAL'
                    END as index_category,
                    COUNT(*) 
                FROM user_indexes 
                WHERE table_name NOT LIKE 'BIN$%'
                AND index_name NOT LIKE 'SYS_%'
                AND generated = 'N'
                GROUP BY 
                    CASE 
                        WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                        WHEN index_type = 'BITMAP' THEN 'BITMAP'
                        WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                        ELSE 'NORMAL'
                    END
                ORDER BY 1
            """)
            target_counts = dict(target_cursor.fetchall())
            
            # عد الفهارس في المصدر
            source_cursor = self.source_conn.cursor()
            source_cursor.execute("""
                SELECT 
                    CASE 
                        WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                        WHEN index_type = 'BITMAP' THEN 'BITMAP'
                        WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                        ELSE 'NORMAL'
                    END as index_category,
                    COUNT(*) 
                FROM user_indexes 
                WHERE table_name NOT LIKE 'BIN$%'
                AND index_name NOT LIKE 'SYS_%'
                AND generated = 'N'
                AND index_name NOT IN (
                    SELECT c.index_name FROM user_constraints c 
                    WHERE c.index_name IS NOT NULL
                    AND c.constraint_type IN ('P', 'U')
                )
                GROUP BY 
                    CASE 
                        WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                        WHEN index_type = 'BITMAP' THEN 'BITMAP'
                        WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                        ELSE 'NORMAL'
                    END
                ORDER BY 1
            """)
            source_counts = dict(source_cursor.fetchall())
            
            type_names = {
                'NORMAL': 'فهارس عادية',
                'UNIQUE': 'فهارس فريدة', 
                'BITMAP': 'فهارس بت ماب',
                'FUNCTION': 'فهارس وظيفية'
            }
            
            print("📊 مقارنة الفهارس:")
            for idx_type in ['NORMAL', 'UNIQUE', 'BITMAP', 'FUNCTION']:
                source_count = source_counts.get(idx_type, 0)
                target_count = target_counts.get(idx_type, 0)
                percentage = (target_count / source_count * 100) if source_count > 0 else 0
                
                print(f"   {type_names[idx_type]}: {target_count}/{source_count} ({percentage:.1f}%)")
            
            target_cursor.close()
            source_cursor.close()
            
            return sum(target_counts.values()) > 0
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_enhanced_copy(self):
        """تشغيل النسخ المحسن للفهارس"""
        print("🚀 بدء النسخ المحسن للفهارس")
        print("=" * 50)
        
        start_time = datetime.now()
        
        try:
            if not self.connect():
                return False
            
            # نسخ الفهارس المفقودة
            success = self.copy_missing_indexes()
            
            if success:
                # التحقق من النتائج
                self.verify_indexes()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            # النتائج النهائية
            print("\n" + "=" * 50)
            print("🎉 تم إكمال النسخ المحسن للفهارس!")
            print(f"📊 الفهارس العادية: {self.stats['normal_indexes']}")
            print(f"🔒 الفهارس الفريدة: {self.stats['unique_indexes']}")
            print(f"🗂️ فهارس بت ماب: {self.stats['bitmap_indexes']}")
            print(f"⚙️ الفهارس الوظيفية: {self.stats['function_indexes']}")
            print(f"❌ الفهارس الفاشلة: {self.stats['failed_indexes']}")
            print(f"⏱️ المدة: {duration}")
            
            if self.stats['failed_list']:
                print(f"\n❌ الفهارس الفاشلة:")
                for index in self.stats['failed_list'][:10]:  # أول 10
                    print(f"   - {index}")
                if len(self.stats['failed_list']) > 10:
                    print(f"   ... و {len(self.stats['failed_list']) - 10} فهرس آخر")
            
            print("=" * 50)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في النسخ المحسن: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = EnhancedIndexCopier()
    success = copier.run_enhanced_copy()
    
    if success:
        print("\n✅ النسخ المحسن للفهارس مكتمل!")
    else:
        print("\n❌ فشل النسخ المحسن للفهارس!")
    
    return success


if __name__ == "__main__":
    main()
