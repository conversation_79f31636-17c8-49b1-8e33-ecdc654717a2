#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة نقل قاعدة البيانات Oracle الحقيقية
Real Oracle Database Migration Tool
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime
import json


class OracleRealMigrator:
    """أداة نقل قاعدة البيانات Oracle الحقيقية"""
    
    def __init__(self):
        # تعيين متغير البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_user = 'ias20241'
        self.source_password = 'ys123'
        self.target_user = 'ship2025'
        self.target_password = 'ys123'
        self.dsn = 'yemensoft'
        
        self.source_conn = None
        self.target_conn = None
        self.dba_conn = None
        
    def connect_source(self):
        """الاتصال بالمصدر"""
        try:
            print(f"🔌 الاتصال بالمصدر: {self.source_user}@{self.dsn}")
            self.source_conn = cx_Oracle.connect(
                self.source_user, 
                self.source_password, 
                self.dsn,
                encoding="UTF-8"
            )
            print("✅ تم الاتصال بالمصدر")
            return True
        except Exception as e:
            print(f"❌ فشل الاتصال بالمصدر: {e}")
            return False
    
    def connect_target(self):
        """الاتصال بالهدف"""
        try:
            print(f"🔌 الاتصال بالهدف: {self.target_user}@{self.dsn}")
            self.target_conn = cx_Oracle.connect(
                self.target_user,
                self.target_password,
                self.dsn,
                encoding="UTF-8"
            )
            print("✅ تم الاتصال بالهدف")
            return True
        except Exception as e:
            print(f"❌ فشل الاتصال بالهدف: {e}")
            return False
    
    def connect_dba(self):
        """الاتصال كـ DBA"""
        try:
            print(f"🔌 الاتصال كـ DBA...")
            # محاولة اتصال كـ system
            self.dba_conn = cx_Oracle.connect(
                "system",
                "oracle", 
                self.dsn,
                encoding="UTF-8"
            )
            print("✅ تم الاتصال كـ DBA")
            return True
        except Exception as e:
            print(f"❌ فشل الاتصال كـ DBA: {e}")
            return False
    
    def create_user_ship2025(self):
        """إنشاء المستخدم ship2025"""
        if not self.dba_conn:
            if not self.connect_dba():
                return False
        
        print("👤 إنشاء المستخدم ship2025...")
        
        try:
            cursor = self.dba_conn.cursor()
            
            # التحقق من وجود المستخدم
            cursor.execute("SELECT COUNT(*) FROM dba_users WHERE username = 'SHIP2025'")
            user_exists = cursor.fetchone()[0] > 0
            
            if user_exists:
                print("⚠️ المستخدم ship2025 موجود بالفعل")
                # حذف المستخدم الموجود
                try:
                    cursor.execute("DROP USER ship2025 CASCADE")
                    print("🗑️ تم حذف المستخدم القديم")
                except:
                    pass
            
            # إنشاء المستخدم الجديد
            commands = [
                f"CREATE USER {self.target_user} IDENTIFIED BY {self.target_password}",
                f"GRANT CONNECT TO {self.target_user}",
                f"GRANT RESOURCE TO {self.target_user}",
                f"GRANT CREATE SESSION TO {self.target_user}",
                f"GRANT CREATE TABLE TO {self.target_user}",
                f"GRANT CREATE VIEW TO {self.target_user}",
                f"GRANT CREATE SEQUENCE TO {self.target_user}",
                f"GRANT CREATE PROCEDURE TO {self.target_user}",
                f"ALTER USER {self.target_user} DEFAULT TABLESPACE USERS",
                f"ALTER USER {self.target_user} QUOTA UNLIMITED ON USERS",
                f"GRANT SELECT ANY TABLE TO {self.target_user}",
                f"GRANT INSERT ANY TABLE TO {self.target_user}"
            ]
            
            for command in commands:
                try:
                    cursor.execute(command)
                    print(f"   ✅ {command}")
                except Exception as e:
                    print(f"   ⚠️ {command}: {e}")
            
            self.dba_conn.commit()
            cursor.close()
            
            print("✅ تم إنشاء المستخدم ship2025 بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إنشاء المستخدم: {e}")
            return False
    
    def get_tables_list(self):
        """الحصول على قائمة الجداول من المصدر"""
        if not self.source_conn:
            if not self.connect_source():
                return []
        
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على الجداول مع معلومات إضافية
            cursor.execute("""
                SELECT table_name, num_rows, blocks, avg_row_len
                FROM user_tables 
                WHERE table_name NOT LIKE 'BIN$%'
                ORDER BY num_rows DESC NULLS LAST, table_name
            """)
            
            tables = cursor.fetchall()
            cursor.close()
            
            print(f"📊 تم العثور على {len(tables)} جدول")
            
            # عرض أول 10 جداول
            print("📋 أكبر الجداول:")
            for i, (table_name, num_rows, blocks, avg_row_len) in enumerate(tables[:10]):
                num_rows = num_rows or 0
                print(f"   {i+1:2d}. {table_name}: {num_rows:,} صف")
            
            return [table[0] for table in tables]
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على قائمة الجداول: {e}")
            return []
    
    def get_table_ddl(self, table_name):
        """الحصول على DDL للجدول"""
        if not self.source_conn:
            return None
        
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على أعمدة الجدول
            cursor.execute("""
                SELECT column_name, data_type, data_length, data_precision, 
                       data_scale, nullable, column_id, data_default
                FROM user_tab_columns
                WHERE table_name = :table_name
                ORDER BY column_id
            """, {'table_name': table_name})
            
            columns = cursor.fetchall()
            
            if not columns:
                return None
            
            # بناء DDL
            ddl_lines = [f"CREATE TABLE {table_name} ("]
            
            for i, (col_name, data_type, data_length, data_precision, data_scale, nullable, col_id, data_default) in enumerate(columns):
                # تنسيق نوع البيانات
                if data_type == 'NUMBER':
                    if data_precision and data_scale:
                        col_type = f"NUMBER({data_precision},{data_scale})"
                    elif data_precision:
                        col_type = f"NUMBER({data_precision})"
                    else:
                        col_type = "NUMBER"
                elif data_type == 'VARCHAR2':
                    col_type = f"VARCHAR2({data_length})"
                elif data_type == 'CHAR':
                    col_type = f"CHAR({data_length})"
                elif data_type in ['DATE', 'CLOB', 'BLOB']:
                    col_type = data_type
                else:
                    col_type = data_type
                
                # إضافة DEFAULT إذا وجد
                default_clause = f" DEFAULT {data_default}" if data_default else ""
                
                # إضافة NOT NULL إذا لزم الأمر
                null_clause = " NOT NULL" if nullable == 'N' else ""
                
                # إضافة فاصلة إذا لم يكن آخر عمود
                comma = "," if i < len(columns) - 1 else ""
                
                ddl_lines.append(f"    {col_name} {col_type}{default_clause}{null_clause}{comma}")
            
            ddl_lines.append(");")
            
            cursor.close()
            return "\n".join(ddl_lines)
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على DDL للجدول {table_name}: {e}")
            return None
    
    def create_table(self, table_name, ddl):
        """إنشاء جدول في الهدف"""
        if not self.target_conn:
            if not self.connect_target():
                return False
        
        try:
            cursor = self.target_conn.cursor()
            cursor.execute(ddl)
            self.target_conn.commit()
            cursor.close()
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجدول {table_name}: {e}")
            return False
    
    def copy_table_data(self, table_name):
        """نسخ بيانات الجدول"""
        if not self.source_conn or not self.target_conn:
            return 0
        
        try:
            # الحصول على عدد الصفوف أولاً
            source_cursor = self.source_conn.cursor()
            source_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_rows = source_cursor.fetchone()[0]
            
            if total_rows == 0:
                print(f"   📊 الجدول {table_name} فارغ")
                source_cursor.close()
                return 0
            
            print(f"   📊 نسخ {total_rows:,} صف من {table_name}...")
            
            # نسخ البيانات على دفعات
            batch_size = 1000
            copied_rows = 0
            
            # الحصول على أسماء الأعمدة
            source_cursor.execute(f"SELECT * FROM {table_name} WHERE ROWNUM <= 1")
            columns = [desc[0] for desc in source_cursor.description]
            
            # إعداد استعلام الإدراج
            placeholders = ', '.join([':' + str(i+1) for i in range(len(columns))])
            insert_sql = f"INSERT INTO {table_name} VALUES ({placeholders})"
            
            target_cursor = self.target_conn.cursor()
            
            # نسخ البيانات على دفعات
            offset = 0
            while offset < total_rows:
                # استعلام الدفعة
                batch_sql = f"""
                    SELECT * FROM (
                        SELECT a.*, ROWNUM rnum FROM (
                            SELECT * FROM {table_name} ORDER BY 1
                        ) a WHERE ROWNUM <= {offset + batch_size}
                    ) WHERE rnum > {offset}
                """
                
                source_cursor.execute(batch_sql)
                batch_data = source_cursor.fetchall()
                
                if batch_data:
                    target_cursor.executemany(insert_sql, batch_data)
                    self.target_conn.commit()
                    copied_rows += len(batch_data)
                    
                    if copied_rows % 5000 == 0:
                        print(f"      📈 تم نسخ {copied_rows:,} من {total_rows:,} صف...")
                
                offset += batch_size
            
            source_cursor.close()
            target_cursor.close()
            
            print(f"   ✅ تم نسخ {copied_rows:,} صف من {table_name}")
            return copied_rows
            
        except Exception as e:
            print(f"   ❌ خطأ في نسخ بيانات {table_name}: {e}")
            return 0
    
    def migrate_table(self, table_name):
        """نقل جدول كامل (بنية + بيانات)"""
        print(f"\n📋 نقل جدول: {table_name}")
        
        # الحصول على DDL
        ddl = self.get_table_ddl(table_name)
        if not ddl:
            print(f"   ❌ فشل في الحصول على DDL")
            return False
        
        # إنشاء الجدول
        if not self.create_table(table_name, ddl):
            print(f"   ❌ فشل في إنشاء الجدول")
            return False
        
        print(f"   ✅ تم إنشاء الجدول")
        
        # نسخ البيانات
        copied_rows = self.copy_table_data(table_name)
        
        return copied_rows >= 0
    
    def run_migration(self, table_limit=None):
        """تشغيل عملية النقل الكاملة"""
        print("🚀 بدء عملية نقل قاعدة البيانات Oracle")
        print("=" * 70)
        print(f"المصدر: {self.source_user}@{self.dsn}")
        print(f"الهدف: {self.target_user}@{self.dsn}")
        print("=" * 70)
        
        start_time = datetime.now()
        
        try:
            # إنشاء المستخدم الجديد
            if not self.create_user_ship2025():
                return False
            
            # الاتصال بالمصدر والهدف
            if not self.connect_source() or not self.connect_target():
                return False
            
            # الحصول على قائمة الجداول
            tables = self.get_tables_list()
            if not tables:
                print("❌ لم يتم العثور على جداول للنقل")
                return False
            
            # تحديد عدد الجداول للنقل
            if table_limit:
                tables = tables[:table_limit]
                print(f"📊 سيتم نقل أول {table_limit} جدول")
            
            # نقل الجداول
            successful_tables = 0
            total_rows = 0
            
            for i, table_name in enumerate(tables, 1):
                print(f"\n📋 [{i}/{len(tables)}] نقل جدول: {table_name}")
                
                if self.migrate_table(table_name):
                    successful_tables += 1
                else:
                    print(f"   ❌ فشل في نقل الجدول {table_name}")
            
            # النتائج النهائية
            end_time = datetime.now()
            duration = end_time - start_time
            
            print("\n" + "=" * 70)
            print("🎉 تم إكمال عملية النقل!")
            print(f"📊 الجداول المنقولة: {successful_tables}/{len(tables)}")
            print(f"⏱️ المدة: {duration}")
            print("=" * 70)
            
            return successful_tables > 0
            
        except Exception as e:
            print(f"\n❌ خطأ في عملية النقل: {e}")
            return False
        
        finally:
            # إغلاق الاتصالات
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()
            if self.dba_conn:
                self.dba_conn.close()


def main():
    """الدالة الرئيسية"""
    migrator = OracleRealMigrator()
    
    print("🔧 أداة نقل قاعدة البيانات Oracle")
    print("=" * 50)
    
    # اختيار عدد الجداول للنقل (للاختبار)
    table_limit = input("كم جدول تريد نقله؟ (اتركه فارغ لنقل الكل): ").strip()
    
    if table_limit:
        try:
            table_limit = int(table_limit)
        except:
            table_limit = 10  # افتراضي
    else:
        table_limit = None
    
    # تشغيل النقل
    success = migrator.run_migration(table_limit)
    
    if success:
        print("\n🎊 تم النقل بنجاح!")
        print("📝 يمكنك الآن استخدام قاعدة البيانات الجديدة")
    else:
        print("\n💥 فشل النقل!")


if __name__ == "__main__":
    main()
