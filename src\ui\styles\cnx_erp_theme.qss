/* CnX ERP Theme - مطابق للتصميم في الصورة */

/* الإعدادات العامة */
QMainWindow {
    background-color: #f5f7fa;
    color: #2d3748;
    font-family: "Segoe UI", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", sans-serif;
}

/* الشريط العلوي الأزرق */
QFrame#topHeaderBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #4299e1, stop:1 #3182ce);
    border: none;
    border-bottom: 2px solid #2b6cb0;
}

QFrame#topHeaderBar QLabel {
    color: white;
    font-weight: bold;
}

QFrame#topHeaderBar QPushButton {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: white;
    padding: 8px 16px;
    font-weight: bold;
    min-width: 80px;
}

QFrame#topHeaderBar QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

QFrame#topHeaderBar QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.3);
}

/* القائمة الجانبية الزرقاء */
QFrame#sideMenu {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #2b6cb0, stop:1 #3182ce);
    border: none;
    border-right: 1px solid #2c5282;
}

QFrame#sideMenu QLabel {
    color: white;
    font-weight: bold;
    padding: 10px;
    font-size: 14px;
}

QFrame#sideMenu QPushButton {
    background-color: transparent;
    border: none;
    color: white;
    text-align: right;
    padding: 12px 20px;
    font-size: 13px;
    font-weight: normal;
}

QFrame#sideMenu QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-right: 3px solid #63b3ed;
}

QFrame#sideMenu QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.2);
}

/* منطقة المحتوى الرئيسية */
QFrame#contentArea {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

/* شعار CnX ERP */
QLabel#logoLabel {
    color: #2d3748;
    font-size: 64px;
    font-weight: bold;
    font-family: "Arial", sans-serif;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

QLabel#logoSubtitle {
    color: #4a5568;
    font-size: 18px;
    font-weight: normal;
    font-style: italic;
    margin-top: 10px;
}

/* الأزرار العامة */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #4299e1, stop:1 #3182ce);
    border: 1px solid #2b6cb0;
    border-radius: 6px;
    color: white;
    padding: 10px 20px;
    font-weight: bold;
    min-width: 100px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #63b3ed, stop:1 #4299e1);
    border: 1px solid #3182ce;
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #3182ce, stop:1 #2b6cb0);
}

QPushButton:disabled {
    background-color: #a0aec0;
    border: 1px solid #718096;
    color: #4a5568;
}

/* شريط التمرير */
QScrollBar:vertical {
    background-color: #f7fafc;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #cbd5e0;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0aec0;
}

/* شريط الحالة */
QStatusBar {
    background-color: #edf2f7;
    border-top: 1px solid #e2e8f0;
    color: #4a5568;
}

/* التحسينات الإضافية */
QWidget {
    selection-background-color: #bee3f8;
    selection-color: #1a202c;
}

QToolTip {
    background-color: #2d3748;
    color: white;
    border: 1px solid #4a5568;
    border-radius: 4px;
    padding: 5px;
}

/* تحسينات إضافية للواجهة */
QSplitter::handle {
    background-color: #e2e8f0;
    width: 2px;
}

QSplitter::handle:hover {
    background-color: #cbd5e0;
}

/* تحسين مظهر منطقة التمرير */
QScrollArea {
    border: none;
    background-color: transparent;
}

/* تحسين الخطوط العربية */
* {
    font-family: "Segoe UI", "Tahoma", "Arial", "Traditional Arabic", sans-serif;
}

/* تحسينات خاصة بالشجرة */
QTreeWidget {
    background-color: transparent;
    color: white;
    border: none;
    outline: none;
    font-size: 13px;
    font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
}

QTreeWidget::item {
    padding: 8px 15px;
    border: none;
    min-height: 35px;
    border-radius: 4px;
    margin: 1px 5px;
}

QTreeWidget::item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-right: 3px solid #60a5fa;
}

QTreeWidget::item:selected {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border-right: 3px solid #3b82f6;
}

QTreeWidget::branch {
    background: transparent;
}

QTreeWidget::branch:has-children:!has-siblings:closed,
QTreeWidget::branch:closed:has-children:has-siblings {
    border-image: none;
    image: none;
}

QTreeWidget::branch:open:has-children:!has-siblings,
QTreeWidget::branch:open:has-children:has-siblings {
    border-image: none;
    image: none;
}

/* تحسين مظهر العقد الرئيسية */
QTreeWidget::item[level="0"] {
    font-weight: bold;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

QTreeWidget::item[level="0"]:hover {
    background-color: rgba(255, 255, 255, 0.15);
}
