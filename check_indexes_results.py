#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص نتائج نسخ الفهارس
Check indexes copy results
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def check_indexes():
    """فحص الفهارس في المصدر والهدف"""
    setup_environment()
    
    print("🔍 فحص الفهارس")
    print("=" * 50)
    
    try:
        # الاتصال بالمصدر
        print("📊 فحص الفهارس في المصدر (ias20241)...")
        source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        source_cursor = source_conn.cursor()
        
        # عد الفهارس في المصدر (باستثناء فهارس النظام والقيود)
        source_cursor.execute("""
            SELECT 
                CASE 
                    WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                    WHEN index_type = 'BITMAP' THEN 'BITMAP'
                    WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                    ELSE 'NORMAL'
                END as index_category,
                COUNT(*) 
            FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            AND NOT EXISTS (
                SELECT 1 FROM user_constraints c 
                WHERE c.index_name = index_name
                AND c.constraint_type IN ('P', 'U')
            )
            GROUP BY 
                CASE 
                    WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                    WHEN index_type = 'BITMAP' THEN 'BITMAP'
                    WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                    ELSE 'NORMAL'
                END
            ORDER BY 1
        """)
        source_counts = dict(source_cursor.fetchall())
        
        # الاتصال بالهدف
        print("📊 فحص الفهارس في الهدف (ship2025)...")
        target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        target_cursor = target_conn.cursor()
        
        # عد الفهارس في الهدف
        target_cursor.execute("""
            SELECT 
                CASE 
                    WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                    WHEN index_type = 'BITMAP' THEN 'BITMAP'
                    WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                    ELSE 'NORMAL'
                END as index_category,
                COUNT(*) 
            FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            GROUP BY 
                CASE 
                    WHEN uniqueness = 'UNIQUE' THEN 'UNIQUE'
                    WHEN index_type = 'BITMAP' THEN 'BITMAP'
                    WHEN index_type = 'FUNCTION-BASED NORMAL' THEN 'FUNCTION'
                    ELSE 'NORMAL'
                END
            ORDER BY 1
        """)
        target_counts = dict(target_cursor.fetchall())
        
        # عرض النتائج
        type_names = {
            'NORMAL': 'فهارس عادية (Normal Indexes)',
            'UNIQUE': 'فهارس فريدة (Unique Indexes)', 
            'BITMAP': 'فهارس بت ماب (Bitmap Indexes)',
            'FUNCTION': 'فهارس وظيفية (Function-based Indexes)'
        }
        
        print("\n📋 مقارنة الفهارس:")
        print("-" * 50)
        
        total_source = 0
        total_target = 0
        
        for idx_type in ['NORMAL', 'UNIQUE', 'BITMAP', 'FUNCTION']:
            source_count = source_counts.get(idx_type, 0)
            target_count = target_counts.get(idx_type, 0)
            percentage = (target_count / source_count * 100) if source_count > 0 else 0
            
            total_source += source_count
            total_target += target_count
            
            print(f"{type_names[idx_type]}:")
            print(f"   المصدر: {source_count}")
            print(f"   الهدف: {target_count}")
            print(f"   النسبة: {percentage:.1f}%")
            print()
        
        print("-" * 50)
        print(f"إجمالي الفهارس:")
        print(f"   المصدر: {total_source}")
        print(f"   الهدف: {total_target}")
        if total_source > 0:
            overall_percentage = (total_target / total_source) * 100
            print(f"   النسبة الإجمالية: {overall_percentage:.1f}%")
        
        # فحص عينة من الفهارس
        print("\n📋 عينة من الفهارس في الهدف:")
        target_cursor.execute("""
            SELECT index_name, table_name, index_type, uniqueness, status
            FROM user_indexes
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            ORDER BY table_name, index_name
        """)
        
        indexes = target_cursor.fetchall()
        
        if indexes:
            print(f"   تم العثور على {len(indexes)} فهرس")
            
            # عرض أول 15 فهرس
            for i, (idx_name, table_name, idx_type, uniqueness, status) in enumerate(indexes[:15]):
                # تحديد الرمز حسب نوع الفهرس
                if uniqueness == 'UNIQUE':
                    symbol = '🔒'
                elif idx_type == 'BITMAP':
                    symbol = '🗂️'
                elif idx_type == 'FUNCTION-BASED NORMAL':
                    symbol = '⚙️'
                else:
                    symbol = '📊'
                
                print(f"   {i+1:2d}. {symbol} {idx_name} على {table_name} ({status})")
            
            if len(indexes) > 15:
                print(f"   ... و {len(indexes) - 15} فهرس آخر")
        else:
            print("   ❌ لم يتم العثور على فهارس")
        
        # فحص الفهارس الوظيفية
        print("\n⚙️ فحص الفهارس الوظيفية:")
        target_cursor.execute("""
            SELECT i.index_name, i.table_name, e.column_expression
            FROM user_indexes i, user_ind_expressions e
            WHERE i.index_name = e.index_name
            AND i.index_type = 'FUNCTION-BASED NORMAL'
            ORDER BY i.table_name, i.index_name
        """)
        
        function_indexes = target_cursor.fetchall()
        
        if function_indexes:
            print(f"   تم العثور على {len(function_indexes)} فهرس وظيفي")
            
            # عرض أول 10 فهارس وظيفية
            for i, (idx_name, table_name, expression) in enumerate(function_indexes[:10]):
                # تقصير التعبير إذا كان طويل
                short_expr = expression[:50] + "..." if len(expression) > 50 else expression
                print(f"   {i+1:2d}. {idx_name} على {table_name}")
                print(f"       التعبير: {short_expr}")
            
            if len(function_indexes) > 10:
                print(f"   ... و {len(function_indexes) - 10} فهرس وظيفي آخر")
        else:
            print("   ❌ لم يتم العثور على فهارس وظيفية")
        
        # فحص حالة الفهارس
        print("\n📊 فحص حالة الفهارس:")
        target_cursor.execute("""
            SELECT status, COUNT(*)
            FROM user_indexes
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            GROUP BY status
            ORDER BY status
        """)
        
        status_counts = target_cursor.fetchall()
        
        if status_counts:
            for status, count in status_counts:
                status_icon = "✅" if status == "VALID" else "⚠️"
                print(f"   {status_icon} {status}: {count} فهرس")
        
        # إغلاق الاتصالات
        source_cursor.close()
        source_conn.close()
        target_cursor.close()
        target_conn.close()
        
        print("\n" + "=" * 50)
        
        if total_target > 0:
            print("✅ تم نسخ الفهارس بنجاح!")
        else:
            print("❌ لم يتم نسخ أي فهارس")
        
        return total_target > 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص الفهارس: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    success = check_indexes()
    
    if success:
        print("🎉 فحص الفهارس مكتمل!")
    else:
        print("💥 فشل في فحص الفهارس!")
    
    return success


if __name__ == "__main__":
    main()
