#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص نتائج نسخ المشاهد
Check views copy results
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def check_views():
    """فحص المشاهد في المصدر والهدف"""
    setup_environment()
    
    print("🔍 فحص المشاهد")
    print("=" * 50)
    
    try:
        # الاتصال بالمصدر
        print("📊 فحص المشاهد في المصدر (ias20241)...")
        source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        source_cursor = source_conn.cursor()
        
        # عد المشاهد في المصدر
        source_cursor.execute("""
            SELECT COUNT(*) FROM user_views WHERE view_name NOT LIKE 'BIN$%'
        """)
        source_total = source_cursor.fetchone()[0]
        
        # تصنيف المشاهد في المصدر
        source_cursor.execute("""
            SELECT 
                CASE 
                    WHEN text_length > 4000 THEN 'COMPLEX'
                    ELSE 'SIMPLE'
                END as view_category,
                COUNT(*) 
            FROM user_views 
            WHERE view_name NOT LIKE 'BIN$%'
            GROUP BY 
                CASE 
                    WHEN text_length > 4000 THEN 'COMPLEX'
                    ELSE 'SIMPLE'
                END
            ORDER BY 1
        """)
        source_counts = dict(source_cursor.fetchall())
        
        # الاتصال بالهدف
        print("📊 فحص المشاهد في الهدف (ship2025)...")
        target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        target_cursor = target_conn.cursor()
        
        # عد المشاهد في الهدف
        target_cursor.execute("""
            SELECT COUNT(*) FROM user_views WHERE view_name NOT LIKE 'BIN$%'
        """)
        target_total = target_cursor.fetchone()[0]
        
        # تصنيف المشاهد في الهدف
        target_cursor.execute("""
            SELECT 
                CASE 
                    WHEN text_length > 4000 THEN 'COMPLEX'
                    ELSE 'SIMPLE'
                END as view_category,
                COUNT(*) 
            FROM user_views 
            WHERE view_name NOT LIKE 'BIN$%'
            GROUP BY 
                CASE 
                    WHEN text_length > 4000 THEN 'COMPLEX'
                    ELSE 'SIMPLE'
                END
            ORDER BY 1
        """)
        target_counts = dict(target_cursor.fetchall())
        
        # عرض النتائج
        print("\n📋 مقارنة المشاهد:")
        print("-" * 50)
        
        type_names = {
            'SIMPLE': 'مشاهد بسيطة (Simple Views)',
            'COMPLEX': 'مشاهد معقدة (Complex Views)'
        }
        
        for view_type in ['SIMPLE', 'COMPLEX']:
            source_count = source_counts.get(view_type, 0)
            target_count = target_counts.get(view_type, 0)
            percentage = (target_count / source_count * 100) if source_count > 0 else 0
            
            print(f"{type_names[view_type]}:")
            print(f"   المصدر: {source_count}")
            print(f"   الهدف: {target_count}")
            print(f"   النسبة: {percentage:.1f}%")
            print()
        
        print("-" * 50)
        print(f"إجمالي المشاهد:")
        print(f"   المصدر: {source_total}")
        print(f"   الهدف: {target_total}")
        if source_total > 0:
            overall_percentage = (target_total / source_total) * 100
            print(f"   النسبة الإجمالية: {overall_percentage:.1f}%")
        
        # فحص حالة المشاهد
        print("\n📊 حالة المشاهد في الهدف:")
        target_cursor.execute("""
            SELECT status, COUNT(*)
            FROM user_objects
            WHERE object_type = 'VIEW'
            AND object_name NOT LIKE 'BIN$%'
            GROUP BY status
            ORDER BY status
        """)
        
        status_counts = target_cursor.fetchall()
        
        if status_counts:
            for status, count in status_counts:
                status_icon = "✅" if status == "VALID" else "⚠️"
                percentage = (count / target_total * 100) if target_total > 0 else 0
                print(f"   {status_icon} {status}: {count} مشهد ({percentage:.1f}%)")
        else:
            print("   ❌ لم يتم العثور على معلومات حالة المشاهد")
        
        # فحص عينة من المشاهد
        print("\n📋 عينة من المشاهد في الهدف:")
        target_cursor.execute("""
            SELECT view_name, text_length
            FROM user_views
            WHERE view_name NOT LIKE 'BIN$%'
            AND ROWNUM <= 15
            ORDER BY view_name
        """)
        
        views = target_cursor.fetchall()
        
        if views:
            print(f"   تم العثور على {target_total} مشهد")
            
            # عرض أول 15 مشهد
            for i, (view_name, text_length) in enumerate(views, 1):
                # تحديد الرمز حسب تعقيد المشهد
                if text_length and text_length > 4000:
                    symbol = '🔧'  # مشهد معقد
                else:
                    symbol = '📊'  # مشهد بسيط
                
                length_display = f"({text_length} حرف)" if text_length else "(غير محدد)"
                print(f"   {i:2d}. {symbol} {view_name} {length_display}")
            
            if target_total > 15:
                print(f"   ... و {target_total - 15} مشهد آخر")
        else:
            print("   ❌ لم يتم العثور على مشاهد")
        
        # فحص المشاهد المعقدة
        print("\n🔧 فحص المشاهد المعقدة:")
        target_cursor.execute("""
            SELECT view_name, text_length
            FROM user_views
            WHERE view_name NOT LIKE 'BIN$%'
            AND text_length > 4000
            ORDER BY text_length DESC
        """)
        
        complex_views = target_cursor.fetchall()
        
        if complex_views:
            print(f"   تم العثور على {len(complex_views)} مشهد معقد")
            
            # عرض أول 10 مشاهد معقدة
            for i, (view_name, text_length) in enumerate(complex_views[:10], 1):
                print(f"   {i:2d}. {view_name} ({text_length:,} حرف)")
            
            if len(complex_views) > 10:
                print(f"   ... و {len(complex_views) - 10} مشهد معقد آخر")
        else:
            print("   ❌ لم يتم العثور على مشاهد معقدة")
        
        # فحص المشاهد غير الصالحة
        print("\n⚠️ فحص المشاهد غير الصالحة:")
        target_cursor.execute("""
            SELECT object_name, status
            FROM user_objects
            WHERE object_type = 'VIEW'
            AND object_name NOT LIKE 'BIN$%'
            AND status != 'VALID'
            ORDER BY object_name
        """)
        
        invalid_views = target_cursor.fetchall()
        
        if invalid_views:
            print(f"   تم العثور على {len(invalid_views)} مشهد غير صالح:")
            for view_name, status in invalid_views[:10]:
                print(f"   ⚠️ {view_name} ({status})")
            if len(invalid_views) > 10:
                print(f"   ... و {len(invalid_views) - 10} مشهد غير صالح آخر")
        else:
            print("   ✅ جميع المشاهد صالحة!")
        
        # إغلاق الاتصالات
        source_cursor.close()
        source_conn.close()
        target_cursor.close()
        target_conn.close()
        
        print("\n" + "=" * 50)
        
        if target_total > 0:
            print("✅ تم نسخ المشاهد بنجاح!")
        else:
            print("❌ لم يتم نسخ أي مشاهد")
        
        return target_total > 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص المشاهد: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    success = check_views()
    
    if success:
        print("🎉 فحص المشاهد مكتمل!")
    else:
        print("💥 فشل في فحص المشاهد!")
    
    return success


if __name__ == "__main__":
    main()
