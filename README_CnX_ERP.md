# 🏢 CnX ERP - نظام تخطيط موارد المؤسسات

## 📋 نظرة عامة

تم تطوير واجهة نظام **CnX ERP** بناءً على التصميم الأصلي المطلوب، مع دعم كامل للغة العربية وتصميم احترافي يحاكي أنظمة ERP الحديثة.

---

## 🎯 **الميزات الرئيسية**

### ✨ **التصميم والواجهة**
- 🎨 **خلفية فنية متدرجة**: خطوط منحنية ملونة تحاكي التصميم الأصلي
- 🌐 **دعم كامل للعربية**: اتجاه RTL وخطوط عربية واضحة
- 📱 **تصميم متجاوب**: يتكيف مع أحجام الشاشات المختلفة
- 🎭 **ألوان احترافية**: لوحة ألوان متناسقة ومريحة للعين

### 🛠️ **المكونات الرئيسية**
- 📊 **شريط الأدوات العلوي**: 9 أدوات رئيسية مع أيقونات واضحة
- 📋 **القائمة الجانبية اليسرى**: 10 قوائم رئيسية للنظام
- 🔧 **لوحة التحكم اليمنى**: عناصر البحث والتصفية
- 🎨 **المنطقة المركزية**: شعار CnX ERP مع خلفية فنية

---

## 🚀 **التثبيت والتشغيل**

### 📦 **المتطلبات**
```bash
Python 3.8+
PySide6 >= 6.5.0
arabic-reshaper >= 2.1.0
python-bidi >= 0.4.2
Pillow >= 9.0.0
```

### ⚡ **التثبيت السريع**
```bash
# تثبيت المكتبات المطلوبة
pip install PySide6 arabic-reshaper python-bidi Pillow

# تشغيل النسخة البسيطة
python cnx_erp_simple.py

# تشغيل النسخة المحسنة (مع الخلفية الفنية)
python cnx_erp_enhanced.py
```

---

## 📁 **هيكل الملفات**

```
📂 CnX ERP Project/
├── 📄 cnx_erp_simple.py          # النسخة البسيطة للاختبار
├── 📄 cnx_erp_enhanced.py        # النسخة المحسنة مع الخلفية الفنية
├── 📄 cnx_erp_main_window.py     # النسخة الكاملة مع جميع الميزات
├── 📄 UI_ANALYSIS_REPORT.md      # تحليل مفصل للتصميم الأصلي
└── 📄 README_CnX_ERP.md          # دليل الاستخدام (هذا الملف)
```

---

## 🎨 **تحليل التصميم**

### 🏗️ **التخطيط العام**
```
┌─────────────────────────────────────────────────────────┐
│ شريط العنوان + شريط الأدوات (9 أدوات)                    │
├─────────────────────────────────────────────────────────┤
│ لوحة التحكم │    المنطقة المركزية    │ القوائم الرئيسية │
│   (يمين)    │   (شعار + خلفية فنية)  │     (يسار)      │
│   220px     │        مرن           │     240px      │
└─────────────────────────────────────────────────────────┘
```

### 🎨 **لوحة الألوان**
- **الأزرق الأساسي**: `#2196F3` (الأزرار والروابط)
- **الأحمر**: `#E53E3E` (التأكيدات والتنبيهات)
- **الأخضر**: `#4CAF50` (العمليات الناجحة)
- **البرتقالي**: `#FF9800` (التحذيرات)
- **الرمادي الفاتح**: `#F5F5F5` (الخلفيات)

---

## 🔧 **الوظائف المتاحة**

### 📊 **شريط الأدوات**
1. **ℹ️ معلومات** - عرض معلومات النظام
2. **🔊 الصوت** - تشغيل/إيقاف الأصوات
3. **🔔 التنبيهات** - عرض الإشعارات
4. **⚙️ الإعدادات** - إعدادات النظام
5. **🖨️ طباعة** - طباعة التقارير
6. **💾 حفظ** - حفظ البيانات
7. **⭐ المفضلة** - إدارة المفضلة
8. **🔧 الأدوات** - أدوات إضافية
9. **📧 البريد** - البريد الإلكتروني

### 📋 **القوائم الرئيسية**
1. **📊 التقرير الإحصائي**
2. **🏢 مركز التكلفة**
3. **📋 أوامر الشراء**
4. **📦 بيانات الأصناف**
5. **📈 بيانات وحسابات**
6. **💰 سجل الأرصدة**
7. **📋 قائمة الجرد/العمل**
8. **📊 تقرير الأرصدة الحالية**
9. **📈 تقرير حركة المخزون**
10. **📋 تقارير الحركات المالية**

### 🔧 **لوحة التحكم**
- **🔍 البحث**: حقل بحث ذكي
- **📅 التاريخ**: اختيار التاريخ
- **📂 النوع**: تصنيف العمليات
- **📊 الحالة**: حالة السجلات
- **أزرار العمليات**: بحث، تصفية، تصدير، طباعة

---

## 🎨 **الخلفية الفنية**

### ✨ **المميزات الفنية**
- **خطوط منحنية متدرجة**: تحاكي التصميم الأصلي
- **ألوان متدرجة**: أحمر، أزرق، أخضر بشفافية متدرجة
- **حركة ديناميكية**: منحنيات رياضية متدفقة
- **تأثيرات بصرية**: ظلال وانعكاسات خفيفة

### 🔧 **التخصيص**
يمكن تخصيص الخلفية الفنية من خلال تعديل:
- عدد الخطوط المنحنية
- كثافة الألوان والشفافية
- اتجاه وشكل المنحنيات
- التدرجات اللونية

---

## 🚀 **التطوير والتوسع**

### 📈 **إضافة وظائف جديدة**
```python
# مثال لإضافة وظيفة جديدة
def add_new_feature(self):
    """إضافة ميزة جديدة للنظام"""
    # كود الميزة الجديدة
    pass
```

### 🎨 **تخصيص الألوان**
```python
# تخصيص لوحة الألوان
COLORS = {
    'primary': '#2196F3',
    'secondary': '#4CAF50',
    'accent': '#FF9800',
    'background': '#F5F5F5'
}
```

### 🌐 **إضافة لغات جديدة**
```python
# دعم لغات إضافية
LANGUAGES = {
    'ar': 'العربية',
    'en': 'English',
    'fr': 'Français'
}
```

---

## 🐛 **استكشاف الأخطاء**

### ❗ **مشاكل شائعة**

1. **مشكلة عرض النصوص العربية**
   ```bash
   pip install --upgrade arabic-reshaper python-bidi
   ```

2. **مشكلة في الخطوط**
   ```python
   # تأكد من وجود خطوط عربية في النظام
   font = QFont("Arial", 12)  # أو "Tahoma"
   ```

3. **مشكلة في الأيقونات**
   ```python
   # استخدام أيقونات نصية بدلاً من ملفات
   icon = "📊"  # بدلاً من QIcon("icon.png")
   ```

---

## 📞 **الدعم والمساعدة**

### 🔗 **روابط مفيدة**
- [PySide6 Documentation](https://doc.qt.io/qtforpython/)
- [Arabic Text Processing](https://github.com/mpcabd/python-arabic-reshaper)
- [Qt Stylesheets](https://doc.qt.io/qt-6/stylesheet.html)

### 📧 **التواصل**
- **البريد الإلكتروني**: <EMAIL>
- **الموقع الرسمي**: www.cnx-erp.com
- **الدعم الفني**: +1-800-CnX-ERP

---

## 📄 **الترخيص**

هذا المشروع مطور لأغراض تعليمية وتجريبية. يمكن استخدامه وتطويره بحرية مع الإشارة للمصدر الأصلي.

---

## 🎉 **شكر وتقدير**

تم تطوير هذا النظام بعناية فائقة لمحاكاة التصميم الأصلي المطلوب، مع إضافة تحسينات حديثة وميزات متقدمة لتجربة مستخدم مثلى.

**نتمنى لكم تجربة ممتعة مع نظام CnX ERP! 🚀**
