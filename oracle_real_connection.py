#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اتصال حقيقي بقاعدة بيانات Oracle
Real Oracle Database Connection
"""

import cx_Oracle
import json
from pathlib import Path
from datetime import datetime
import logging


class RealOracleConnection:
    """اتصال حقيقي بـ Oracle"""
    
    def __init__(self):
        self.connection = None
        self.cursor = None
        
        # إعداد السجلات
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # إعدادات الاتصال
        self.configs = {
            'ias20241': {
                'username': 'ias20241',
                'password': 'ys123',
                'dsn': 'yemensoft'
            },
            'ship2025': {
                'username': 'ship2025', 
                'password': 'ys123',
                'dsn': 'yemensoft'
            },
            'system': {
                'username': 'system',
                'password': 'oracle',
                'dsn': 'yemensoft'
            }
        }
    
    def connect(self, user_type='ias20241'):
        """الاتصال بقاعدة البيانات"""
        try:
            config = self.configs[user_type]
            
            print(f"🔌 الاتصال بـ Oracle...")
            print(f"   المستخدم: {config['username']}")
            print(f"   الخادم: {config['dsn']}")
            
            # محاولة الاتصال
            self.connection = cx_Oracle.connect(
                user=config['username'],
                password=config['password'],
                dsn=config['dsn'],
                encoding="UTF-8"
            )
            
            self.cursor = self.connection.cursor()
            
            # اختبار الاتصال
            self.cursor.execute("SELECT USER FROM DUAL")
            current_user = self.cursor.fetchone()[0]
            
            self.cursor.execute("SELECT SYSDATE FROM DUAL")
            current_time = self.cursor.fetchone()[0]
            
            print(f"✅ تم الاتصال بنجاح!")
            print(f"   المستخدم الحالي: {current_user}")
            print(f"   وقت الخادم: {current_time}")
            
            return True
            
        except cx_Oracle.Error as e:
            error, = e.args
            print(f"❌ خطأ Oracle: {error.message}")
            print(f"   الكود: {error.code}")
            return False
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال"""
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
            if self.connection:
                self.connection.close()
                self.connection = None
            print("🔌 تم قطع الاتصال")
        except Exception as e:
            print(f"❌ خطأ في قطع الاتصال: {e}")
    
    def execute_query(self, query, params=None, fetch=True):
        """تنفيذ استعلام"""
        try:
            if not self.connection:
                raise Exception("لا يوجد اتصال بقاعدة البيانات")
            
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            if fetch and query.strip().upper().startswith('SELECT'):
                return self.cursor.fetchall()
            else:
                self.connection.commit()
                return self.cursor.rowcount
                
        except Exception as e:
            print(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            raise
    
    def execute_script(self, script_content):
        """تنفيذ سكريبت SQL"""
        try:
            # تقسيم السكريبت إلى أوامر منفصلة
            commands = []
            current_command = ""
            
            for line in script_content.split('\n'):
                line = line.strip()
                
                # تجاهل التعليقات والأسطر الفارغة
                if not line or line.startswith('--'):
                    continue
                
                current_command += line + " "
                
                # إذا انتهى السطر بـ ; فهو نهاية الأمر
                if line.endswith(';'):
                    commands.append(current_command.strip()[:-1])  # إزالة ;
                    current_command = ""
            
            # إضافة آخر أمر إذا لم ينته بـ ;
            if current_command.strip():
                commands.append(current_command.strip())
            
            executed_count = 0
            for command in commands:
                if command.strip():
                    try:
                        self.cursor.execute(command)
                        executed_count += 1
                        if executed_count % 5 == 0:
                            print(f"   📊 تم تنفيذ {executed_count} أمر...")
                    except Exception as e:
                        print(f"   ⚠️ خطأ في الأمر: {str(e)[:100]}...")
            
            self.connection.commit()
            print(f"✅ تم تنفيذ {executed_count} أمر بنجاح")
            return executed_count
            
        except Exception as e:
            print(f"❌ خطأ في تنفيذ السكريبت: {e}")
            if self.connection:
                self.connection.rollback()
            raise
    
    def get_tables_info(self):
        """الحصول على معلومات الجداول"""
        try:
            query = """
                SELECT table_name, num_rows, blocks, avg_row_len, last_analyzed
                FROM user_tables
                ORDER BY table_name
            """
            
            tables = self.execute_query(query)
            
            tables_info = {}
            for table in tables:
                table_name = table[0]
                tables_info[table_name] = {
                    'name': table_name,
                    'num_rows': table[1] or 0,
                    'blocks': table[2] or 0,
                    'avg_row_len': table[3] or 0,
                    'last_analyzed': str(table[4]) if table[4] else None
                }
            
            return tables_info
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات الجداول: {e}")
            return {}
    
    def get_table_columns(self, table_name):
        """الحصول على أعمدة جدول محدد"""
        try:
            query = """
                SELECT column_name, data_type, data_length, data_precision, 
                       data_scale, nullable, column_id
                FROM user_tab_columns
                WHERE table_name = :table_name
                ORDER BY column_id
            """
            
            return self.execute_query(query, {'table_name': table_name})
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على أعمدة الجدول: {e}")
            return []
    
    def check_user_exists(self, username):
        """التحقق من وجود مستخدم"""
        try:
            query = "SELECT COUNT(*) FROM dba_users WHERE username = :username"
            result = self.execute_query(query, {'username': username.upper()})
            return result[0][0] > 0
        except:
            return False
    
    def create_user(self, username, password):
        """إنشاء مستخدم جديد"""
        try:
            commands = [
                f"CREATE USER {username} IDENTIFIED BY {password}",
                f"GRANT CONNECT TO {username}",
                f"GRANT RESOURCE TO {username}",
                f"GRANT CREATE SESSION TO {username}",
                f"GRANT CREATE TABLE TO {username}",
                f"GRANT CREATE VIEW TO {username}",
                f"GRANT CREATE SEQUENCE TO {username}",
                f"ALTER USER {username} DEFAULT TABLESPACE USERS",
                f"ALTER USER {username} QUOTA UNLIMITED ON USERS"
            ]
            
            for command in commands:
                self.cursor.execute(command)
            
            self.connection.commit()
            print(f"✅ تم إنشاء المستخدم {username} بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدم: {e}")
            return False


def test_real_connection():
    """اختبار الاتصال الحقيقي"""
    print("🧪 اختبار الاتصال الحقيقي بـ Oracle")
    print("=" * 50)
    
    oracle = RealOracleConnection()
    
    # اختبار الاتصال بـ ias20241
    print("\n1️⃣ اختبار الاتصال بـ ias20241:")
    if oracle.connect('ias20241'):
        # الحصول على معلومات الجداول
        tables_info = oracle.get_tables_info()
        print(f"📊 عدد الجداول: {len(tables_info)}")
        
        for table_name, info in list(tables_info.items())[:3]:  # أول 3 جداول
            print(f"   📋 {table_name}: {info['num_rows']} صف")
        
        oracle.disconnect()
    
    # اختبار الاتصال بـ ship2025
    print("\n2️⃣ اختبار الاتصال بـ ship2025:")
    if oracle.connect('ship2025'):
        print("✅ المستخدم ship2025 موجود")
        oracle.disconnect()
    else:
        print("❌ المستخدم ship2025 غير موجود - يحتاج إنشاء")
    
    print("\n🎉 انتهى اختبار الاتصال")


if __name__ == "__main__":
    test_real_connection()
