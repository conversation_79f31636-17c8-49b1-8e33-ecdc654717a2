{"inspection_info": {"username": "ias20241", "dsn": "yemensoft", "inspection_date": "2025-07-12T13:05:41.272215", "oracle_version": "19c", "note": "هذه محاكاة لبنية قاعدة البيانات المتوقعة"}, "tables": [{"table_name": "CUSTOMERS", "num_rows": 15000, "blocks": 250, "avg_row_len": 180, "last_analyzed": "2024-07-10"}, {"table_name": "REMITTANCES", "num_rows": 45000, "blocks": 800, "avg_row_len": 220, "last_analyzed": "2024-07-10"}, {"table_name": "BRANCHES", "num_rows": 25, "blocks": 1, "avg_row_len": 150, "last_analyzed": "2024-07-10"}, {"table_name": "CURRENCIES", "num_rows": 50, "blocks": 1, "avg_row_len": 80, "last_analyzed": "2024-07-10"}, {"table_name": "EXCHANGE_RATES", "num_rows": 2000, "blocks": 15, "avg_row_len": 60, "last_analyzed": "2024-07-10"}, {"table_name": "TRANSACTIONS", "num_rows": 120000, "blocks": 2000, "avg_row_len": 200, "last_analyzed": "2024-07-10"}, {"table_name": "USERS", "num_rows": 100, "blocks": 2, "avg_row_len": 120, "last_analyzed": "2024-07-10"}, {"table_name": "SUPPLIERS", "num_rows": 500, "blocks": 10, "avg_row_len": 160, "last_analyzed": "2024-07-10"}, {"table_name": "BANKS", "num_rows": 200, "blocks": 3, "avg_row_len": 140, "last_analyzed": "2024-07-10"}, {"table_name": "REPORTS", "num_rows": 5000, "blocks": 50, "avg_row_len": 300, "last_analyzed": "2024-07-10"}], "columns": [{"table_name": "CUSTOMERS", "column_name": "CUSTOMER_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N", "column_id": 1}, {"table_name": "CUSTOMERS", "column_name": "CUSTOMER_NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N", "column_id": 2}, {"table_name": "CUSTOMERS", "column_name": "CUSTOMER_NAME_EN", "data_type": "VARCHAR2", "data_length": 100, "nullable": "Y", "column_id": 3}, {"table_name": "CUSTOMERS", "column_name": "ID_NUMBER", "data_type": "VARCHAR2", "data_length": 20, "nullable": "Y", "column_id": 4}, {"table_name": "CUSTOMERS", "column_name": "PHONE", "data_type": "VARCHAR2", "data_length": 20, "nullable": "Y", "column_id": 5}, {"table_name": "CUSTOMERS", "column_name": "EMAIL", "data_type": "VARCHAR2", "data_length": 100, "nullable": "Y", "column_id": 6}, {"table_name": "CUSTOMERS", "column_name": "ADDRESS", "data_type": "VARCHAR2", "data_length": 200, "nullable": "Y", "column_id": 7}, {"table_name": "CUSTOMERS", "column_name": "NATIONALITY", "data_type": "VARCHAR2", "data_length": 50, "nullable": "Y", "column_id": 8}, {"table_name": "CUSTOMERS", "column_name": "CREATED_DATE", "data_type": "DATE", "data_length": 7, "nullable": "N", "column_id": 9}, {"table_name": "CUSTOMERS", "column_name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N", "column_id": 10}, {"table_name": "REMITTANCES", "column_name": "REMITTANCE_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N", "column_id": 1}, {"table_name": "REMITTANCES", "column_name": "REMITTANCE_NUMBER", "data_type": "VARCHAR2", "data_length": 20, "nullable": "N", "column_id": 2}, {"table_name": "REMITTANCES", "column_name": "CUSTOMER_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N", "column_id": 3}, {"table_name": "REMITTANCES", "column_name": "SENDER_NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N", "column_id": 4}, {"table_name": "REMITTANCES", "column_name": "RECEIVER_NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N", "column_id": 5}, {"table_name": "REMITTANCES", "column_name": "AMOUNT", "data_type": "NUMBER", "data_length": 22, "data_precision": 15, "data_scale": 2, "nullable": "N", "column_id": 6}, {"table_name": "REMITTANCES", "column_name": "CURRENCY_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N", "column_id": 7}, {"table_name": "REMITTANCES", "column_name": "EXCHANGE_RATE", "data_type": "NUMBER", "data_length": 22, "data_precision": 10, "data_scale": 4, "nullable": "N", "column_id": 8}, {"table_name": "REMITTANCES", "column_name": "BRANCH_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N", "column_id": 9}, {"table_name": "REMITTANCES", "column_name": "USER_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N", "column_id": 10}, {"table_name": "REMITTANCES", "column_name": "REMITTANCE_DATE", "data_type": "DATE", "data_length": 7, "nullable": "N", "column_id": 11}, {"table_name": "REMITTANCES", "column_name": "STATUS", "data_type": "VARCHAR2", "data_length": 20, "nullable": "N", "column_id": 12}, {"table_name": "REMITTANCES", "column_name": "NOTES", "data_type": "CLOB", "data_length": 4000, "nullable": "Y", "column_id": 13}, {"table_name": "BRANCHES", "column_name": "BRANCH_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N", "column_id": 1}, {"table_name": "BRANCHES", "column_name": "BRANCH_NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N", "column_id": 2}, {"table_name": "BRANCHES", "column_name": "BRANCH_CODE", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N", "column_id": 3}, {"table_name": "BRANCHES", "column_name": "ADDRESS", "data_type": "VARCHAR2", "data_length": 200, "nullable": "Y", "column_id": 4}, {"table_name": "BRANCHES", "column_name": "PHONE", "data_type": "VARCHAR2", "data_length": 20, "nullable": "Y", "column_id": 5}, {"table_name": "BRANCHES", "column_name": "MANAGER_NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "Y", "column_id": 6}, {"table_name": "BRANCHES", "column_name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N", "column_id": 7}, {"table_name": "CURRENCIES", "column_name": "CURRENCY_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N", "column_id": 1}, {"table_name": "CURRENCIES", "column_name": "CURRENCY_CODE", "data_type": "VARCHAR2", "data_length": 3, "nullable": "N", "column_id": 2}, {"table_name": "CURRENCIES", "column_name": "CURRENCY_NAME", "data_type": "VARCHAR2", "data_length": 50, "nullable": "N", "column_id": 3}, {"table_name": "CURRENCIES", "column_name": "CURRENCY_NAME_EN", "data_type": "VARCHAR2", "data_length": 50, "nullable": "Y", "column_id": 4}, {"table_name": "CURRENCIES", "column_name": "SYMBOL", "data_type": "VARCHAR2", "data_length": 5, "nullable": "Y", "column_id": 5}, {"table_name": "CURRENCIES", "column_name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N", "column_id": 6}], "indexes": [{"index_name": "PK_CUSTOMERS", "table_name": "CUSTOMERS", "index_type": "NORMAL", "uniqueness": "UNIQUE", "status": "VALID"}, {"index_name": "PK_REMITTANCES", "table_name": "REMITTANCES", "index_type": "NORMAL", "uniqueness": "UNIQUE", "status": "VALID"}, {"index_name": "PK_BRANCHES", "table_name": "BRANCHES", "index_type": "NORMAL", "uniqueness": "UNIQUE", "status": "VALID"}, {"index_name": "PK_CURRENCIES", "table_name": "CURRENCIES", "index_type": "NORMAL", "uniqueness": "UNIQUE", "status": "VALID"}, {"index_name": "IDX_REMITTANCES_DATE", "table_name": "REMITTANCES", "index_type": "NORMAL", "uniqueness": "NONUNIQUE", "status": "VALID"}, {"index_name": "IDX_REMITTANCES_CUSTOMER", "table_name": "REMITTANCES", "index_type": "NORMAL", "uniqueness": "NONUNIQUE", "status": "VALID"}, {"index_name": "IDX_CUSTOMERS_ID_NUMBER", "table_name": "CUSTOMERS", "index_type": "NORMAL", "uniqueness": "NONUNIQUE", "status": "VALID"}], "constraints": [{"constraint_name": "PK_CUSTOMERS", "table_name": "CUSTOMERS", "constraint_type": "P", "status": "ENABLED"}, {"constraint_name": "PK_REMITTANCES", "table_name": "REMITTANCES", "constraint_type": "P", "status": "ENABLED"}, {"constraint_name": "PK_BRANCHES", "table_name": "BRANCHES", "constraint_type": "P", "status": "ENABLED"}, {"constraint_name": "PK_CURRENCIES", "table_name": "CURRENCIES", "constraint_type": "P", "status": "ENABLED"}, {"constraint_name": "FK_REMITTANCES_CUSTOMER", "table_name": "REMITTANCES", "constraint_type": "R", "status": "ENABLED"}, {"constraint_name": "FK_REMITTANCES_CURRENCY", "table_name": "REMITTANCES", "constraint_type": "R", "status": "ENABLED"}, {"constraint_name": "FK_REMITTANCES_BRANCH", "table_name": "REMITTANCES", "constraint_type": "R", "status": "ENABLED"}, {"constraint_name": "CHK_CUSTOMERS_STATUS", "table_name": "CUSTOMERS", "constraint_type": "C", "status": "ENABLED"}, {"constraint_name": "CHK_REMITTANCES_STATUS", "table_name": "REMITTANCES", "constraint_type": "C", "status": "ENABLED"}], "sequences": [{"sequence_name": "SEQ_CUSTOMERS", "min_value": 1, "max_value": 999999999999999999999999999, "increment_by": 1, "cycle_flag": "N", "cache_size": 20}, {"sequence_name": "SEQ_REMITTANCES", "min_value": 1, "max_value": 999999999999999999999999999, "increment_by": 1, "cycle_flag": "N", "cache_size": 20}, {"sequence_name": "SEQ_BRANCHES", "min_value": 1, "max_value": 999999999999999999999999999, "increment_by": 1, "cycle_flag": "N", "cache_size": 20}, {"sequence_name": "SEQ_CURRENCIES", "min_value": 1, "max_value": 999999999999999999999999999, "increment_by": 1, "cycle_flag": "N", "cache_size": 20}]}