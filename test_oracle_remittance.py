#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام طلب الحوالة مع Oracle
Test Remittance Request System with Oracle
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from src.database.oracle_manager import test_oracle_connection
from src.database.remittance_requests_oracle import RemittanceRequestsOracle
from src.ui.remittances.remittance_request_window import RemittanceRequestWindow


def test_oracle_connection_basic():
    """اختبار الاتصال الأساسي بـ Oracle"""
    print("🧪 اختبار الاتصال بقاعدة بيانات Oracle...")
    
    try:
        if test_oracle_connection():
            print("✅ الاتصال بـ Oracle يعمل بشكل صحيح!")
            return True
        else:
            print("❌ فشل في الاتصال بـ Oracle!")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False


def test_remittance_requests_oracle():
    """اختبار فئة إدارة طلبات الحوالات مع Oracle"""
    print("\n🧪 اختبار فئة إدارة طلبات الحوالات...")
    
    try:
        oracle_db = RemittanceRequestsOracle()
        
        # اختبار إنشاء الجدول
        print("   📋 اختبار إنشاء الجدول...")
        if oracle_db.create_table_if_not_exists():
            print("   ✅ تم إنشاء/التحقق من الجدول بنجاح")
        else:
            print("   ❌ فشل في إنشاء الجدول")
            return False
        
        # اختبار الحصول على الطلبات
        print("   📋 اختبار الحصول على قائمة الطلبات...")
        requests = oracle_db.get_all_requests()
        print(f"   ✅ تم الحصول على {len(requests)} طلب")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار فئة إدارة الطلبات: {e}")
        return False


def test_remittance_window():
    """اختبار نافذة طلب الحوالة"""
    print("\n🧪 اختبار نافذة طلب الحوالة...")
    
    try:
        app = QApplication(sys.argv)
        
        # إنشاء النافذة
        print("   🪟 إنشاء نافذة طلب الحوالة...")
        window = RemittanceRequestWindow()
        
        print("   ✅ تم إنشاء النافذة بنجاح")
        
        # عرض النافذة للاختبار اليدوي
        print("   🖥️ عرض النافذة للاختبار...")
        window.show()
        
        # تشغيل التطبيق لفترة قصيرة للاختبار
        print("   ⏱️ النافذة مفتوحة للاختبار - أغلقها عند الانتهاء")
        
        return app.exec()
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النافذة: {e}")
        return False


def test_create_sample_request():
    """اختبار إنشاء طلب حوالة تجريبي"""
    print("\n🧪 اختبار إنشاء طلب حوالة تجريبي...")
    
    try:
        oracle_db = RemittanceRequestsOracle()
        
        # بيانات تجريبية
        sample_data = {
            'request_number': f"TEST{sys.maxsize % 1000000}",
            'request_date': '2025-07-12',
            'branch': 'الفرع الرئيسي',
            'exchanger': 'أحمد محمد',
            'amount': 1000.00,
            'currency': 'ريال سعودي',
            'exchange_rate': 1.0000,
            'priority': 'عادي',
            'purpose': 'اختبار النظام',
            'sender_name': 'محمد أحمد التجريبي',
            'sender_id': '1234567890',
            'sender_nationality': 'سعودي',
            'sender_id_type': 'هوية وطنية',
            'sender_phone': '**********',
            'sender_landline': '**********',
            'sender_email': '<EMAIL>',
            'sender_pobox': '12345',
            'sender_address': 'الرياض، السعودية',
            'receiver_name': 'سارة محمد التجريبية',
            'receiver_id': '**********',
            'receiver_phone': '**********',
            'receiver_account': '************************',
            'receiver_bank': 'البنك الأهلي السعودي',
            'receiver_branch': 'فرع الرياض',
            'receiver_swift': 'NCBKSARI',
            'receiver_country': 'السعودية',
            'receiver_city': 'الرياض',
            'receiver_bank_country': 'السعودية',
            'receiver_address': 'الرياض، السعودية',
            'notes': 'هذا طلب تجريبي لاختبار النظام',
            'sms_notification': 1,
            'email_notification': 0,
            'auto_create_remittance': 1,
            'print_receipt': 0,
            'status': 'معلق'
        }
        
        print("   📝 إنشاء طلب تجريبي...")
        request_id = oracle_db.create_request(sample_data)
        
        if request_id:
            print(f"   ✅ تم إنشاء الطلب التجريبي بنجاح - ID: {request_id}")
            
            # اختبار الحصول على الطلب
            print("   📖 اختبار الحصول على الطلب...")
            retrieved_request = oracle_db.get_request_by_id(request_id)
            
            if retrieved_request:
                print("   ✅ تم الحصول على الطلب بنجاح")
                
                # اختبار حذف الطلب التجريبي
                print("   🗑️ حذف الطلب التجريبي...")
                if oracle_db.delete_request(request_id):
                    print("   ✅ تم حذف الطلب التجريبي بنجاح")
                    return True
                else:
                    print("   ⚠️ فشل في حذف الطلب التجريبي")
                    return False
            else:
                print("   ❌ فشل في الحصول على الطلب")
                return False
        else:
            print("   ❌ فشل في إنشاء الطلب التجريبي")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إنشاء الطلب: {e}")
        return False


def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار نظام طلب الحوالة مع Oracle")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 4
    
    # اختبار الاتصال
    if test_oracle_connection_basic():
        tests_passed += 1
    
    # اختبار فئة إدارة الطلبات
    if test_remittance_requests_oracle():
        tests_passed += 1
    
    # اختبار إنشاء طلب تجريبي
    if test_create_sample_request():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبارات: {tests_passed}/{total_tests} نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        
        # اختبار النافذة (اختياري)
        print("\n🪟 هل تريد اختبار النافذة؟ (y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice in ['y', 'yes', 'نعم', 'ن']:
                test_remittance_window()
        except:
            pass
        
        return True
    else:
        print("💥 بعض الاختبارات فشلت! يرجى مراجعة الأخطاء")
        return False


def main():
    """الدالة الرئيسية"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n✅ النظام جاهز للاستخدام مع Oracle!")
            print("📝 يمكنك الآن تشغيل التطبيق الرئيسي")
        else:
            print("\n❌ يرجى إصلاح المشاكل قبل الاستخدام")
            
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n💥 خطأ عام في الاختبار: {e}")
        return False


if __name__ == "__main__":
    main()
