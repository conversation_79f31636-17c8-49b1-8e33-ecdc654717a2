#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فاحص قاعدة بيانات Oracle - فحص شامل لبنية المستخدم ias20241
Oracle Database Inspector - Comprehensive structure analysis for user ias20241
"""

try:
    import oracledb
    ORACLE_LIB = 'oracledb'
except ImportError:
    try:
        import cx_Oracle as oracledb
        ORACLE_LIB = 'cx_Oracle'
    except ImportError:
        print("❌ لم يتم العثور على مكتبة Oracle. يرجى تثبيت oracledb أو cx_Oracle")
        exit(1)

import json
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️ مكتبة pandas غير متاحة. سيتم حفظ النتائج كـ JSON فقط")

from datetime import datetime
import os
from pathlib import Path


class OracleDatabaseInspector:
    """فاحص قاعدة بيانات Oracle"""
    
    def __init__(self, username, password, dsn):
        self.username = username
        self.password = password
        self.dsn = dsn
        self.connection = None
        self.cursor = None
        self.inspection_results = {}
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            print(f"🔌 محاولة الاتصال بقاعدة البيانات...")
            print(f"   المكتبة المستخدمة: {ORACLE_LIB}")
            print(f"   المستخدم: {self.username}")
            print(f"   الخادم: {self.dsn}")

            # محاولة الاتصال بطرق مختلفة
            connection_string = f"{self.username}/{self.password}@{self.dsn}"

            try:
                self.connection = oracledb.connect(connection_string)
            except:
                # محاولة بطريقة أخرى
                self.connection = oracledb.connect(
                    user=self.username,
                    password=self.password,
                    dsn=self.dsn
                )

            self.cursor = self.connection.cursor()

            # اختبار الاتصال
            self.cursor.execute("SELECT USER FROM DUAL")
            current_user = self.cursor.fetchone()[0]

            print(f"✅ تم الاتصال بنجاح!")
            print(f"   المستخدم الحالي: {current_user}")

            try:
                print(f"   إصدار Oracle: {self.connection.version}")
            except:
                print("   إصدار Oracle: غير متاح")

            return True

        except Exception as e:
            print(f"❌ فشل في الاتصال بقاعدة البيانات:")
            print(f"   الخطأ: {e}")
            print(f"   نوع الخطأ: {type(e).__name__}")

            # محاولة تشخيص المشكلة
            print("\n🔍 تشخيص المشكلة:")
            print("   1. تأكد من أن خادم Oracle يعمل")
            print("   2. تأكد من صحة اسم المستخدم وكلمة المرور")
            print("   3. تأكد من صحة اسم الخدمة (DSN)")
            print("   4. تأكد من أن Oracle Client مثبت")

            return False
    
    def inspect_tables(self):
        """فحص جميع الجداول والأعمدة"""
        print("\n📋 فحص بنية الجداول...")
        
        try:
            # الحصول على قائمة الجداول
            tables_query = """
                SELECT table_name, num_rows, blocks, avg_row_len, last_analyzed
                FROM user_tables
                ORDER BY table_name
            """
            
            self.cursor.execute(tables_query)
            tables = self.cursor.fetchall()
            
            tables_info = []
            columns_info = []
            
            print(f"   تم العثور على {len(tables)} جدول")
            
            for table in tables:
                table_name = table[0]
                table_info = {
                    'table_name': table_name,
                    'num_rows': table[1],
                    'blocks': table[2],
                    'avg_row_len': table[3],
                    'last_analyzed': str(table[4]) if table[4] else None
                }
                tables_info.append(table_info)
                
                # فحص أعمدة كل جدول
                columns_query = """
                    SELECT column_name, data_type, data_length, data_precision, 
                           data_scale, nullable, data_default, column_id
                    FROM user_tab_columns
                    WHERE table_name = :table_name
                    ORDER BY column_id
                """
                
                self.cursor.execute(columns_query, {'table_name': table_name})
                columns = self.cursor.fetchall()
                
                for column in columns:
                    column_info = {
                        'table_name': table_name,
                        'column_name': column[0],
                        'data_type': column[1],
                        'data_length': column[2],
                        'data_precision': column[3],
                        'data_scale': column[4],
                        'nullable': column[5],
                        'data_default': str(column[6]) if column[6] else None,
                        'column_id': column[7]
                    }
                    columns_info.append(column_info)
                
                print(f"     ✓ {table_name}: {len(columns)} عمود")
            
            self.inspection_results['tables'] = tables_info
            self.inspection_results['columns'] = columns_info
            
            print(f"✅ تم فحص {len(tables)} جدول بإجمالي {len(columns_info)} عمود")
            
        except Exception as e:
            print(f"❌ خطأ في فحص الجداول: {e}")
    
    def inspect_indexes(self):
        """فحص جميع الفهارس"""
        print("\n🔍 فحص الفهارس...")
        
        try:
            # فحص الفهارس
            indexes_query = """
                SELECT index_name, table_name, index_type, uniqueness, 
                       status, num_rows, distinct_keys, blevel, leaf_blocks
                FROM user_indexes
                ORDER BY table_name, index_name
            """
            
            self.cursor.execute(indexes_query)
            indexes = self.cursor.fetchall()
            
            indexes_info = []
            index_columns_info = []
            
            for index in indexes:
                index_name = index[0]
                index_info = {
                    'index_name': index_name,
                    'table_name': index[1],
                    'index_type': index[2],
                    'uniqueness': index[3],
                    'status': index[4],
                    'num_rows': index[5],
                    'distinct_keys': index[6],
                    'blevel': index[7],
                    'leaf_blocks': index[8]
                }
                indexes_info.append(index_info)
                
                # فحص أعمدة الفهرس
                index_columns_query = """
                    SELECT column_name, column_position, descend
                    FROM user_ind_columns
                    WHERE index_name = :index_name
                    ORDER BY column_position
                """
                
                self.cursor.execute(index_columns_query, {'index_name': index_name})
                index_columns = self.cursor.fetchall()
                
                for col in index_columns:
                    index_col_info = {
                        'index_name': index_name,
                        'column_name': col[0],
                        'column_position': col[1],
                        'descend': col[2]
                    }
                    index_columns_info.append(index_col_info)
            
            self.inspection_results['indexes'] = indexes_info
            self.inspection_results['index_columns'] = index_columns_info
            
            print(f"✅ تم فحص {len(indexes)} فهرس")
            
        except Exception as e:
            print(f"❌ خطأ في فحص الفهارس: {e}")
    
    def inspect_constraints(self):
        """فحص جميع القيود والعلاقات"""
        print("\n🔗 فحص القيود والعلاقات...")
        
        try:
            # فحص القيود
            constraints_query = """
                SELECT constraint_name, table_name, constraint_type, 
                       status, deferrable, deferred, validated, 
                       search_condition, r_constraint_name
                FROM user_constraints
                ORDER BY table_name, constraint_type, constraint_name
            """
            
            self.cursor.execute(constraints_query)
            constraints = self.cursor.fetchall()
            
            constraints_info = []
            constraint_columns_info = []
            
            for constraint in constraints:
                constraint_name = constraint[0]
                constraint_info = {
                    'constraint_name': constraint_name,
                    'table_name': constraint[1],
                    'constraint_type': constraint[2],
                    'status': constraint[3],
                    'deferrable': constraint[4],
                    'deferred': constraint[5],
                    'validated': constraint[6],
                    'search_condition': str(constraint[7]) if constraint[7] else None,
                    'r_constraint_name': constraint[8]
                }
                constraints_info.append(constraint_info)
                
                # فحص أعمدة القيد
                constraint_columns_query = """
                    SELECT column_name, position
                    FROM user_cons_columns
                    WHERE constraint_name = :constraint_name
                    ORDER BY position
                """
                
                self.cursor.execute(constraint_columns_query, {'constraint_name': constraint_name})
                constraint_columns = self.cursor.fetchall()
                
                for col in constraint_columns:
                    constraint_col_info = {
                        'constraint_name': constraint_name,
                        'column_name': col[0],
                        'position': col[1]
                    }
                    constraint_columns_info.append(constraint_col_info)
            
            self.inspection_results['constraints'] = constraints_info
            self.inspection_results['constraint_columns'] = constraint_columns_info
            
            print(f"✅ تم فحص {len(constraints)} قيد")
            
        except Exception as e:
            print(f"❌ خطأ في فحص القيود: {e}")
    
    def inspect_sequences(self):
        """فحص المتسلسلات"""
        print("\n🔢 فحص المتسلسلات...")
        
        try:
            sequences_query = """
                SELECT sequence_name, min_value, max_value, increment_by,
                       cycle_flag, order_flag, cache_size, last_number
                FROM user_sequences
                ORDER BY sequence_name
            """
            
            self.cursor.execute(sequences_query)
            sequences = self.cursor.fetchall()
            
            sequences_info = []
            for seq in sequences:
                seq_info = {
                    'sequence_name': seq[0],
                    'min_value': seq[1],
                    'max_value': seq[2],
                    'increment_by': seq[3],
                    'cycle_flag': seq[4],
                    'order_flag': seq[5],
                    'cache_size': seq[6],
                    'last_number': seq[7]
                }
                sequences_info.append(seq_info)
            
            self.inspection_results['sequences'] = sequences_info
            
            print(f"✅ تم فحص {len(sequences)} متسلسلة")
            
        except Exception as e:
            print(f"❌ خطأ في فحص المتسلسلات: {e}")
    
    def run_full_inspection(self):
        """تشغيل فحص شامل"""
        print("🚀 بدء الفحص الشامل لقاعدة البيانات")
        print("=" * 60)
        
        if not self.connect():
            return False
        
        try:
            # معلومات عامة
            self.inspection_results['inspection_info'] = {
                'username': self.username,
                'dsn': self.dsn,
                'inspection_date': datetime.now().isoformat(),
                'oracle_version': self.connection.version
            }
            
            # تشغيل جميع عمليات الفحص
            self.inspect_tables()
            self.inspect_indexes()
            self.inspect_constraints()
            self.inspect_sequences()
            
            print("\n" + "=" * 60)
            print("✅ تم إكمال الفحص الشامل بنجاح!")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الفحص الشامل: {e}")
            return False
        
        finally:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            print("🔌 تم إغلاق الاتصال بقاعدة البيانات")
    
    def save_results(self, output_dir="oracle_inspection_results"):
        """حفظ نتائج الفحص"""
        try:
            # إنشاء مجلد النتائج
            Path(output_dir).mkdir(exist_ok=True)

            # حفظ النتائج كـ JSON
            json_file = f"{output_dir}/inspection_results.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.inspection_results, f, ensure_ascii=False, indent=2, default=str)

            print(f"\n💾 تم حفظ النتائج:")
            print(f"   📄 JSON: {json_file}")

            # حفظ الجداول كـ Excel إذا كانت pandas متاحة
            if PANDAS_AVAILABLE:
                try:
                    excel_file = f"{output_dir}/database_structure.xlsx"
                    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                        if 'tables' in self.inspection_results:
                            pd.DataFrame(self.inspection_results['tables']).to_excel(
                                writer, sheet_name='Tables', index=False)

                        if 'columns' in self.inspection_results:
                            pd.DataFrame(self.inspection_results['columns']).to_excel(
                                writer, sheet_name='Columns', index=False)

                        if 'indexes' in self.inspection_results:
                            pd.DataFrame(self.inspection_results['indexes']).to_excel(
                                writer, sheet_name='Indexes', index=False)

                        if 'constraints' in self.inspection_results:
                            pd.DataFrame(self.inspection_results['constraints']).to_excel(
                                writer, sheet_name='Constraints', index=False)

                        if 'sequences' in self.inspection_results:
                            pd.DataFrame(self.inspection_results['sequences']).to_excel(
                                writer, sheet_name='Sequences', index=False)

                    print(f"   📊 Excel: {excel_file}")
                except Exception as e:
                    print(f"   ⚠️ تعذر إنشاء ملف Excel: {e}")

            # حفظ ملف نصي مبسط
            text_file = f"{output_dir}/database_summary.txt"
            with open(text_file, 'w', encoding='utf-8') as f:
                f.write("تقرير فحص قاعدة البيانات\n")
                f.write("=" * 50 + "\n\n")

                if 'inspection_info' in self.inspection_results:
                    info = self.inspection_results['inspection_info']
                    f.write(f"المستخدم: {info.get('username', 'غير محدد')}\n")
                    f.write(f"الخادم: {info.get('dsn', 'غير محدد')}\n")
                    f.write(f"تاريخ الفحص: {info.get('inspection_date', 'غير محدد')}\n\n")

                if 'tables' in self.inspection_results:
                    f.write(f"عدد الجداول: {len(self.inspection_results['tables'])}\n")
                if 'columns' in self.inspection_results:
                    f.write(f"عدد الأعمدة: {len(self.inspection_results['columns'])}\n")
                if 'indexes' in self.inspection_results:
                    f.write(f"عدد الفهارس: {len(self.inspection_results['indexes'])}\n")
                if 'constraints' in self.inspection_results:
                    f.write(f"عدد القيود: {len(self.inspection_results['constraints'])}\n")
                if 'sequences' in self.inspection_results:
                    f.write(f"عدد المتسلسلات: {len(self.inspection_results['sequences'])}\n")

            print(f"   📝 ملخص نصي: {text_file}")

            return True

        except Exception as e:
            print(f"❌ خطأ في حفظ النتائج: {e}")
            return False


def main():
    """الدالة الرئيسية"""
    # معلومات الاتصال
    username = "ias20241"
    password = "ys123"
    dsn = "yemensoft"
    
    # إنشاء فاحص قاعدة البيانات
    inspector = OracleDatabaseInspector(username, password, dsn)
    
    # تشغيل الفحص الشامل
    if inspector.run_full_inspection():
        # حفظ النتائج
        inspector.save_results()
        
        # طباعة ملخص النتائج
        print("\n📊 ملخص النتائج:")
        results = inspector.inspection_results
        
        if 'tables' in results:
            print(f"   📋 الجداول: {len(results['tables'])}")
        if 'columns' in results:
            print(f"   📝 الأعمدة: {len(results['columns'])}")
        if 'indexes' in results:
            print(f"   🔍 الفهارس: {len(results['indexes'])}")
        if 'constraints' in results:
            print(f"   🔗 القيود: {len(results['constraints'])}")
        if 'sequences' in results:
            print(f"   🔢 المتسلسلات: {len(results['sequences'])}")
        
        print("\n🎉 تم إكمال فحص قاعدة البيانات بنجاح!")
        return True
    else:
        print("\n💥 فشل في فحص قاعدة البيانات!")
        return False


if __name__ == "__main__":
    main()
