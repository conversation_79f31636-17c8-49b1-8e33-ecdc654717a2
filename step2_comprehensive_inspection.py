#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 2: فحص شامل لبنية ias20241
Step 2: Comprehensive inspection of ias20241 structure
"""

import os
import cx_Oracle
import json
from pathlib import Path
from datetime import datetime


class ComprehensiveInspector:
    """فاحص شامل لبنية قاعدة البيانات"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.conn = None
        self.cursor = None
        self.inspection_data = {
            'inspection_date': datetime.now().isoformat(),
            'source_user': 'ias20241',
            'tables': {},
            'indexes': {},
            'constraints': {},
            'sequences': {},
            'views': {},
            'procedures': {},
            'functions': {},
            'packages': {},
            'triggers': {},
            'synonyms': {},
            'types': {},
            'statistics': {}
        }
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            print("🔌 الاتصال بـ ias20241...")
            self.conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.cursor = self.conn.cursor()
            
            # التحقق من الاتصال
            self.cursor.execute("SELECT USER, SYSDATE FROM DUAL")
            user, sysdate = self.cursor.fetchone()
            
            print(f"✅ تم الاتصال بـ {user} في {sysdate}")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def inspect_tables(self):
        """فحص الجداول"""
        print("\n📋 فحص الجداول...")
        
        try:
            # الحصول على معلومات الجداول الأساسية
            self.cursor.execute("""
                SELECT table_name, num_rows, blocks, avg_row_len, 
                       last_analyzed, tablespace_name, temporary,
                       cluster_name, iot_name, iot_type, partitioned,
                       status, compression, compress_for
                FROM user_tables
                WHERE table_name NOT LIKE 'BIN$%'
                ORDER BY table_name
            """)
            
            tables_basic = self.cursor.fetchall()
            print(f"   📊 تم العثور على {len(tables_basic)} جدول")
            
            for table_data in tables_basic:
                table_name = table_data[0]
                
                # معلومات الجدول الأساسية
                table_info = {
                    'name': table_name,
                    'num_rows': table_data[1] or 0,
                    'blocks': table_data[2] or 0,
                    'avg_row_len': table_data[3] or 0,
                    'last_analyzed': str(table_data[4]) if table_data[4] else None,
                    'tablespace_name': table_data[5],
                    'temporary': table_data[6],
                    'cluster_name': table_data[7],
                    'iot_name': table_data[8],
                    'iot_type': table_data[9],
                    'partitioned': table_data[10],
                    'status': table_data[11],
                    'compression': table_data[12],
                    'compress_for': table_data[13],
                    'columns': [],
                    'primary_key': None,
                    'foreign_keys': [],
                    'check_constraints': [],
                    'unique_constraints': [],
                    'indexes': [],
                    'triggers': [],
                    'comments': None
                }
                
                # فحص الأعمدة
                self.inspect_table_columns(table_name, table_info)
                
                # فحص التعليقات
                self.inspect_table_comments(table_name, table_info)
                
                self.inspection_data['tables'][table_name] = table_info
            
            print(f"✅ تم فحص {len(self.inspection_data['tables'])} جدول")
            
        except Exception as e:
            print(f"❌ خطأ في فحص الجداول: {e}")
    
    def inspect_table_columns(self, table_name, table_info):
        """فحص أعمدة الجدول"""
        try:
            self.cursor.execute("""
                SELECT column_name, data_type, data_length, data_precision,
                       data_scale, nullable, column_id, data_default,
                       num_distinct, low_value, high_value, density,
                       num_nulls, num_buckets, last_analyzed, avg_col_len,
                       char_length, char_used, virtual_column, hidden_column
                FROM user_tab_columns
                WHERE table_name = :table_name
                ORDER BY column_id
            """, {'table_name': table_name})
            
            columns = self.cursor.fetchall()
            
            for col_data in columns:
                column_info = {
                    'name': col_data[0],
                    'data_type': col_data[1],
                    'data_length': col_data[2],
                    'data_precision': col_data[3],
                    'data_scale': col_data[4],
                    'nullable': col_data[5],
                    'column_id': col_data[6],
                    'data_default': col_data[7],
                    'num_distinct': col_data[8],
                    'low_value': col_data[9],
                    'high_value': col_data[10],
                    'density': col_data[11],
                    'num_nulls': col_data[12],
                    'num_buckets': col_data[13],
                    'last_analyzed': str(col_data[14]) if col_data[14] else None,
                    'avg_col_len': col_data[15],
                    'char_length': col_data[16],
                    'char_used': col_data[17],
                    'virtual_column': col_data[18],
                    'hidden_column': col_data[19],
                    'comments': None
                }
                
                # فحص تعليقات العمود
                self.cursor.execute("""
                    SELECT comments FROM user_col_comments
                    WHERE table_name = :table_name AND column_name = :column_name
                """, {'table_name': table_name, 'column_name': col_data[0]})
                
                comment_result = self.cursor.fetchone()
                if comment_result and comment_result[0]:
                    column_info['comments'] = comment_result[0]
                
                table_info['columns'].append(column_info)
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص أعمدة {table_name}: {e}")
    
    def inspect_table_comments(self, table_name, table_info):
        """فحص تعليقات الجدول"""
        try:
            self.cursor.execute("""
                SELECT comments FROM user_tab_comments
                WHERE table_name = :table_name
            """, {'table_name': table_name})
            
            result = self.cursor.fetchone()
            if result and result[0]:
                table_info['comments'] = result[0]
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص تعليقات {table_name}: {e}")
    
    def inspect_indexes(self):
        """فحص الفهارس"""
        print("\n🔍 فحص الفهارس...")
        
        try:
            self.cursor.execute("""
                SELECT index_name, index_type, table_name, uniqueness,
                       compression, prefix_length, tablespace_name,
                       ini_trans, max_trans, initial_extent, next_extent,
                       min_extents, max_extents, pct_increase, pct_threshold,
                       include_column, freelists, freelist_groups,
                       pct_free, logging, blevel, leaf_blocks, distinct_keys,
                       avg_leaf_blocks_per_key, avg_data_blocks_per_key,
                       clustering_factor, status, num_rows, sample_size,
                       last_analyzed, degree, instances, partitioned,
                       temporary, generated, secondary, buffer_pool,
                       flash_cache, cell_flash_cache, user_stats,
                       duration, pct_direct_access, ityp_owner, ityp_name,
                       parameters, global_stats, domidx_status,
                       domidx_opstatus, funcidx_status, join_index,
                       iot_redundant_pkey_elim, dropped, visibility,
                       domidx_management, segment_created
                FROM user_indexes
                ORDER BY table_name, index_name
            """)
            
            indexes = self.cursor.fetchall()
            print(f"   📊 تم العثور على {len(indexes)} فهرس")
            
            for idx_data in indexes:
                index_name = idx_data[0]
                
                index_info = {
                    'name': index_name,
                    'type': idx_data[1],
                    'table_name': idx_data[2],
                    'uniqueness': idx_data[3],
                    'compression': idx_data[4],
                    'prefix_length': idx_data[5],
                    'tablespace_name': idx_data[6],
                    'status': idx_data[25],
                    'num_rows': idx_data[26],
                    'last_analyzed': str(idx_data[28]) if idx_data[28] else None,
                    'partitioned': idx_data[31],
                    'visibility': idx_data[45],
                    'columns': []
                }
                
                # فحص أعمدة الفهرس
                self.inspect_index_columns(index_name, index_info)
                
                self.inspection_data['indexes'][index_name] = index_info
            
            print(f"✅ تم فحص {len(self.inspection_data['indexes'])} فهرس")
            
        except Exception as e:
            print(f"❌ خطأ في فحص الفهارس: {e}")
    
    def inspect_index_columns(self, index_name, index_info):
        """فحص أعمدة الفهرس"""
        try:
            self.cursor.execute("""
                SELECT column_name, column_position, column_length,
                       char_length, descend
                FROM user_ind_columns
                WHERE index_name = :index_name
                ORDER BY column_position
            """, {'index_name': index_name})
            
            columns = self.cursor.fetchall()
            
            for col_data in columns:
                column_info = {
                    'name': col_data[0],
                    'position': col_data[1],
                    'length': col_data[2],
                    'char_length': col_data[3],
                    'descend': col_data[4]
                }
                index_info['columns'].append(column_info)
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص أعمدة الفهرس {index_name}: {e}")
    
    def inspect_constraints(self):
        """فحص القيود"""
        print("\n🔒 فحص القيود...")
        
        try:
            self.cursor.execute("""
                SELECT constraint_name, constraint_type, table_name,
                       search_condition, r_owner, r_constraint_name,
                       delete_rule, status, deferrable, deferred,
                       validated, generated, bad, rely, last_change,
                       index_owner, index_name, invalid, view_related
                FROM user_constraints
                ORDER BY table_name, constraint_type, constraint_name
            """)
            
            constraints = self.cursor.fetchall()
            print(f"   📊 تم العثور على {len(constraints)} قيد")
            
            for cons_data in constraints:
                constraint_name = cons_data[0]
                
                constraint_info = {
                    'name': constraint_name,
                    'type': cons_data[1],
                    'table_name': cons_data[2],
                    'search_condition': cons_data[3],
                    'r_owner': cons_data[4],
                    'r_constraint_name': cons_data[5],
                    'delete_rule': cons_data[6],
                    'status': cons_data[7],
                    'deferrable': cons_data[8],
                    'deferred': cons_data[9],
                    'validated': cons_data[10],
                    'generated': cons_data[11],
                    'columns': []
                }
                
                # فحص أعمدة القيد
                self.inspect_constraint_columns(constraint_name, constraint_info)
                
                self.inspection_data['constraints'][constraint_name] = constraint_info
            
            print(f"✅ تم فحص {len(self.inspection_data['constraints'])} قيد")
            
        except Exception as e:
            print(f"❌ خطأ في فحص القيود: {e}")
    
    def inspect_constraint_columns(self, constraint_name, constraint_info):
        """فحص أعمدة القيد"""
        try:
            self.cursor.execute("""
                SELECT column_name, position
                FROM user_cons_columns
                WHERE constraint_name = :constraint_name
                ORDER BY position
            """, {'constraint_name': constraint_name})
            
            columns = self.cursor.fetchall()
            
            for col_data in columns:
                column_info = {
                    'name': col_data[0],
                    'position': col_data[1]
                }
                constraint_info['columns'].append(column_info)
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص أعمدة القيد {constraint_name}: {e}")
    
    def inspect_sequences(self):
        """فحص المتسلسلات"""
        print("\n🔢 فحص المتسلسلات...")
        
        try:
            self.cursor.execute("""
                SELECT sequence_name, min_value, max_value, increment_by,
                       cycle_flag, order_flag, cache_size, last_number
                FROM user_sequences
                ORDER BY sequence_name
            """)
            
            sequences = self.cursor.fetchall()
            print(f"   📊 تم العثور على {len(sequences)} متسلسلة")
            
            for seq_data in sequences:
                sequence_name = seq_data[0]
                
                sequence_info = {
                    'name': sequence_name,
                    'min_value': seq_data[1],
                    'max_value': seq_data[2],
                    'increment_by': seq_data[3],
                    'cycle_flag': seq_data[4],
                    'order_flag': seq_data[5],
                    'cache_size': seq_data[6],
                    'last_number': seq_data[7]
                }
                
                self.inspection_data['sequences'][sequence_name] = sequence_info
            
            print(f"✅ تم فحص {len(self.inspection_data['sequences'])} متسلسلة")
            
        except Exception as e:
            print(f"❌ خطأ في فحص المتسلسلات: {e}")
    
    def inspect_views(self):
        """فحص المشاهدات"""
        print("\n👁️ فحص المشاهدات...")
        
        try:
            self.cursor.execute("""
                SELECT view_name, text_length, text, type_text_length,
                       type_text, oid_text_length, oid_text, view_type_owner,
                       view_type, superview_name, editioning_view,
                       read_only
                FROM user_views
                ORDER BY view_name
            """)
            
            views = self.cursor.fetchall()
            print(f"   📊 تم العثور على {len(views)} مشاهدة")
            
            for view_data in views:
                view_name = view_data[0]
                
                view_info = {
                    'name': view_name,
                    'text_length': view_data[1],
                    'text': view_data[2].read() if view_data[2] else None,
                    'view_type': view_data[8],
                    'read_only': view_data[11]
                }
                
                self.inspection_data['views'][view_name] = view_info
            
            print(f"✅ تم فحص {len(self.inspection_data['views'])} مشاهدة")
            
        except Exception as e:
            print(f"❌ خطأ في فحص المشاهدات: {e}")
    
    def generate_statistics(self):
        """إنشاء إحصائيات شاملة"""
        print("\n📊 إنشاء الإحصائيات...")
        
        stats = {
            'total_tables': len(self.inspection_data['tables']),
            'total_indexes': len(self.inspection_data['indexes']),
            'total_constraints': len(self.inspection_data['constraints']),
            'total_sequences': len(self.inspection_data['sequences']),
            'total_views': len(self.inspection_data['views']),
            'constraint_types': {},
            'index_types': {},
            'largest_tables': [],
            'total_rows': 0,
            'total_columns': 0
        }
        
        # إحصائيات الجداول
        table_sizes = []
        for table_name, table_info in self.inspection_data['tables'].items():
            num_rows = table_info['num_rows']
            stats['total_rows'] += num_rows
            stats['total_columns'] += len(table_info['columns'])
            
            table_sizes.append({
                'name': table_name,
                'rows': num_rows,
                'columns': len(table_info['columns'])
            })
        
        # أكبر الجداول
        stats['largest_tables'] = sorted(table_sizes, key=lambda x: x['rows'], reverse=True)[:10]
        
        # إحصائيات القيود
        for constraint_info in self.inspection_data['constraints'].values():
            constraint_type = constraint_info['type']
            stats['constraint_types'][constraint_type] = stats['constraint_types'].get(constraint_type, 0) + 1
        
        # إحصائيات الفهارس
        for index_info in self.inspection_data['indexes'].values():
            index_type = index_info['type']
            stats['index_types'][index_type] = stats['index_types'].get(index_type, 0) + 1
        
        self.inspection_data['statistics'] = stats
        
        print(f"✅ تم إنشاء الإحصائيات")
    
    def save_inspection_results(self):
        """حفظ نتائج الفحص"""
        print("\n💾 حفظ نتائج الفحص...")
        
        # إنشاء مجلد النتائج
        results_dir = Path("inspection_results")
        results_dir.mkdir(exist_ok=True)
        
        # حفظ البيانات الكاملة كـ JSON
        json_file = results_dir / f"ias20241_complete_inspection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.inspection_data, f, ensure_ascii=False, indent=2, default=str)
        
        # حفظ تقرير مختصر
        summary_file = results_dir / f"ias20241_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("تقرير فحص شامل لقاعدة البيانات ias20241\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"تاريخ الفحص: {self.inspection_data['inspection_date']}\n\n")
            
            stats = self.inspection_data['statistics']
            f.write("📊 الإحصائيات العامة:\n")
            f.write(f"   الجداول: {stats['total_tables']}\n")
            f.write(f"   الفهارس: {stats['total_indexes']}\n")
            f.write(f"   القيود: {stats['total_constraints']}\n")
            f.write(f"   المتسلسلات: {stats['total_sequences']}\n")
            f.write(f"   المشاهدات: {stats['total_views']}\n")
            f.write(f"   إجمالي الصفوف: {stats['total_rows']:,}\n")
            f.write(f"   إجمالي الأعمدة: {stats['total_columns']:,}\n\n")
            
            f.write("📋 أكبر الجداول:\n")
            for i, table in enumerate(stats['largest_tables'][:10], 1):
                f.write(f"   {i:2d}. {table['name']}: {table['rows']:,} صف, {table['columns']} عمود\n")
        
        print(f"✅ تم حفظ النتائج:")
        print(f"   📄 البيانات الكاملة: {json_file}")
        print(f"   📝 التقرير المختصر: {summary_file}")
        
        return json_file, summary_file
    
    def run_comprehensive_inspection(self):
        """تشغيل الفحص الشامل"""
        print("🚀 بدء الفحص الشامل لبنية ias20241")
        print("=" * 60)
        
        if not self.connect():
            return False
        
        try:
            # فحص جميع المكونات
            self.inspect_tables()
            self.inspect_indexes()
            self.inspect_constraints()
            self.inspect_sequences()
            self.inspect_views()
            
            # إنشاء الإحصائيات
            self.generate_statistics()
            
            # حفظ النتائج
            json_file, summary_file = self.save_inspection_results()
            
            print("\n🎉 تم إكمال الفحص الشامل بنجاح!")
            return True
            
        except Exception as e:
            print(f"\n❌ خطأ في الفحص الشامل: {e}")
            return False
            
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()


def main():
    """الدالة الرئيسية"""
    inspector = ComprehensiveInspector()
    success = inspector.run_comprehensive_inspection()
    
    if success:
        print("\n✅ الخطوة 2 مكتملة - تم فحص بنية ias20241 بالكامل")
    else:
        print("\n❌ فشل في الخطوة 2")
    
    return success


if __name__ == "__main__":
    main()
