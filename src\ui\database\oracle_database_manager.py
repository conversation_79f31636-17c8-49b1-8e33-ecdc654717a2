# -*- coding: utf-8 -*-
"""
نظام إدارة قاعدة بيانات Oracle المتقدم
Advanced Oracle Database Management System
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QGroupBox, QGridLayout, QLabel,
                               QPushButton, QLineEdit, QSpinBox, QComboBox,
                               QCheckBox, QTextEdit, QProgressBar, QMessageBox,
                               QFileDialog, QTableWidget, QTableWidgetItem,
                               QHeaderView, QAbstractItemView, QFrame,
                               QScrollArea, QSlider, QDoubleSpinBox,
                               QDateTimeEdit, QToolBar, QStatusBar, QSplitter,
                               QTreeWidget, QTreeWidgetItem, QPlainTextEdit)
from PySide6.QtCore import Qt, <PERSON><PERSON><PERSON><PERSON>, Signal, QThread, QDateTime
from PySide6.QtGui import QFont, QIcon, QPixmap, QColor, QPalette, QTextCharFormat, QSyntaxHighlighter

import cx_Oracle
import json
import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime, timedelta
import psutil
import threading
import time
import re

from ...utils.arabic_support import reshape_arabic_text


class SQLSyntaxHighlighter(QSyntaxHighlighter):
    """مُلون بناء جملة SQL"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # تعريف الألوان والأنماط
        self.highlighting_rules = []
        
        # كلمات SQL المحجوزة
        keyword_format = QTextCharFormat()
        keyword_format.setColor(QColor(0, 0, 255))
        keyword_format.setFontWeight(QFont.Bold)
        
        keywords = [
            "SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", "CREATE", "DROP",
            "ALTER", "TABLE", "INDEX", "VIEW", "PROCEDURE", "FUNCTION", "PACKAGE",
            "TRIGGER", "SEQUENCE", "CONSTRAINT", "PRIMARY", "FOREIGN", "KEY", "UNIQUE",
            "NOT", "NULL", "AND", "OR", "ORDER", "BY", "GROUP", "HAVING", "JOIN",
            "INNER", "LEFT", "RIGHT", "OUTER", "UNION", "DISTINCT", "COUNT", "SUM",
            "AVG", "MAX", "MIN", "CASE", "WHEN", "THEN", "ELSE", "END", "IF", "ELSIF",
            "LOOP", "FOR", "WHILE", "BEGIN", "END", "DECLARE", "CURSOR", "EXCEPTION",
            "COMMIT", "ROLLBACK", "SAVEPOINT", "GRANT", "REVOKE", "CONNECT", "RESOURCE"
        ]
        
        for keyword in keywords:
            pattern = f"\\b{keyword}\\b"
            self.highlighting_rules.append((re.compile(pattern, re.IGNORECASE), keyword_format))
        
        # التعليقات
        comment_format = QTextCharFormat()
        comment_format.setColor(QColor(0, 128, 0))
        comment_format.setFontItalic(True)
        self.highlighting_rules.append((re.compile("--[^\n]*"), comment_format))
        self.highlighting_rules.append((re.compile("/\\*.*\\*/", re.DOTALL), comment_format))
        
        # النصوص
        string_format = QTextCharFormat()
        string_format.setColor(QColor(255, 0, 0))
        self.highlighting_rules.append((re.compile("'[^']*'"), string_format))
        
        # الأرقام
        number_format = QTextCharFormat()
        number_format.setColor(QColor(255, 165, 0))
        self.highlighting_rules.append((re.compile("\\b\\d+(\\.\\d+)?\\b"), number_format))
    
    def highlightBlock(self, text):
        """تطبيق التلوين على النص"""
        for pattern, format in self.highlighting_rules:
            for match in pattern.finditer(text):
                start, end = match.span()
                self.setFormat(start, end - start, format)


class OracleDatabaseThread(QThread):
    """خيط تنفيذ عمليات قاعدة البيانات"""
    progress_updated = Signal(int)
    status_updated = Signal(str)
    result_ready = Signal(object)
    error_occurred = Signal(str)
    
    def __init__(self, operation, connection_params, query=None, options=None):
        super().__init__()
        self.operation = operation
        self.connection_params = connection_params
        self.query = query
        self.options = options or {}
        
    def run(self):
        try:
            if self.operation == "execute_query":
                self.execute_query()
            elif self.operation == "get_database_info":
                self.get_database_info()
            elif self.operation == "backup_schema":
                self.backup_schema()
            elif self.operation == "analyze_performance":
                self.analyze_performance()
            elif self.operation == "manage_users":
                self.manage_users()
            elif self.operation == "monitor_sessions":
                self.monitor_sessions()
                
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def execute_query(self):
        """تنفيذ استعلام SQL"""
        try:
            self.status_updated.emit("الاتصال بقاعدة البيانات...")
            conn = cx_Oracle.connect(**self.connection_params)
            cursor = conn.cursor()
            
            self.progress_updated.emit(25)
            self.status_updated.emit("تنفيذ الاستعلام...")
            
            # تنفيذ الاستعلام
            cursor.execute(self.query)
            
            self.progress_updated.emit(75)
            
            # الحصول على النتائج
            if cursor.description:
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()
                result = {'columns': columns, 'rows': rows, 'type': 'select'}
            else:
                result = {'message': 'تم تنفيذ الاستعلام بنجاح', 'type': 'dml'}
                conn.commit()
            
            self.progress_updated.emit(100)
            self.result_ready.emit(result)
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            self.error_occurred.emit(f"خطأ في تنفيذ الاستعلام: {str(e)}")
    
    def get_database_info(self):
        """الحصول على معلومات قاعدة البيانات"""
        try:
            self.status_updated.emit("جمع معلومات قاعدة البيانات...")
            conn = cx_Oracle.connect(**self.connection_params)
            cursor = conn.cursor()
            
            info = {}
            
            # معلومات عامة
            self.progress_updated.emit(20)
            cursor.execute("SELECT * FROM v$version WHERE ROWNUM = 1")
            info['oracle_version'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT name FROM v$database")
            info['database_name'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT username FROM user_users")
            info['current_user'] = cursor.fetchone()[0]
            
            # إحصائيات الجداول
            self.progress_updated.emit(40)
            cursor.execute("SELECT COUNT(*) FROM user_tables")
            info['tables_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM user_views")
            info['views_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM user_indexes")
            info['indexes_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM user_sequences")
            info['sequences_count'] = cursor.fetchone()[0]
            
            # إحصائيات الكائنات
            self.progress_updated.emit(60)
            cursor.execute("""
                SELECT object_type, COUNT(*) 
                FROM user_objects 
                WHERE status = 'VALID'
                GROUP BY object_type
                ORDER BY object_type
            """)
            info['objects_by_type'] = dict(cursor.fetchall())
            
            # حجم البيانات
            self.progress_updated.emit(80)
            cursor.execute("""
                SELECT SUM(bytes)/1024/1024 as size_mb 
                FROM user_segments
            """)
            result = cursor.fetchone()
            info['data_size_mb'] = result[0] if result[0] else 0
            
            self.progress_updated.emit(100)
            self.result_ready.emit(info)
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            self.error_occurred.emit(f"خطأ في جمع معلومات قاعدة البيانات: {str(e)}")
    
    def backup_schema(self):
        """نسخ احتياطي للمخطط"""
        try:
            self.status_updated.emit("بدء النسخ الاحتياطي...")
            # هنا يمكن تنفيذ عملية النسخ الاحتياطي باستخدام Data Pump أو أدوات أخرى
            self.progress_updated.emit(100)
            self.result_ready.emit("تم إنشاء النسخة الاحتياطية بنجاح")
            
        except Exception as e:
            self.error_occurred.emit(f"خطأ في النسخ الاحتياطي: {str(e)}")


class OracleDatabaseManager(QMainWindow):
    """نظام إدارة قاعدة بيانات Oracle المتقدم"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("نظام إدارة قاعدة بيانات Oracle المتقدم - ProShipment")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        # متغيرات النافذة
        self.connection_params = {
            'user': 'ship2025',
            'password': 'ys123',
            'dsn': 'yemensoft',
            'encoding': 'UTF-8'
        }
        
        self.db_thread = None
        self.monitoring_timer = QTimer()
        self.query_history = []
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # تحميل الإعدادات وبدء المراقبة
        self.load_settings()
        self.start_monitoring()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        self.create_header_section(layout)
        
        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        
        # تبويب لوحة التحكم
        dashboard_tab = self.create_dashboard_tab()
        self.tabs.addTab(dashboard_tab, "🏠 لوحة التحكم")
        
        # تبويب محرر SQL
        sql_editor_tab = self.create_sql_editor_tab()
        self.tabs.addTab(sql_editor_tab, "📝 محرر SQL")
        
        # تبويب إدارة الكائنات
        objects_tab = self.create_objects_management_tab()
        self.tabs.addTab(objects_tab, "🗂️ إدارة الكائنات")
        
        # تبويب مراقبة الأداء
        performance_tab = self.create_performance_monitoring_tab()
        self.tabs.addTab(performance_tab, "📊 مراقبة الأداء")
        
        # تبويب إدارة المستخدمين
        users_tab = self.create_users_management_tab()
        self.tabs.addTab(users_tab, "👥 إدارة المستخدمين")
        
        # تبويب النسخ الاحتياطية
        backup_tab = self.create_backup_management_tab()
        self.tabs.addTab(backup_tab, "💾 النسخ الاحتياطية")
        
        # تبويب الأمان والمراجعة
        security_tab = self.create_security_audit_tab()
        self.tabs.addTab(security_tab, "🔒 الأمان والمراجعة")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ الإعدادات")
        
        layout.addWidget(self.tabs)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1e3a8a, stop:0.5 #3b82f6, stop:1 #60a5fa);
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة Oracle
        icon_label = QLabel("🗄️")
        icon_label.setStyleSheet("font-size: 42px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات العنوان
        info_layout = QVBoxLayout()
        
        title_label = QLabel("نظام إدارة قاعدة بيانات Oracle المتقدم")
        title_label.setFont(QFont("Arial", 22, QFont.Bold))
        title_label.setStyleSheet("color: white; margin-bottom: 5px;")
        
        subtitle_label = QLabel("إدارة شاملة ومتقدمة لقاعدة بيانات Oracle مع أدوات احترافية")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: #e0e7ff;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # معلومات سريعة عن قاعدة البيانات
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        
        stats_layout = QGridLayout(stats_frame)
        
        self.db_name_label = QLabel("اسم قاعدة البيانات\nship2025")
        self.db_status_label = QLabel("حالة الاتصال\n🟢 متصل")
        self.db_version_label = QLabel("إصدار Oracle\nOracle 19c")
        self.db_objects_label = QLabel("عدد الكائنات\n0")
        
        for i, label in enumerate([self.db_name_label, self.db_status_label, 
                                 self.db_version_label, self.db_objects_label]):
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    font-size: 11px;
                    padding: 5px;
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(label, 0, i)
        
        header_layout.addWidget(stats_frame)
        
        layout.addWidget(header_frame)
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات قاعدة البيانات
        db_info_group = QGroupBox("معلومات قاعدة البيانات")
        db_info_layout = QGridLayout(db_info_group)
        
        # معلومات الاتصال
        db_info_layout.addWidget(QLabel("المستخدم:"), 0, 0)
        self.current_user_label = QLabel(self.connection_params['user'])
        self.current_user_label.setStyleSheet("font-weight: bold; color: #1e40af;")
        db_info_layout.addWidget(self.current_user_label, 0, 1)
        
        db_info_layout.addWidget(QLabel("قاعدة البيانات:"), 0, 2)
        self.database_name_label = QLabel(self.connection_params['dsn'])
        self.database_name_label.setStyleSheet("font-weight: bold; color: #1e40af;")
        db_info_layout.addWidget(self.database_name_label, 0, 3)
        
        # إحصائيات سريعة
        db_info_layout.addWidget(QLabel("عدد الجداول:"), 1, 0)
        self.tables_count_label = QLabel("0")
        db_info_layout.addWidget(self.tables_count_label, 1, 1)
        
        db_info_layout.addWidget(QLabel("عدد المشاهد:"), 1, 2)
        self.views_count_label = QLabel("0")
        db_info_layout.addWidget(self.views_count_label, 1, 3)
        
        db_info_layout.addWidget(QLabel("حجم البيانات:"), 2, 0)
        self.data_size_label = QLabel("0 MB")
        db_info_layout.addWidget(self.data_size_label, 2, 1)
        
        db_info_layout.addWidget(QLabel("آخر تحديث:"), 2, 2)
        self.last_update_label = QLabel(datetime.now().strftime("%Y-%m-%d %H:%M"))
        db_info_layout.addWidget(self.last_update_label, 2, 3)
        
        layout.addWidget(db_info_group)
        
        # أزرار الإجراءات السريعة
        quick_actions_group = QGroupBox("إجراءات سريعة")
        quick_actions_layout = QGridLayout(quick_actions_group)
        
        # اختبار الاتصال
        test_connection_btn = QPushButton("🔌 اختبار الاتصال")
        test_connection_btn.clicked.connect(self.test_connection)
        quick_actions_layout.addWidget(test_connection_btn, 0, 0)
        
        # تحديث المعلومات
        refresh_info_btn = QPushButton("🔄 تحديث المعلومات")
        refresh_info_btn.clicked.connect(self.refresh_database_info)
        quick_actions_layout.addWidget(refresh_info_btn, 0, 1)
        
        # نسخة احتياطية سريعة
        quick_backup_btn = QPushButton("💾 نسخة احتياطية سريعة")
        quick_backup_btn.clicked.connect(self.create_quick_backup)
        quick_actions_layout.addWidget(quick_backup_btn, 0, 2)
        
        # تحسين الأداء
        optimize_btn = QPushButton("⚡ تحسين الأداء")
        optimize_btn.clicked.connect(self.optimize_performance)
        quick_actions_layout.addWidget(optimize_btn, 1, 0)
        
        # مراقبة الجلسات
        monitor_sessions_btn = QPushButton("👥 مراقبة الجلسات")
        monitor_sessions_btn.clicked.connect(self.monitor_sessions)
        quick_actions_layout.addWidget(monitor_sessions_btn, 1, 1)
        
        # تقرير شامل
        generate_report_btn = QPushButton("📊 تقرير شامل")
        generate_report_btn.clicked.connect(self.generate_comprehensive_report)
        quick_actions_layout.addWidget(generate_report_btn, 1, 2)
        
        layout.addWidget(quick_actions_group)
        
        # جدول الكائنات الحديثة
        recent_objects_group = QGroupBox("الكائنات المُحدثة مؤخراً")
        recent_objects_layout = QVBoxLayout(recent_objects_group)
        
        self.recent_objects_table = QTableWidget()
        self.setup_recent_objects_table()
        recent_objects_layout.addWidget(self.recent_objects_table)
        
        layout.addWidget(recent_objects_group)
        
        return tab
    
    def create_sql_editor_tab(self):
        """إنشاء تبويب محرر SQL"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # شريط أدوات المحرر
        editor_toolbar = QFrame()
        editor_toolbar_layout = QHBoxLayout(editor_toolbar)
        
        # أزرار التحكم
        execute_btn = QPushButton("▶️ تنفيذ (F5)")
        execute_btn.setShortcut("F5")
        execute_btn.clicked.connect(self.execute_sql_query)
        execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        editor_toolbar_layout.addWidget(execute_btn)
        
        clear_btn = QPushButton("🗑️ مسح")
        clear_btn.clicked.connect(self.clear_sql_editor)
        editor_toolbar_layout.addWidget(clear_btn)
        
        save_query_btn = QPushButton("💾 حفظ الاستعلام")
        save_query_btn.clicked.connect(self.save_sql_query)
        editor_toolbar_layout.addWidget(save_query_btn)
        
        load_query_btn = QPushButton("📁 تحميل استعلام")
        load_query_btn.clicked.connect(self.load_sql_query)
        editor_toolbar_layout.addWidget(load_query_btn)
        
        editor_toolbar_layout.addStretch()
        
        # معلومات الاستعلام
        self.query_info_label = QLabel("جاهز")
        self.query_info_label.setStyleSheet("color: #059669; font-weight: bold;")
        editor_toolbar_layout.addWidget(self.query_info_label)
        
        layout.addWidget(editor_toolbar)
        
        # المحرر والنتائج
        splitter = QSplitter(Qt.Vertical)
        
        # محرر SQL
        editor_group = QGroupBox("محرر SQL")
        editor_layout = QVBoxLayout(editor_group)
        
        self.sql_editor = QPlainTextEdit()
        self.sql_editor.setPlainText("-- اكتب استعلام SQL هنا\nSELECT * FROM user_tables WHERE ROWNUM <= 10;")
        self.sql_editor.setFont(QFont("Consolas", 12))
        
        # تطبيق مُلون بناء الجملة
        self.sql_highlighter = SQLSyntaxHighlighter(self.sql_editor.document())
        
        editor_layout.addWidget(self.sql_editor)
        splitter.addWidget(editor_group)
        
        # نتائج الاستعلام
        results_group = QGroupBox("نتائج الاستعلام")
        results_layout = QVBoxLayout(results_group)
        
        # شريط تقدم التنفيذ
        self.query_progress = QProgressBar()
        self.query_progress.setVisible(False)
        results_layout.addWidget(self.query_progress)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.setup_results_table()
        results_layout.addWidget(self.results_table)
        
        splitter.addWidget(results_group)
        
        # تعيين النسب
        splitter.setSizes([300, 400])
        layout.addWidget(splitter)
        
        return tab

    def create_objects_management_tab(self):
        """إنشاء تبويب إدارة الكائنات"""
        tab = QWidget()
        layout = QHBoxLayout(tab)

        # شجرة الكائنات
        objects_tree_group = QGroupBox("شجرة الكائنات")
        objects_tree_layout = QVBoxLayout(objects_tree_group)

        # شريط البحث
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.objects_search_input = QLineEdit()
        self.objects_search_input.setPlaceholderText("ابحث عن كائن...")
        self.objects_search_input.textChanged.connect(self.filter_objects_tree)
        search_layout.addWidget(self.objects_search_input)

        refresh_objects_btn = QPushButton("🔄")
        refresh_objects_btn.clicked.connect(self.refresh_objects_tree)
        search_layout.addWidget(refresh_objects_btn)

        objects_tree_layout.addLayout(search_layout)

        # شجرة الكائنات
        self.objects_tree = QTreeWidget()
        self.setup_objects_tree()
        objects_tree_layout.addWidget(self.objects_tree)

        layout.addWidget(objects_tree_group, 1)

        # تفاصيل الكائن
        object_details_group = QGroupBox("تفاصيل الكائن")
        object_details_layout = QVBoxLayout(object_details_group)

        # معلومات الكائن
        self.object_info_text = QTextEdit()
        self.object_info_text.setReadOnly(True)
        object_details_layout.addWidget(self.object_info_text)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        view_ddl_btn = QPushButton("📄 عرض DDL")
        view_ddl_btn.clicked.connect(self.view_object_ddl)
        actions_layout.addWidget(view_ddl_btn)

        analyze_object_btn = QPushButton("📊 تحليل الكائن")
        analyze_object_btn.clicked.connect(self.analyze_object)
        actions_layout.addWidget(analyze_object_btn)

        export_object_btn = QPushButton("📤 تصدير")
        export_object_btn.clicked.connect(self.export_object)
        actions_layout.addWidget(export_object_btn)

        actions_layout.addStretch()
        object_details_layout.addLayout(actions_layout)

        layout.addWidget(object_details_group, 2)

        return tab

    def create_performance_monitoring_tab(self):
        """إنشاء تبويب مراقبة الأداء"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # مؤشرات الأداء الرئيسية
        kpi_group = QGroupBox("مؤشرات الأداء الرئيسية")
        kpi_layout = QGridLayout(kpi_group)

        # استخدام المعالج
        kpi_layout.addWidget(QLabel("استخدام المعالج:"), 0, 0)
        self.cpu_usage_label = QLabel("0%")
        self.cpu_progress = QProgressBar()
        kpi_layout.addWidget(self.cpu_usage_label, 0, 1)
        kpi_layout.addWidget(self.cpu_progress, 0, 2)

        # استخدام الذاكرة
        kpi_layout.addWidget(QLabel("استخدام الذاكرة:"), 1, 0)
        self.memory_usage_label = QLabel("0 MB")
        self.memory_progress = QProgressBar()
        kpi_layout.addWidget(self.memory_usage_label, 1, 1)
        kpi_layout.addWidget(self.memory_progress, 1, 2)

        # الجلسات النشطة
        kpi_layout.addWidget(QLabel("الجلسات النشطة:"), 2, 0)
        self.active_sessions_label = QLabel("0")
        kpi_layout.addWidget(self.active_sessions_label, 2, 1)

        # متوسط وقت الاستجابة
        kpi_layout.addWidget(QLabel("متوسط وقت الاستجابة:"), 2, 2)
        self.response_time_label = QLabel("0 ms")
        kpi_layout.addWidget(self.response_time_label, 2, 3)

        layout.addWidget(kpi_group)

        # جدول الجلسات النشطة
        sessions_group = QGroupBox("الجلسات النشطة")
        sessions_layout = QVBoxLayout(sessions_group)

        self.sessions_table = QTableWidget()
        self.setup_sessions_table()
        sessions_layout.addWidget(self.sessions_table)

        layout.addWidget(sessions_group)

        # إحصائيات الاستعلامات
        queries_group = QGroupBox("إحصائيات الاستعلامات")
        queries_layout = QVBoxLayout(queries_group)

        self.queries_stats_table = QTableWidget()
        self.setup_queries_stats_table()
        queries_layout.addWidget(self.queries_stats_table)

        layout.addWidget(queries_group)

        return tab

    def create_users_management_tab(self):
        """إنشاء تبويب إدارة المستخدمين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أزرار إدارة المستخدمين
        users_actions_group = QGroupBox("إجراءات إدارة المستخدمين")
        users_actions_layout = QHBoxLayout(users_actions_group)

        create_user_btn = QPushButton("👤 إنشاء مستخدم جديد")
        create_user_btn.clicked.connect(self.create_new_user)
        users_actions_layout.addWidget(create_user_btn)

        modify_user_btn = QPushButton("✏️ تعديل مستخدم")
        modify_user_btn.clicked.connect(self.modify_user)
        users_actions_layout.addWidget(modify_user_btn)

        delete_user_btn = QPushButton("🗑️ حذف مستخدم")
        delete_user_btn.clicked.connect(self.delete_user)
        users_actions_layout.addWidget(delete_user_btn)

        grant_privileges_btn = QPushButton("🔑 منح صلاحيات")
        grant_privileges_btn.clicked.connect(self.grant_privileges)
        users_actions_layout.addWidget(grant_privileges_btn)

        users_actions_layout.addStretch()
        layout.addWidget(users_actions_group)

        # جدول المستخدمين
        users_group = QGroupBox("قائمة المستخدمين")
        users_layout = QVBoxLayout(users_group)

        self.users_table = QTableWidget()
        self.setup_users_table()
        users_layout.addWidget(self.users_table)

        layout.addWidget(users_group)

        # صلاحيات المستخدم المحدد
        privileges_group = QGroupBox("صلاحيات المستخدم المحدد")
        privileges_layout = QVBoxLayout(privileges_group)

        self.privileges_table = QTableWidget()
        self.setup_privileges_table()
        privileges_layout.addWidget(self.privileges_table)

        layout.addWidget(privileges_group)

        return tab

    def create_backup_management_tab(self):
        """إنشاء تبويب إدارة النسخ الاحتياطية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات النسخ الاحتياطية
        backup_settings_group = QGroupBox("إعدادات النسخ الاحتياطية")
        backup_settings_layout = QGridLayout(backup_settings_group)

        # نوع النسخة الاحتياطية
        backup_settings_layout.addWidget(QLabel("نوع النسخة:"), 0, 0)
        self.backup_type_combo = QComboBox()
        self.backup_type_combo.addItems(["مخطط كامل", "جداول محددة", "بيانات فقط", "هيكل فقط"])
        backup_settings_layout.addWidget(self.backup_type_combo, 0, 1)

        # مجلد الحفظ
        backup_settings_layout.addWidget(QLabel("مجلد الحفظ:"), 1, 0)
        self.backup_folder_input = QLineEdit()
        self.backup_folder_input.setText("backups/oracle/")
        backup_settings_layout.addWidget(self.backup_folder_input, 1, 1)

        browse_backup_btn = QPushButton("📁 تصفح")
        browse_backup_btn.clicked.connect(self.browse_backup_folder)
        backup_settings_layout.addWidget(browse_backup_btn, 1, 2)

        # ضغط النسخة
        self.compress_backup_checkbox = QCheckBox("ضغط النسخة الاحتياطية")
        self.compress_backup_checkbox.setChecked(True)
        backup_settings_layout.addWidget(self.compress_backup_checkbox, 2, 0, 1, 2)

        layout.addWidget(backup_settings_group)

        # أزرار النسخ الاحتياطي
        backup_actions_group = QGroupBox("عمليات النسخ الاحتياطي")
        backup_actions_layout = QHBoxLayout(backup_actions_group)

        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        create_backup_btn.clicked.connect(self.create_backup)
        backup_actions_layout.addWidget(create_backup_btn)

        schedule_backup_btn = QPushButton("⏰ جدولة النسخ")
        schedule_backup_btn.clicked.connect(self.schedule_backup)
        backup_actions_layout.addWidget(schedule_backup_btn)

        restore_backup_btn = QPushButton("📥 استعادة نسخة")
        restore_backup_btn.clicked.connect(self.restore_backup)
        backup_actions_layout.addWidget(restore_backup_btn)

        backup_actions_layout.addStretch()
        layout.addWidget(backup_actions_group)

        # قائمة النسخ الاحتياطية
        backups_list_group = QGroupBox("النسخ الاحتياطية المتاحة")
        backups_list_layout = QVBoxLayout(backups_list_group)

        self.backups_table = QTableWidget()
        self.setup_backups_table()
        backups_list_layout.addWidget(self.backups_table)

        layout.addWidget(backups_list_group)

        return tab

    def create_security_audit_tab(self):
        """إنشاء تبويب الأمان والمراجعة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات الأمان
        security_settings_group = QGroupBox("إعدادات الأمان")
        security_settings_layout = QGridLayout(security_settings_group)

        # تفعيل المراجعة
        self.audit_enabled_checkbox = QCheckBox("تفعيل نظام المراجعة")
        self.audit_enabled_checkbox.setChecked(True)
        security_settings_layout.addWidget(self.audit_enabled_checkbox, 0, 0, 1, 2)

        # مراجعة تسجيل الدخول
        self.audit_login_checkbox = QCheckBox("مراجعة تسجيل الدخول")
        security_settings_layout.addWidget(self.audit_login_checkbox, 1, 0)

        # مراجعة DDL
        self.audit_ddl_checkbox = QCheckBox("مراجعة عمليات DDL")
        security_settings_layout.addWidget(self.audit_ddl_checkbox, 1, 1)

        # مراجعة DML
        self.audit_dml_checkbox = QCheckBox("مراجعة عمليات DML")
        security_settings_layout.addWidget(self.audit_dml_checkbox, 2, 0)

        # مراجعة الصلاحيات
        self.audit_privileges_checkbox = QCheckBox("مراجعة تغيير الصلاحيات")
        security_settings_layout.addWidget(self.audit_privileges_checkbox, 2, 1)

        layout.addWidget(security_settings_group)

        # سجل المراجعة
        audit_log_group = QGroupBox("سجل المراجعة")
        audit_log_layout = QVBoxLayout(audit_log_group)

        # فلاتر السجل
        filters_layout = QHBoxLayout()
        filters_layout.addWidget(QLabel("من تاريخ:"))
        self.audit_from_date = QDateTimeEdit()
        self.audit_from_date.setDateTime(QDateTime.currentDateTime().addDays(-7))
        filters_layout.addWidget(self.audit_from_date)

        filters_layout.addWidget(QLabel("إلى تاريخ:"))
        self.audit_to_date = QDateTimeEdit()
        self.audit_to_date.setDateTime(QDateTime.currentDateTime())
        filters_layout.addWidget(self.audit_to_date)

        filter_audit_btn = QPushButton("🔍 تطبيق الفلتر")
        filter_audit_btn.clicked.connect(self.filter_audit_log)
        filters_layout.addWidget(filter_audit_btn)

        filters_layout.addStretch()
        audit_log_layout.addLayout(filters_layout)

        # جدول سجل المراجعة
        self.audit_log_table = QTableWidget()
        self.setup_audit_log_table()
        audit_log_layout.addWidget(self.audit_log_table)

        layout.addWidget(audit_log_group)

        return tab

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات الاتصال
        connection_group = QGroupBox("إعدادات الاتصال")
        connection_layout = QGridLayout(connection_group)

        connection_layout.addWidget(QLabel("المستخدم:"), 0, 0)
        self.username_input = QLineEdit()
        self.username_input.setText(self.connection_params['user'])
        connection_layout.addWidget(self.username_input, 0, 1)

        connection_layout.addWidget(QLabel("كلمة المرور:"), 1, 0)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText(self.connection_params['password'])
        connection_layout.addWidget(self.password_input, 1, 1)

        connection_layout.addWidget(QLabel("DSN:"), 2, 0)
        self.dsn_input = QLineEdit()
        self.dsn_input.setText(self.connection_params['dsn'])
        connection_layout.addWidget(self.dsn_input, 2, 1)

        # أزرار الاتصال
        connection_buttons_layout = QHBoxLayout()
        test_connection_btn = QPushButton("🔌 اختبار الاتصال")
        test_connection_btn.clicked.connect(self.test_connection)
        connection_buttons_layout.addWidget(test_connection_btn)

        save_connection_btn = QPushButton("💾 حفظ الإعدادات")
        save_connection_btn.clicked.connect(self.save_connection_settings)
        connection_buttons_layout.addWidget(save_connection_btn)

        connection_buttons_layout.addStretch()
        connection_layout.addLayout(connection_buttons_layout, 3, 0, 1, 2)

        layout.addWidget(connection_group)

        # إعدادات الواجهة
        ui_settings_group = QGroupBox("إعدادات الواجهة")
        ui_settings_layout = QGridLayout(ui_settings_group)

        # تحديث تلقائي
        ui_settings_layout.addWidget(QLabel("تحديث تلقائي (ثانية):"), 0, 0)
        self.auto_refresh_spinbox = QSpinBox()
        self.auto_refresh_spinbox.setRange(5, 300)
        self.auto_refresh_spinbox.setValue(30)
        ui_settings_layout.addWidget(self.auto_refresh_spinbox, 0, 1)

        # عدد الصفوف المعروضة
        ui_settings_layout.addWidget(QLabel("عدد الصفوف المعروضة:"), 1, 0)
        self.max_rows_spinbox = QSpinBox()
        self.max_rows_spinbox.setRange(10, 10000)
        self.max_rows_spinbox.setValue(1000)
        ui_settings_layout.addWidget(self.max_rows_spinbox, 1, 1)

        # حفظ تاريخ الاستعلامات
        self.save_query_history_checkbox = QCheckBox("حفظ تاريخ الاستعلامات")
        self.save_query_history_checkbox.setChecked(True)
        ui_settings_layout.addWidget(self.save_query_history_checkbox, 2, 0, 1, 2)

        layout.addWidget(ui_settings_group)

        layout.addStretch()
        return tab

    # دوال إعداد الجداول
    def setup_recent_objects_table(self):
        """إعداد جدول الكائنات الحديثة"""
        headers = ["اسم الكائن", "النوع", "آخر تعديل", "الحالة"]
        self.recent_objects_table.setColumnCount(len(headers))
        self.recent_objects_table.setHorizontalHeaderLabels(headers)
        self.recent_objects_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.recent_objects_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.recent_objects_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_results_table(self):
        """إعداد جدول النتائج"""
        self.results_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.results_table.horizontalHeader()
        header.setStretchLastSection(True)

    def setup_objects_tree(self):
        """إعداد شجرة الكائنات"""
        self.objects_tree.setHeaderLabels(["الكائنات", "العدد"])
        self.objects_tree.itemClicked.connect(self.on_object_selected)

        # إضافة العقد الرئيسية
        tables_node = QTreeWidgetItem(self.objects_tree, ["الجداول", "0"])
        views_node = QTreeWidgetItem(self.objects_tree, ["المشاهد", "0"])
        indexes_node = QTreeWidgetItem(self.objects_tree, ["الفهارس", "0"])
        sequences_node = QTreeWidgetItem(self.objects_tree, ["التسلسلات", "0"])
        procedures_node = QTreeWidgetItem(self.objects_tree, ["الإجراءات", "0"])
        functions_node = QTreeWidgetItem(self.objects_tree, ["الدوال", "0"])
        packages_node = QTreeWidgetItem(self.objects_tree, ["الحزم", "0"])
        triggers_node = QTreeWidgetItem(self.objects_tree, ["المشغلات", "0"])

        # توسيع العقد
        self.objects_tree.expandAll()

    def setup_sessions_table(self):
        """إعداد جدول الجلسات"""
        headers = ["SID", "المستخدم", "البرنامج", "الحالة", "وقت البداية", "وقت آخر نشاط"]
        self.sessions_table.setColumnCount(len(headers))
        self.sessions_table.setHorizontalHeaderLabels(headers)
        self.sessions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sessions_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.sessions_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_queries_stats_table(self):
        """إعداد جدول إحصائيات الاستعلامات"""
        headers = ["نوع الاستعلام", "العدد", "متوسط الوقت", "أطول وقت", "آخر تنفيذ"]
        self.queries_stats_table.setColumnCount(len(headers))
        self.queries_stats_table.setHorizontalHeaderLabels(headers)
        self.queries_stats_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.queries_stats_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.queries_stats_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_users_table(self):
        """إعداد جدول المستخدمين"""
        headers = ["اسم المستخدم", "الحالة", "تاريخ الإنشاء", "آخر تسجيل دخول", "الملف الشخصي"]
        self.users_table.setColumnCount(len(headers))
        self.users_table.setHorizontalHeaderLabels(headers)
        self.users_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.itemSelectionChanged.connect(self.on_user_selected)

        # تعديل عرض الأعمدة
        header = self.users_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_privileges_table(self):
        """إعداد جدول الصلاحيات"""
        headers = ["الصلاحية", "الكائن", "المانح", "قابل للمنح", "تاريخ المنح"]
        self.privileges_table.setColumnCount(len(headers))
        self.privileges_table.setHorizontalHeaderLabels(headers)
        self.privileges_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.privileges_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.privileges_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_backups_table(self):
        """إعداد جدول النسخ الاحتياطية"""
        headers = ["اسم الملف", "النوع", "التاريخ", "الحجم", "الحالة"]
        self.backups_table.setColumnCount(len(headers))
        self.backups_table.setHorizontalHeaderLabels(headers)
        self.backups_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.backups_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.backups_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_audit_log_table(self):
        """إعداد جدول سجل المراجعة"""
        headers = ["التاريخ", "المستخدم", "العملية", "الكائن", "النتيجة", "التفاصيل"]
        self.audit_log_table.setColumnCount(len(headers))
        self.audit_log_table.setHorizontalHeaderLabels(headers)
        self.audit_log_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.audit_log_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.audit_log_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # اختبار الاتصال
        test_connection_action = toolbar.addAction("🔌 اختبار الاتصال")
        test_connection_action.setToolTip("اختبار الاتصال بقاعدة البيانات")
        test_connection_action.triggered.connect(self.test_connection)

        toolbar.addSeparator()

        # تحديث المعلومات
        refresh_action = toolbar.addAction("🔄 تحديث")
        refresh_action.setToolTip("تحديث جميع المعلومات")
        refresh_action.triggered.connect(self.refresh_all_data)

        # نسخة احتياطية سريعة
        quick_backup_action = toolbar.addAction("💾 نسخة احتياطية")
        quick_backup_action.setToolTip("إنشاء نسخة احتياطية سريعة")
        quick_backup_action.triggered.connect(self.create_quick_backup)

        toolbar.addSeparator()

        # تنفيذ SQL
        execute_sql_action = toolbar.addAction("▶️ تنفيذ SQL")
        execute_sql_action.setToolTip("تنفيذ استعلام SQL (F5)")
        execute_sql_action.triggered.connect(self.execute_sql_query)

        # مراقبة الأداء
        monitor_action = toolbar.addAction("📊 مراقبة الأداء")
        monitor_action.setToolTip("مراقبة أداء قاعدة البيانات")
        monitor_action.triggered.connect(lambda: self.tabs.setCurrentIndex(3))

        toolbar.addSeparator()

        # إعدادات
        settings_action = toolbar.addAction("⚙️ الإعدادات")
        settings_action.setToolTip("إعدادات النظام")
        settings_action.triggered.connect(lambda: self.tabs.setCurrentIndex(7))

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        self.statusbar = self.statusBar()

        # حالة الاتصال
        self.connection_status_label = QLabel("🟢 متصل")
        self.connection_status_label.setStyleSheet("color: green; font-weight: bold;")
        self.statusbar.addWidget(self.connection_status_label)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # معلومات المستخدم
        self.user_info_label = QLabel(f"المستخدم: {self.connection_params['user']}")
        self.statusbar.addPermanentWidget(self.user_info_label)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # آخر تحديث
        self.last_refresh_label = QLabel("آخر تحديث: غير محدد")
        self.statusbar.addPermanentWidget(self.last_refresh_label)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # شريط التقدم العام
        self.general_progress = QProgressBar()
        self.general_progress.setVisible(False)
        self.general_progress.setMaximumWidth(200)
        self.statusbar.addPermanentWidget(self.general_progress)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديث دوري للمراقبة
        self.monitoring_timer.timeout.connect(self.update_monitoring_data)

        # ربط تغيير التبويبات
        self.tabs.currentChanged.connect(self.on_tab_changed)

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            settings_file = Path("config/oracle_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # تطبيق إعدادات الاتصال
                if 'connection' in settings:
                    conn_settings = settings['connection']
                    self.connection_params.update(conn_settings)

                # تطبيق إعدادات الواجهة
                if 'ui' in settings:
                    ui_settings = settings['ui']
                    if 'auto_refresh_interval' in ui_settings:
                        self.auto_refresh_spinbox.setValue(ui_settings['auto_refresh_interval'])
                    if 'max_rows' in ui_settings:
                        self.max_rows_spinbox.setValue(ui_settings['max_rows'])

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        self.monitoring_timer.start(5000)  # كل 5 ثوانٍ
        self.refresh_database_info()

    # الوظائف الأساسية
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            self.connection_status_label.setText("🟡 جاري الاختبار...")
            self.connection_status_label.setStyleSheet("color: orange; font-weight: bold;")

            # محاولة الاتصال
            conn = cx_Oracle.connect(**self.connection_params)
            cursor = conn.cursor()

            # اختبار بسيط
            cursor.execute("SELECT 1 FROM dual")
            result = cursor.fetchone()

            cursor.close()
            conn.close()

            if result:
                self.connection_status_label.setText("🟢 متصل")
                self.connection_status_label.setStyleSheet("color: green; font-weight: bold;")
                QMessageBox.information(self, "نجح الاتصال", "تم الاتصال بقاعدة البيانات بنجاح!")

                # تحديث معلومات قاعدة البيانات
                self.refresh_database_info()

        except Exception as e:
            self.connection_status_label.setText("🔴 غير متصل")
            self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")
            QMessageBox.critical(self, "فشل الاتصال", f"فشل في الاتصال بقاعدة البيانات:\n{str(e)}")

    def refresh_database_info(self):
        """تحديث معلومات قاعدة البيانات"""
        if self.db_thread and self.db_thread.isRunning():
            return

        self.general_progress.setVisible(True)
        self.general_progress.setValue(0)

        # إنشاء خيط لجمع المعلومات
        self.db_thread = OracleDatabaseThread("get_database_info", self.connection_params)
        self.db_thread.progress_updated.connect(self.general_progress.setValue)
        self.db_thread.result_ready.connect(self.on_database_info_ready)
        self.db_thread.error_occurred.connect(self.on_database_error)

        self.db_thread.start()

    def on_database_info_ready(self, info):
        """معالج استلام معلومات قاعدة البيانات"""
        self.general_progress.setVisible(False)

        # تحديث المعلومات في الواجهة
        self.db_version_label.setText(f"إصدار Oracle\n{info.get('oracle_version', 'غير محدد')[:20]}")
        self.db_name_label.setText(f"اسم قاعدة البيانات\n{info.get('database_name', 'غير محدد')}")

        # تحديث الإحصائيات
        self.tables_count_label.setText(str(info.get('tables_count', 0)))
        self.views_count_label.setText(str(info.get('views_count', 0)))
        self.data_size_label.setText(f"{info.get('data_size_mb', 0):.1f} MB")

        # تحديث عدد الكائنات الإجمالي
        total_objects = sum(info.get('objects_by_type', {}).values())
        self.db_objects_label.setText(f"عدد الكائنات\n{total_objects}")

        # تحديث وقت آخر تحديث
        self.last_update_label.setText(datetime.now().strftime("%Y-%m-%d %H:%M"))
        self.last_refresh_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")

        # تحديث شجرة الكائنات
        self.update_objects_tree_counts(info.get('objects_by_type', {}))

    def on_database_error(self, error_message):
        """معالج أخطاء قاعدة البيانات"""
        self.general_progress.setVisible(False)
        self.connection_status_label.setText("🔴 خطأ")
        self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")
        QMessageBox.critical(self, "خطأ في قاعدة البيانات", error_message)

    def execute_sql_query(self):
        """تنفيذ استعلام SQL"""
        query = self.sql_editor.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", "يرجى كتابة استعلام SQL أولاً")
            return

        if self.db_thread and self.db_thread.isRunning():
            QMessageBox.warning(self, "تحذير", "عملية أخرى قيد التنفيذ")
            return

        # إضافة الاستعلام إلى التاريخ
        if self.save_query_history_checkbox.isChecked():
            self.query_history.append({
                'query': query,
                'timestamp': datetime.now(),
                'user': self.connection_params['user']
            })

        self.query_progress.setVisible(True)
        self.query_progress.setValue(0)
        self.query_info_label.setText("جاري التنفيذ...")
        self.query_info_label.setStyleSheet("color: #f59e0b; font-weight: bold;")

        # إنشاء خيط لتنفيذ الاستعلام
        self.db_thread = OracleDatabaseThread("execute_query", self.connection_params, query)
        self.db_thread.progress_updated.connect(self.query_progress.setValue)
        self.db_thread.result_ready.connect(self.on_query_result_ready)
        self.db_thread.error_occurred.connect(self.on_query_error)

        self.db_thread.start()

    def on_query_result_ready(self, result):
        """معالج نتائج الاستعلام"""
        self.query_progress.setVisible(False)

        if result['type'] == 'select':
            # عرض نتائج SELECT
            columns = result['columns']
            rows = result['rows']

            self.results_table.setColumnCount(len(columns))
            self.results_table.setHorizontalHeaderLabels(columns)
            self.results_table.setRowCount(len(rows))

            for row_idx, row_data in enumerate(rows):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data) if cell_data is not None else "")
                    self.results_table.setItem(row_idx, col_idx, item)

            self.query_info_label.setText(f"تم العثور على {len(rows)} صف")
            self.query_info_label.setStyleSheet("color: #059669; font-weight: bold;")

        else:
            # عرض رسالة نجاح للعمليات الأخرى
            self.results_table.setColumnCount(1)
            self.results_table.setHorizontalHeaderLabels(["النتيجة"])
            self.results_table.setRowCount(1)
            self.results_table.setItem(0, 0, QTableWidgetItem(result['message']))

            self.query_info_label.setText("تم تنفيذ الاستعلام بنجاح")
            self.query_info_label.setStyleSheet("color: #059669; font-weight: bold;")

    def on_query_error(self, error_message):
        """معالج أخطاء الاستعلام"""
        self.query_progress.setVisible(False)
        self.query_info_label.setText("فشل في التنفيذ")
        self.query_info_label.setStyleSheet("color: #ef4444; font-weight: bold;")

        # عرض الخطأ في جدول النتائج
        self.results_table.setColumnCount(1)
        self.results_table.setHorizontalHeaderLabels(["خطأ"])
        self.results_table.setRowCount(1)
        self.results_table.setItem(0, 0, QTableWidgetItem(error_message))

        QMessageBox.critical(self, "خطأ في الاستعلام", error_message)

    def clear_sql_editor(self):
        """مسح محرر SQL"""
        self.sql_editor.clear()
        self.results_table.clear()
        self.results_table.setRowCount(0)
        self.results_table.setColumnCount(0)
        self.query_info_label.setText("جاهز")
        self.query_info_label.setStyleSheet("color: #059669; font-weight: bold;")

    def save_sql_query(self):
        """حفظ الاستعلام"""
        query = self.sql_editor.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", "لا يوجد استعلام لحفظه")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ الاستعلام", "", "SQL Files (*.sql);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(query)
                QMessageBox.information(self, "نجح", "تم حفظ الاستعلام بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حفظ الاستعلام: {str(e)}")

    def load_sql_query(self):
        """تحميل استعلام"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "تحميل استعلام", "", "SQL Files (*.sql);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    query = f.read()
                self.sql_editor.setPlainText(query)
                QMessageBox.information(self, "نجح", "تم تحميل الاستعلام بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تحميل الاستعلام: {str(e)}")

    def update_objects_tree_counts(self, objects_by_type):
        """تحديث أعداد الكائنات في الشجرة"""
        type_mapping = {
            'TABLE': 0,
            'VIEW': 1,
            'INDEX': 2,
            'SEQUENCE': 3,
            'PROCEDURE': 4,
            'FUNCTION': 5,
            'PACKAGE': 6,
            'TRIGGER': 7
        }

        for obj_type, count in objects_by_type.items():
            if obj_type in type_mapping:
                item_index = type_mapping[obj_type]
                if item_index < self.objects_tree.topLevelItemCount():
                    item = self.objects_tree.topLevelItem(item_index)
                    item.setText(1, str(count))

    def update_monitoring_data(self):
        """تحديث بيانات المراقبة"""
        try:
            # استخدام المعالج
            cpu_percent = psutil.cpu_percent()
            self.cpu_usage_label.setText(f"{cpu_percent:.1f}%")
            self.cpu_progress.setValue(int(cpu_percent))

            # استخدام الذاكرة
            memory = psutil.virtual_memory()
            memory_mb = memory.used / (1024 * 1024)
            memory_percent = memory.percent
            self.memory_usage_label.setText(f"{memory_mb:.0f} MB")
            self.memory_progress.setValue(int(memory_percent))

            # محاكاة بيانات الجلسات والاستجابة
            self.active_sessions_label.setText("1")
            self.response_time_label.setText("25 ms")

        except Exception as e:
            print(f"خطأ في تحديث بيانات المراقبة: {e}")

    # دوال معالجة الأحداث
    def on_tab_changed(self, index):
        """معالج تغيير التبويب"""
        if index == 2:  # تبويب إدارة الكائنات
            self.refresh_objects_tree()
        elif index == 3:  # تبويب مراقبة الأداء
            self.update_monitoring_data()

    def on_object_selected(self, item, column):
        """معالج اختيار كائن من الشجرة"""
        object_name = item.text(0)
        self.object_info_text.setText(f"تم اختيار: {object_name}\n\nتفاصيل الكائن ستظهر هنا...")

    def on_user_selected(self):
        """معالج اختيار مستخدم"""
        selected_items = self.users_table.selectedItems()
        if selected_items:
            username = selected_items[0].text()
            self.load_user_privileges(username)

    def filter_objects_tree(self, text):
        """فلترة شجرة الكائنات"""
        # تنفيذ فلترة بسيطة
        for i in range(self.objects_tree.topLevelItemCount()):
            item = self.objects_tree.topLevelItem(i)
            if text.lower() in item.text(0).lower():
                item.setHidden(False)
            else:
                item.setHidden(True)

    # دوال الوظائف المتقدمة (مبسطة للعرض)
    def refresh_objects_tree(self):
        """تحديث شجرة الكائنات"""
        QMessageBox.information(self, "قريباً", "تحديث شجرة الكائنات قيد التطوير")

    def view_object_ddl(self):
        """عرض DDL للكائن"""
        QMessageBox.information(self, "قريباً", "عرض DDL قيد التطوير")

    def analyze_object(self):
        """تحليل الكائن"""
        QMessageBox.information(self, "قريباً", "تحليل الكائن قيد التطوير")

    def export_object(self):
        """تصدير الكائن"""
        QMessageBox.information(self, "قريباً", "تصدير الكائن قيد التطوير")

    def create_new_user(self):
        """إنشاء مستخدم جديد"""
        QMessageBox.information(self, "قريباً", "إنشاء مستخدم جديد قيد التطوير")

    def modify_user(self):
        """تعديل مستخدم"""
        QMessageBox.information(self, "قريباً", "تعديل مستخدم قيد التطوير")

    def delete_user(self):
        """حذف مستخدم"""
        QMessageBox.information(self, "قريباً", "حذف مستخدم قيد التطوير")

    def grant_privileges(self):
        """منح صلاحيات"""
        QMessageBox.information(self, "قريباً", "منح صلاحيات قيد التطوير")

    def load_user_privileges(self, username):
        """تحميل صلاحيات المستخدم"""
        self.privileges_table.setRowCount(0)
        # هنا يمكن تحميل الصلاحيات الفعلية من قاعدة البيانات

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        QMessageBox.information(self, "قريباً", "إنشاء نسخة احتياطية قيد التطوير")

    def schedule_backup(self):
        """جدولة النسخ الاحتياطية"""
        QMessageBox.information(self, "قريباً", "جدولة النسخ الاحتياطية قيد التطوير")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        QMessageBox.information(self, "قريباً", "استعادة نسخة احتياطية قيد التطوير")

    def browse_backup_folder(self):
        """تصفح مجلد النسخ الاحتياطية"""
        folder = QFileDialog.getExistingDirectory(self, "اختر مجلد النسخ الاحتياطية")
        if folder:
            self.backup_folder_input.setText(folder)

    def filter_audit_log(self):
        """فلترة سجل المراجعة"""
        QMessageBox.information(self, "قريباً", "فلترة سجل المراجعة قيد التطوير")

    def save_connection_settings(self):
        """حفظ إعدادات الاتصال"""
        try:
            # تحديث معاملات الاتصال
            self.connection_params['user'] = self.username_input.text()
            self.connection_params['password'] = self.password_input.text()
            self.connection_params['dsn'] = self.dsn_input.text()

            # حفظ الإعدادات
            settings = {
                'connection': self.connection_params,
                'ui': {
                    'auto_refresh_interval': self.auto_refresh_spinbox.value(),
                    'max_rows': self.max_rows_spinbox.value(),
                    'save_query_history': self.save_query_history_checkbox.isChecked()
                }
            }

            # إنشاء مجلد الإعدادات
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)

            # حفظ الإعدادات
            with open(config_dir / "oracle_settings.json", 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")

            # تحديث تسميات الواجهة
            self.current_user_label.setText(self.connection_params['user'])
            self.database_name_label.setText(self.connection_params['dsn'])
            self.user_info_label.setText(f"المستخدم: {self.connection_params['user']}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الإعدادات: {e}")

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.refresh_database_info()
        self.update_monitoring_data()

    def create_quick_backup(self):
        """نسخة احتياطية سريعة"""
        self.create_backup()

    def optimize_performance(self):
        """تحسين الأداء"""
        QMessageBox.information(self, "قريباً", "تحسين الأداء قيد التطوير")

    def monitor_sessions(self):
        """مراقبة الجلسات"""
        # الانتقال إلى تبويب مراقبة الأداء
        self.tabs.setCurrentIndex(3)

    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        QMessageBox.information(self, "قريباً", "إنشاء تقرير شامل قيد التطوير")


# دالة لفتح نظام إدارة قاعدة البيانات
def open_oracle_database_manager(parent=None):
    """فتح نظام إدارة قاعدة بيانات Oracle"""
    try:
        manager = OracleDatabaseManager(parent)
        manager.show()
        return manager
    except Exception as e:
        QMessageBox.critical(parent, "خطأ", f"فشل في فتح نظام إدارة قاعدة البيانات: {str(e)}")
        return None


if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # تطبيق الثيم
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f8fafc;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #1e40af;
        }
        QPushButton {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2563eb;
        }
        QPushButton:pressed {
            background-color: #1d4ed8;
        }
        QTableWidget {
            gridline-color: #e2e8f0;
            background-color: white;
            alternate-background-color: #f8fafc;
        }
        QHeaderView::section {
            background-color: #f1f5f9;
            padding: 8px;
            border: 1px solid #e2e8f0;
            font-weight: bold;
        }
    """)

    window = OracleDatabaseManager()
    window.show()

    sys.exit(app.exec())
