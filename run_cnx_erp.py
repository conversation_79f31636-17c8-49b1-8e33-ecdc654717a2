#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CnX ERP - ملف التشغيل السريع
Quick Launch Script for CnX ERP System
"""

import sys
import os
import subprocess

def check_requirements():
    """فحص المتطلبات المطلوبة"""
    required_packages = [
        'PySide6',
        'arabic_reshaper', 
        'bidi',
        'PIL'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - متوفر")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - غير متوفر")
    
    return missing_packages

def install_requirements():
    """تثبيت المتطلبات المفقودة"""
    packages_to_install = [
        'PySide6',
        'arabic-reshaper',
        'python-bidi', 
        'Pillow'
    ]
    
    print("\n🔄 جاري تثبيت المكتبات المطلوبة...")
    
    for package in packages_to_install:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ تم تثبيت {package} بنجاح")
        except subprocess.CalledProcessError:
            print(f"❌ فشل في تثبيت {package}")
            return False
    
    return True

def show_menu():
    """عرض قائمة الخيارات"""
    print("\n" + "="*60)
    print("🏢 مرحباً بك في نظام CnX ERP")
    print("="*60)
    print("اختر النسخة التي تريد تشغيلها:")
    print()
    print("1️⃣  النسخة البسيطة (للاختبار السريع)")
    print("2️⃣  النسخة المحسنة (مع الخلفية الفنية)")
    print("3️⃣  النسخة الكاملة (جميع الميزات)")
    print("4️⃣  النسخة العربية المُصححة (دعم كامل للعربية + ملء الشاشة) ⭐")
    print("5️⃣  فحص المتطلبات")
    print("6️⃣  تثبيت المتطلبات")
    print("0️⃣  خروج")
    print("="*60)

def run_version(version):
    """تشغيل النسخة المحددة"""
    files = {
        1: 'cnx_erp_simple.py',
        2: 'cnx_erp_enhanced.py',
        3: 'cnx_erp_main_window.py',
        4: 'cnx_erp_arabic_fixed.py'
    }
    
    filename = files.get(version)
    if not filename:
        print("❌ خيار غير صحيح!")
        return
    
    if not os.path.exists(filename):
        print(f"❌ الملف {filename} غير موجود!")
        return
    
    print(f"\n🚀 جاري تشغيل {filename}...")
    try:
        subprocess.run([sys.executable, filename])
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔄 جاري تحضير نظام CnX ERP...")
    
    while True:
        show_menu()
        
        try:
            choice = input("\n👆 اختر رقم الخيار: ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام نظام CnX ERP!")
                break
                
            elif choice == '1':
                run_version(1)
                
            elif choice == '2':
                run_version(2)
                
            elif choice == '3':
                run_version(3)

            elif choice == '4':
                run_version(4)

            elif choice == '5':
                print("\n🔍 جاري فحص المتطلبات...")
                missing = check_requirements()
                if not missing:
                    print("\n✅ جميع المتطلبات متوفرة!")
                else:
                    print(f"\n⚠️  المتطلبات المفقودة: {', '.join(missing)}")

            elif choice == '6':
                if install_requirements():
                    print("\n✅ تم تثبيت جميع المتطلبات بنجاح!")
                else:
                    print("\n❌ فشل في تثبيت بعض المتطلبات")
                    
            else:
                print("❌ خيار غير صحيح! اختر رقم من 0 إلى 6")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم الخروج من البرنامج")
            break
        except Exception as e:
            print(f"\n❌ خطأ غير متوقع: {e}")
        
        input("\n⏸️  اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
