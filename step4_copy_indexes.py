#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 4: نسخ الفهارس
Step 4: Copy indexes
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class IndexCopier:
    """ناسخ الفهارس"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'indexes_created': 0,
            'indexes_failed': 0,
            'indexes_skipped': 0,
            'failed_indexes': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def get_indexes_info(self):
        """الحصول على معلومات الفهارس"""
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على الفهارس (باستثناء فهارس المفاتيح الأساسية)
            cursor.execute("""
                SELECT i.index_name, i.table_name, i.index_type, i.uniqueness,
                       i.tablespace_name, i.compression, i.status
                FROM user_indexes i
                WHERE i.index_name NOT IN (
                    SELECT constraint_name 
                    FROM user_constraints 
                    WHERE constraint_type = 'P'
                    AND constraint_name = i.index_name
                )
                AND i.table_name NOT LIKE 'BIN$%'
                ORDER BY i.table_name, i.index_name
            """)
            
            indexes = cursor.fetchall()
            
            print(f"📊 تم العثور على {len(indexes)} فهرس للنسخ")
            
            # الحصول على أعمدة كل فهرس
            indexes_info = []
            for idx_data in indexes:
                index_name = idx_data[0]
                
                # الحصول على أعمدة الفهرس
                cursor.execute("""
                    SELECT column_name, column_position, descend
                    FROM user_ind_columns
                    WHERE index_name = :index_name
                    ORDER BY column_position
                """, {'index_name': index_name})
                
                columns = cursor.fetchall()
                
                index_info = {
                    'name': index_name,
                    'table_name': idx_data[1],
                    'type': idx_data[2],
                    'uniqueness': idx_data[3],
                    'tablespace_name': idx_data[4],
                    'compression': idx_data[5],
                    'status': idx_data[6],
                    'columns': columns
                }
                
                indexes_info.append(index_info)
            
            cursor.close()
            return indexes_info
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات الفهارس: {e}")
            return []
    
    def generate_index_ddl(self, index_info):
        """إنشاء DDL للفهرس"""
        try:
            index_name = index_info['name']
            table_name = index_info['table_name']
            uniqueness = index_info['uniqueness']
            columns = index_info['columns']
            
            if not columns:
                return None
            
            # بناء قائمة الأعمدة
            column_list = []
            for col_name, col_pos, descend in columns:
                if descend == 'DESC':
                    column_list.append(f"{col_name} DESC")
                else:
                    column_list.append(col_name)
            
            columns_str = ", ".join(column_list)
            
            # بناء DDL
            if uniqueness == 'UNIQUE':
                ddl = f"CREATE UNIQUE INDEX {index_name} ON {table_name} ({columns_str})"
            else:
                ddl = f"CREATE INDEX {index_name} ON {table_name} ({columns_str})"
            
            # إضافة خصائص إضافية
            if index_info.get('tablespace_name'):
                ddl += f" TABLESPACE {index_info['tablespace_name']}"
            
            return ddl
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء DDL للفهرس {index_info['name']}: {e}")
            return None
    
    def create_index(self, index_info):
        """إنشاء فهرس في الهدف"""
        try:
            index_name = index_info['name']
            table_name = index_info['table_name']
            
            target_cursor = self.target_conn.cursor()
            
            # التحقق من وجود الجدول في الهدف
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
            """, {'table_name': table_name})
            
            if target_cursor.fetchone()[0] == 0:
                print(f"   ⚠️ الجدول {table_name} غير موجود - تخطي الفهرس {index_name}")
                target_cursor.close()
                return 'skipped'
            
            # التحقق من وجود الفهرس
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_indexes WHERE index_name = :index_name
            """, {'index_name': index_name})
            
            if target_cursor.fetchone()[0] > 0:
                print(f"   ⚠️ الفهرس {index_name} موجود بالفعل - سيتم تخطيه")
                target_cursor.close()
                return 'skipped'
            
            # إنشاء DDL
            ddl = self.generate_index_ddl(index_info)
            if not ddl:
                target_cursor.close()
                return 'failed'
            
            # إنشاء الفهرس
            target_cursor.execute(ddl)
            self.target_conn.commit()
            target_cursor.close()
            
            return 'created'
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الفهرس {index_info['name']}: {e}")
            return 'failed'
    
    def copy_indexes(self, batch_size=50):
        """نسخ جميع الفهارس"""
        print("🚀 بدء نسخ الفهارس")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # الحصول على معلومات الفهارس
        indexes_info = self.get_indexes_info()
        if not indexes_info:
            print("❌ لم يتم العثور على فهارس للنسخ")
            return False
        
        print(f"📊 سيتم نسخ {len(indexes_info)} فهرس")
        
        # نسخ الفهارس على دفعات
        for i in range(0, len(indexes_info), batch_size):
            batch = indexes_info[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(indexes_info) + batch_size - 1) // batch_size
            
            print(f"\n📦 الدفعة {batch_num}/{total_batches} ({len(batch)} فهرس)")
            print("-" * 40)
            
            for j, index_info in enumerate(batch, 1):
                index_name = index_info['name']
                table_name = index_info['table_name']
                
                print(f"[{i + j:4d}/{len(indexes_info)}] إنشاء فهرس: {index_name} على {table_name}")
                
                result = self.create_index(index_info)
                
                if result == 'created':
                    print(f"   ✅ تم إنشاء الفهرس {index_name}")
                    self.stats['indexes_created'] += 1
                elif result == 'skipped':
                    self.stats['indexes_skipped'] += 1
                else:  # failed
                    self.stats['indexes_failed'] += 1
                    self.stats['failed_indexes'].append(index_name)
            
            # عرض تقدم الدفعة
            print(f"✅ تمت الدفعة {batch_num}: {self.stats['indexes_created']} نجح، {self.stats['indexes_failed']} فشل، {self.stats['indexes_skipped']} تخطي")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("🎉 تم إكمال نسخ الفهارس!")
        print(f"✅ الفهارس المنشأة: {self.stats['indexes_created']}")
        print(f"⚠️ الفهارس المتخطاة: {self.stats['indexes_skipped']}")
        print(f"❌ الفهارس الفاشلة: {self.stats['indexes_failed']}")
        print(f"⏱️ المدة: {duration}")
        
        if self.stats['failed_indexes']:
            print(f"\n❌ الفهارس الفاشلة:")
            for index in self.stats['failed_indexes'][:10]:  # أول 10
                print(f"   - {index}")
            if len(self.stats['failed_indexes']) > 10:
                print(f"   ... و {len(self.stats['failed_indexes']) - 10} فهرس آخر")
        
        print("=" * 60)
        
        return self.stats['indexes_created'] > 0
    
    def verify_indexes(self):
        """التحقق من الفهارس المنسوخة"""
        print("\n🔍 التحقق من الفهارس المنسوخة...")
        
        try:
            # عد الفهارس في الهدف
            target_cursor = self.target_conn.cursor()
            target_cursor.execute("SELECT COUNT(*) FROM user_indexes")
            target_count = target_cursor.fetchone()[0]
            
            # عد الفهارس في المصدر (باستثناء فهارس المفاتيح الأساسية)
            source_cursor = self.source_conn.cursor()
            source_cursor.execute("""
                SELECT COUNT(*) FROM user_indexes i
                WHERE i.index_name NOT IN (
                    SELECT constraint_name 
                    FROM user_constraints 
                    WHERE constraint_type = 'P'
                    AND constraint_name = i.index_name
                )
                AND i.table_name NOT LIKE 'BIN$%'
            """)
            source_count = source_cursor.fetchone()[0]
            
            print(f"📊 الفهارس في المصدر: {source_count}")
            print(f"📊 الفهارس في الهدف: {target_count}")
            
            if source_count > 0:
                percentage = (target_count / source_count) * 100
                print(f"📊 نسبة النجاح: {percentage:.1f}%")
            
            # عرض عينة من الفهارس
            target_cursor.execute("""
                SELECT index_name, table_name, uniqueness, status
                FROM user_indexes
                ORDER BY table_name, index_name
            """)
            
            indexes = target_cursor.fetchall()
            
            if indexes:
                print("\n📋 عينة من الفهارس المنسوخة:")
                for i, (idx_name, table_name, uniqueness, status) in enumerate(indexes[:10]):
                    unique_str = "UNIQUE" if uniqueness == 'UNIQUE' else "NON-UNIQUE"
                    print(f"   {i+1:2d}. {idx_name} على {table_name} ({unique_str}) - {status}")
                
                if len(indexes) > 10:
                    print(f"   ... و {len(indexes) - 10} فهرس آخر")
            
            target_cursor.close()
            source_cursor.close()
            
            return target_count > 0
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_copy_process(self):
        """تشغيل عملية نسخ الفهارس"""
        try:
            if not self.connect():
                return False
            
            # نسخ الفهارس
            success = self.copy_indexes()
            
            if success:
                # التحقق من النتائج
                self.verify_indexes()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في عملية النسخ: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = IndexCopier()
    success = copier.run_copy_process()
    
    if success:
        print("\n✅ الخطوة 4 مكتملة - تم نسخ الفهارس")
    else:
        print("\n❌ فشل في الخطوة 4")
    
    return success


if __name__ == "__main__":
    main()
