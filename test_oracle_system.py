#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة قاعدة بيانات Oracle المتقدم
Test Oracle Database Management System
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from src.ui.database.oracle_database_manager import OracleDatabaseManager


def test_oracle_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        import cx_Oracle
        
        # إعداد البيئة
        tns_admin = Path("network/admin")
        if tns_admin.exists():
            os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        # اختبار الاتصال
        conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        cursor = conn.cursor()
        
        # اختبار بسيط
        cursor.execute("SELECT 1 FROM dual")
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        if result:
            print("✅ الاتصال بقاعدة البيانات ناجح!")
            return True
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار نظام إدارة قاعدة بيانات Oracle المتقدم")
    print("=" * 60)
    
    # اختبار الاتصال أولاً
    if not test_oracle_connection():
        print("⚠️ تحذير: لا يمكن الاتصال بقاعدة البيانات")
        print("   تأكد من:")
        print("   - تشغيل خدمة Oracle")
        print("   - صحة بيانات الاتصال")
        print("   - وجود مجلد network/admin")
        print()
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # تطبيق الثيم
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f8fafc;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #1e40af;
        }
        QPushButton {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2563eb;
        }
        QPushButton:pressed {
            background-color: #1d4ed8;
        }
        QTableWidget {
            gridline-color: #e2e8f0;
            background-color: white;
            alternate-background-color: #f8fafc;
        }
        QHeaderView::section {
            background-color: #f1f5f9;
            padding: 8px;
            border: 1px solid #e2e8f0;
            font-weight: bold;
        }
        QTabWidget::pane {
            border: 1px solid #e2e8f0;
            background-color: white;
        }
        QTabBar::tab {
            background-color: #f1f5f9;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        QTabBar::tab:selected {
            background-color: #3b82f6;
            color: white;
        }
        QTreeWidget {
            background-color: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }
        QTreeWidget::item {
            padding: 4px;
        }
        QTreeWidget::item:selected {
            background-color: #3b82f6;
            color: white;
        }
        QLineEdit, QTextEdit, QPlainTextEdit {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 8px;
            background-color: white;
        }
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #3b82f6;
        }
        QProgressBar {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            text-align: center;
        }
        QProgressBar::chunk {
            background-color: #10b981;
            border-radius: 5px;
        }
    """)
    
    try:
        # إنشاء نظام إدارة قاعدة البيانات
        oracle_manager = OracleDatabaseManager()
        oracle_manager.show()
        
        print("✅ تم تشغيل نظام إدارة قاعدة البيانات بنجاح!")
        print("📋 الميزات المتاحة:")
        print("   🏠 لوحة التحكم - معلومات شاملة عن قاعدة البيانات")
        print("   📝 محرر SQL - تنفيذ الاستعلامات مع تلوين بناء الجملة")
        print("   🗂️ إدارة الكائنات - تصفح وإدارة جداول ومشاهد وفهارس")
        print("   📊 مراقبة الأداء - مراقبة الجلسات والاستعلامات")
        print("   👥 إدارة المستخدمين - إنشاء وتعديل المستخدمين والصلاحيات")
        print("   💾 النسخ الاحتياطية - إنشاء واستعادة النسخ الاحتياطية")
        print("   🔒 الأمان والمراجعة - مراقبة العمليات والأمان")
        print("   ⚙️ الإعدادات - تخصيص الاتصال والواجهة")
        print()
        print("🎯 استخدم التبويبات للتنقل بين الأنظمة المختلفة")
        print("🔌 اختبر الاتصال من لوحة التحكم أو شريط الأدوات")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في تشغيل نظام إدارة قاعدة البيانات:\n{str(e)}")
        return False


if __name__ == "__main__":
    main()
