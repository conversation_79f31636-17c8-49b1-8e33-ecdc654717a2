#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 1: إنشاء المستخدم ship2025
Step 1: Create user ship2025
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
    print(f"🔧 TNS_ADMIN: {os.environ['TNS_ADMIN']}")


def connect_as_dba():
    """الاتصال كـ DBA"""
    try:
        print("🔌 الاتصال كـ DBA (system)...")
        
        # محاولات اتصال مختلفة
        connection_attempts = [
            ("system", "oracle"),
            ("sys", "oracle"),
            ("system", "manager"),
            ("sys", "manager")
        ]
        
        for username, password in connection_attempts:
            try:
                print(f"   محاولة الاتصال: {username}")
                
                if username == "sys":
                    conn = cx_Oracle.connect(
                        username, password, "yemensoft",
                        mode=cx_Oracle.SYSDBA,
                        encoding="UTF-8"
                    )
                else:
                    conn = cx_Oracle.connect(
                        username, password, "yemensoft",
                        encoding="UTF-8"
                    )
                
                cursor = conn.cursor()
                cursor.execute("SELECT USER FROM DUAL")
                current_user = cursor.fetchone()[0]
                
                print(f"✅ تم الاتصال كـ {current_user}")
                return conn, cursor
                
            except Exception as e:
                print(f"   ❌ فشل الاتصال كـ {username}: {e}")
                continue
        
        raise Exception("فشل في جميع محاولات الاتصال كـ DBA")
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال كـ DBA: {e}")
        return None, None


def check_user_exists(cursor, username):
    """التحقق من وجود المستخدم"""
    try:
        cursor.execute("SELECT COUNT(*) FROM dba_users WHERE username = :username", 
                      {'username': username.upper()})
        count = cursor.fetchone()[0]
        return count > 0
    except:
        return False


def drop_user_if_exists(cursor, username):
    """حذف المستخدم إذا كان موجوداً"""
    try:
        if check_user_exists(cursor, username):
            print(f"🗑️ حذف المستخدم الموجود: {username}")
            cursor.execute(f"DROP USER {username} CASCADE")
            print(f"✅ تم حذف المستخدم {username}")
            return True
    except Exception as e:
        print(f"❌ خطأ في حذف المستخدم: {e}")
        return False


def create_user_ship2025(conn, cursor):
    """إنشاء المستخدم ship2025"""
    username = "ship2025"
    password = "ys123"
    
    print(f"👤 إنشاء المستخدم {username}...")
    
    try:
        # حذف المستخدم إذا كان موجوداً
        drop_user_if_exists(cursor, username)
        
        # إنشاء المستخدم الجديد
        commands = [
            # إنشاء المستخدم
            f"CREATE USER {username} IDENTIFIED BY {password}",
            
            # الصلاحيات الأساسية
            f"GRANT CONNECT TO {username}",
            f"GRANT RESOURCE TO {username}",
            f"GRANT CREATE SESSION TO {username}",
            
            # صلاحيات الكائنات
            f"GRANT CREATE TABLE TO {username}",
            f"GRANT CREATE VIEW TO {username}",
            f"GRANT CREATE SEQUENCE TO {username}",
            f"GRANT CREATE PROCEDURE TO {username}",
            f"GRANT CREATE FUNCTION TO {username}",
            f"GRANT CREATE PACKAGE TO {username}",
            f"GRANT CREATE TRIGGER TO {username}",
            f"GRANT CREATE SYNONYM TO {username}",
            f"GRANT CREATE TYPE TO {username}",
            
            # صلاحيات متقدمة
            f"GRANT CREATE ANY TABLE TO {username}",
            f"GRANT ALTER ANY TABLE TO {username}",
            f"GRANT DROP ANY TABLE TO {username}",
            f"GRANT SELECT ANY TABLE TO {username}",
            f"GRANT INSERT ANY TABLE TO {username}",
            f"GRANT UPDATE ANY TABLE TO {username}",
            f"GRANT DELETE ANY TABLE TO {username}",
            
            # صلاحيات الفهارس
            f"GRANT CREATE ANY INDEX TO {username}",
            f"GRANT ALTER ANY INDEX TO {username}",
            f"GRANT DROP ANY INDEX TO {username}",
            
            # صلاحيات المتسلسلات
            f"GRANT CREATE ANY SEQUENCE TO {username}",
            f"GRANT ALTER ANY SEQUENCE TO {username}",
            f"GRANT DROP ANY SEQUENCE TO {username}",
            f"GRANT SELECT ANY SEQUENCE TO {username}",
            
            # إعدادات التخزين
            f"ALTER USER {username} DEFAULT TABLESPACE USERS",
            f"ALTER USER {username} TEMPORARY TABLESPACE TEMP",
            f"ALTER USER {username} QUOTA UNLIMITED ON USERS",
            
            # صلاحيات إضافية للوصول لجداول ias20241
            f"GRANT SELECT ANY DICTIONARY TO {username}",
            f"GRANT EXECUTE ANY PROCEDURE TO {username}"
        ]
        
        success_count = 0
        for command in commands:
            try:
                cursor.execute(command)
                print(f"   ✅ {command}")
                success_count += 1
            except Exception as e:
                if "already exists" in str(e).lower():
                    print(f"   ⚠️ {command} (موجود بالفعل)")
                    success_count += 1
                else:
                    print(f"   ❌ {command}: {e}")
        
        # تأكيد التغييرات
        conn.commit()
        
        print(f"\n📊 تم تنفيذ {success_count}/{len(commands)} أمر بنجاح")
        
        # التحقق من إنشاء المستخدم
        if check_user_exists(cursor, username):
            print(f"✅ تم إنشاء المستخدم {username} بنجاح")
            
            # عرض معلومات المستخدم
            cursor.execute("""
                SELECT username, account_status, default_tablespace, 
                       temporary_tablespace, created
                FROM dba_users 
                WHERE username = :username
            """, {'username': username.upper()})
            
            user_info = cursor.fetchone()
            if user_info:
                print(f"📋 معلومات المستخدم:")
                print(f"   👤 الاسم: {user_info[0]}")
                print(f"   📊 الحالة: {user_info[1]}")
                print(f"   💾 مساحة التخزين: {user_info[2]}")
                print(f"   🗂️ مساحة مؤقتة: {user_info[3]}")
                print(f"   📅 تاريخ الإنشاء: {user_info[4]}")
            
            return True
        else:
            print(f"❌ فشل في إنشاء المستخدم {username}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return False


def test_new_user_connection():
    """اختبار الاتصال بالمستخدم الجديد"""
    print("\n🧪 اختبار الاتصال بالمستخدم الجديد...")
    
    try:
        conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        cursor = conn.cursor()
        
        # اختبار بسيط
        cursor.execute("SELECT USER, SYSDATE FROM DUAL")
        user, sysdate = cursor.fetchone()
        
        print(f"✅ تم الاتصال بنجاح!")
        print(f"   👤 المستخدم: {user}")
        print(f"   🕐 الوقت: {sysdate}")
        
        # اختبار الصلاحيات
        try:
            cursor.execute("SELECT COUNT(*) FROM user_tables")
            table_count = cursor.fetchone()[0]
            print(f"   📊 عدد الجداول الحالية: {table_count}")
        except Exception as e:
            print(f"   ⚠️ خطأ في فحص الجداول: {e}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في الاتصال بالمستخدم الجديد: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🚀 الخطوة 1: إنشاء المستخدم ship2025")
    print("=" * 60)
    
    # إعداد البيئة
    setup_environment()
    
    # الاتصال كـ DBA
    conn, cursor = connect_as_dba()
    if not conn:
        print("💥 فشل في الاتصال كـ DBA - لا يمكن المتابعة")
        return False
    
    try:
        # إنشاء المستخدم
        success = create_user_ship2025(conn, cursor)
        
        if success:
            # اختبار الاتصال بالمستخدم الجديد
            test_success = test_new_user_connection()
            
            if test_success:
                print("\n🎉 تم إنشاء المستخدم ship2025 بنجاح!")
                print("✅ الخطوة 1 مكتملة")
                return True
            else:
                print("\n❌ فشل في اختبار المستخدم الجديد")
                return False
        else:
            print("\n❌ فشل في إنشاء المستخدم")
            return False
            
    except Exception as e:
        print(f"\n💥 خطأ عام: {e}")
        return False
        
    finally:
        # إغلاق الاتصال
        if cursor:
            cursor.close()
        if conn:
            conn.close()
        print("🔌 تم إغلاق اتصال DBA")


if __name__ == "__main__":
    main()
