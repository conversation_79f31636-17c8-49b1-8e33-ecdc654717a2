# -*- coding: utf-8 -*-
"""
مدير قاعدة بيانات Oracle - نسخة محاكاة للاختبار
Oracle Database Manager - Mock Version for Testing
"""

import json
from pathlib import Path
from datetime import datetime
import logging
import sqlite3


class OracleManagerMock:
    """مدير قاعدة بيانات Oracle - نسخة محاكاة"""
    
    def __init__(self, config_file="config/oracle_config.json"):
        self.config_file = Path(config_file)
        self.connection = None
        self.cursor = None
        self.config = {}
        self.load_config()
        
        # استخدام SQLite كمحاكاة لـ Oracle
        self.db_path = Path("data/oracle_mock.db")
        self.db_path.parent.mkdir(exist_ok=True)
        
        # إعداد السجلات
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """تحميل إعدادات قاعدة البيانات"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                # إعدادات افتراضية
                self.config = {
                    "username": "ship2025",
                    "password": "ys123",
                    "dsn": "yemensoft",
                    "encoding": "UTF-8",
                    "mock_mode": True
                }
                self.save_config()
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإعدادات: {e}")
            self.config = {
                "username": "ship2025",
                "password": "ys123",
                "dsn": "yemensoft",
                "encoding": "UTF-8",
                "mock_mode": True
            }
    
    def save_config(self):
        """حفظ إعدادات قاعدة البيانات"""
        try:
            self.config_file.parent.mkdir(exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {e}")
    
    def connect(self):
        """الاتصال بقاعدة البيانات (محاكاة)"""
        try:
            self.logger.info(f"محاولة الاتصال بقاعدة البيانات Oracle (محاكاة)...")
            self.logger.info(f"المستخدم: {self.config['username']}")
            self.logger.info(f"الخادم: {self.config['dsn']}")
            
            # استخدام SQLite كمحاكاة
            self.connection = sqlite3.connect(str(self.db_path))
            self.cursor = self.connection.cursor()
            
            # محاكاة اختبار الاتصال
            self.cursor.execute("SELECT 'ship2025' as user")
            current_user = self.cursor.fetchone()[0]
            
            self.logger.info(f"✅ تم الاتصال بنجاح (محاكاة)!")
            self.logger.info(f"المستخدم الحالي: {current_user}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ فشل في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
            if self.connection:
                self.connection.close()
                self.connection = None
            self.logger.info("تم قطع الاتصال بقاعدة البيانات")
        except Exception as e:
            self.logger.error(f"خطأ في قطع الاتصال: {e}")
    
    def execute_query(self, query, params=None, fetch=True):
        """تنفيذ استعلام (محاكاة Oracle بـ SQLite)"""
        try:
            if not self.connection:
                if not self.connect():
                    raise Exception("فشل في الاتصال بقاعدة البيانات")
            
            # تحويل استعلامات Oracle إلى SQLite
            query = self._convert_oracle_to_sqlite(query)
            
            if params:
                # تحويل معاملات Oracle إلى SQLite
                if isinstance(params, dict):
                    # تحويل :param إلى ?
                    for key, value in params.items():
                        query = query.replace(f":{key}", "?")
                    params = list(params.values())
                
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            if fetch:
                if query.strip().upper().startswith('SELECT'):
                    return self.cursor.fetchall()
                else:
                    self.connection.commit()
                    return self.cursor.rowcount
            else:
                self.connection.commit()
                return self.cursor.rowcount
                
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            raise
    
    def _convert_oracle_to_sqlite(self, query):
        """تحويل استعلامات Oracle إلى SQLite"""
        # تحويلات أساسية
        query = query.replace("SYSDATE", "datetime('now')")
        query = query.replace("CURRENT_TIMESTAMP", "datetime('now')")
        query = query.replace("TO_DATE(", "date(")
        query = query.replace(", 'YYYY-MM-DD')", "")
        query = query.replace("TO_CHAR(", "strftime('%Y-%m-%d', ")
        query = query.replace("UPPER(", "upper(")
        query = query.replace("CLOB", "TEXT")
        query = query.replace("VARCHAR2", "TEXT")
        query = query.replace("NUMBER", "REAL")
        
        return query
    
    def execute_many(self, query, params_list):
        """تنفيذ استعلام متعدد"""
        try:
            if not self.connection:
                if not self.connect():
                    raise Exception("فشل في الاتصال بقاعدة البيانات")
            
            query = self._convert_oracle_to_sqlite(query)
            self.cursor.executemany(query, params_list)
            self.connection.commit()
            return self.cursor.rowcount
            
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام المتعدد: {e}")
            if self.connection:
                self.connection.rollback()
            raise
    
    def get_sequence_next_value(self, sequence_name):
        """الحصول على القيمة التالية من المتسلسلة (محاكاة)"""
        try:
            # محاكاة المتسلسلة باستخدام جدول خاص
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS sequences (
                    name TEXT PRIMARY KEY,
                    current_value INTEGER DEFAULT 0
                )
            """)
            
            # الحصول على القيمة الحالية
            self.cursor.execute("SELECT current_value FROM sequences WHERE name = ?", (sequence_name,))
            result = self.cursor.fetchone()
            
            if result:
                current_value = result[0] + 1
                self.cursor.execute("UPDATE sequences SET current_value = ? WHERE name = ?", 
                                  (current_value, sequence_name))
            else:
                current_value = 1
                self.cursor.execute("INSERT INTO sequences (name, current_value) VALUES (?, ?)", 
                                  (sequence_name, current_value))
            
            self.connection.commit()
            return current_value
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على قيمة المتسلسلة: {e}")
            return None
    
    def table_exists(self, table_name):
        """التحقق من وجود جدول"""
        try:
            query = """
                SELECT COUNT(*) FROM sqlite_master 
                WHERE type='table' AND name = ? COLLATE NOCASE
            """
            result = self.execute_query(query, [table_name.upper()])
            return result[0][0] > 0 if result else False
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من وجود الجدول: {e}")
            return False
    
    def get_table_columns(self, table_name):
        """الحصول على أعمدة الجدول"""
        try:
            query = f"PRAGMA table_info({table_name})"
            return self.execute_query(query)
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على أعمدة الجدول: {e}")
            return []
    
    def test_connection(self):
        """اختبار الاتصال"""
        try:
            if self.connect():
                # اختبار بسيط
                result = self.execute_query("SELECT datetime('now')")
                current_time = result[0][0] if result else None
                
                self.logger.info(f"✅ اختبار الاتصال نجح (محاكاة)")
                self.logger.info(f"الوقت الحالي: {current_time}")
                
                return True
            else:
                return False
        except Exception as e:
            self.logger.error(f"❌ فشل اختبار الاتصال: {e}")
            return False
        finally:
            self.disconnect()
    
    def __enter__(self):
        """دعم context manager"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """إغلاق الاتصال عند الخروج"""
        self.disconnect()


# استخدام النسخة المحاكاة كافتراضية
oracle_manager = OracleManagerMock()


def get_oracle_connection():
    """الحصول على اتصال Oracle (محاكاة)"""
    return oracle_manager


def test_oracle_connection():
    """اختبار اتصال Oracle (محاكاة)"""
    manager = OracleManagerMock()
    return manager.test_connection()


if __name__ == "__main__":
    # اختبار الاتصال
    print("🧪 اختبار اتصال Oracle (محاكاة)...")
    if test_oracle_connection():
        print("✅ الاتصال يعمل بشكل صحيح!")
    else:
        print("❌ فشل في الاتصال!")
