#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CnX ERP - Main Window Interface
واجهة نظام CnX ERP الرئيسية

تطبيق مطابق للتصميم الأصلي مع دعم كامل للغة العربية
"""

import sys
import os
import math
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTreeWidget, QTreeWidgetItem, QFrame,
    QLineEdit, QComboBox, QDateEdit, QSpacerItem, QSizePolicy,
    QScrollArea, QGridLayout, QToolBar, QMenuBar, QStatusBar
)
from PySide6.QtCore import Qt, QSize, QDate, QRect, QPoint
from PySide6.QtGui import (
    QFont, QPixmap, QPainter, QLinearGradient, QColor,
    QIcon, QPalette, QBrush, QAction, QPen, QPainterPath
)
import arabic_reshaper
from bidi.algorithm import get_display

class ArtisticBackgroundWidget(QWidget):
    """ويدجت الخلفية الفنية مع الخطوط المنحنية المتدرجة"""

    def __init__(self):
        super().__init__()
        self.setAutoFillBackground(True)

    def paintEvent(self, event):
        """رسم الخلفية الفنية"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # خلفية بيضاء
        painter.fillRect(self.rect(), QColor(255, 255, 255))

        # رسم الخطوط المنحنية المتدرجة
        self.draw_curved_lines(painter)

    def draw_curved_lines(self, painter):
        """رسم الخطوط المنحنية الملونة"""
        width = self.width()
        height = self.height()

        # الخطوط الحمراء (أعلى يمين)
        self.draw_wave_lines(painter, QColor(229, 62, 62, 30),
                           width * 0.6, 0, width * 0.4, height * 0.4, "red")

        # الخطوط الزرقاء (وسط)
        self.draw_wave_lines(painter, QColor(49, 130, 206, 25),
                           width * 0.2, height * 0.3, width * 0.6, height * 0.4, "blue")

        # الخطوط الخضراء (أسفل يسار)
        self.draw_wave_lines(painter, QColor(56, 161, 105, 20),
                           0, height * 0.6, width * 0.4, height * 0.4, "green")

    def draw_wave_lines(self, painter, color, x, y, w, h, wave_type):
        """رسم مجموعة من الخطوط المتموجة"""
        painter.setPen(QPen(color, 2))

        num_lines = 15
        for i in range(num_lines):
            path = QPainterPath()

            # نقطة البداية
            start_x = x + (i * w / num_lines)
            start_y = y + (i * h / num_lines)

            path.moveTo(start_x, start_y)

            # إنشاء منحنى بيزيه
            if wave_type == "red":
                # منحنيات متجهة للأسفل واليسار
                control1_x = start_x + w * 0.3
                control1_y = start_y + h * 0.2
                control2_x = start_x + w * 0.6
                control2_y = start_y + h * 0.8
                end_x = start_x + w * 0.8
                end_y = start_y + h
            elif wave_type == "blue":
                # منحنيات أفقية متموجة
                control1_x = start_x + w * 0.25
                control1_y = start_y - h * 0.1
                control2_x = start_x + w * 0.75
                control2_y = start_y + h * 0.1
                end_x = start_x + w
                end_y = start_y
            else:  # green
                # منحنيات متجهة للأعلى واليمين
                control1_x = start_x + w * 0.2
                control1_y = start_y - h * 0.8
                control2_x = start_x + w * 0.4
                control2_y = start_y - h * 0.2
                end_x = start_x + w * 0.8
                end_y = start_y - h

            path.cubicTo(control1_x, control1_y, control2_x, control2_y, end_x, end_y)
            painter.drawPath(path)

class CnXERPMainWindow(QMainWindow):
    """النافذة الرئيسية لنظام CnX ERP"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_styles()
        self.create_menu_data()
        
    def init_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("CnX ERP - شركة القدس للتجارة والتوريدات المحدودة - الإدارة المالية 1.7/2024")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1024, 768)
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء المكونات الرئيسية
        self.create_toolbar()
        self.create_left_sidebar()
        self.create_central_area()
        self.create_right_sidebar()
        
        # ترتيب المكونات (RTL)
        main_layout.addWidget(self.right_sidebar)
        main_layout.addWidget(self.central_area, 1)
        main_layout.addWidget(self.left_sidebar)
        
        # شريط الحالة
        self.statusBar().showMessage("مرحباً بك في نظام CnX ERP")
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات العلوي"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)
        toolbar.setFloatable(False)
        
        # إضافة الأدوات
        tools = [
            ("ℹ️", "معلومات", self.show_info),
            ("🔊", "الصوت", self.toggle_sound),
            ("🔔", "التنبيهات", self.show_notifications),
            ("⚙️", "الإعدادات", self.show_settings),
            ("🖨️", "طباعة", self.print_document),
            ("💾", "حفظ", self.save_document),
            ("⭐", "المفضلة", self.toggle_favorite),
            ("🔧", "الأدوات", self.show_tools),
            ("📧", "البريد الإلكتروني", self.open_email)
        ]
        
        for icon, tooltip, callback in tools:
            action = QAction(icon, tooltip, self)
            action.triggered.connect(callback)
            toolbar.addAction(action)
            
    def create_left_sidebar(self):
        """إنشاء الشريط الجانبي الأيسر"""
        self.left_sidebar = QFrame()
        self.left_sidebar.setFixedWidth(220)
        self.left_sidebar.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(self.left_sidebar)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # عنوان القسم
        title = QLabel(self.format_arabic_text("القوائم الرئيسية"))
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # شجرة القوائم
        self.menu_tree = QTreeWidget()
        self.menu_tree.setHeaderHidden(True)
        self.menu_tree.setRootIsDecorated(True)
        layout.addWidget(self.menu_tree)
        
        # إضافة القوائم
        self.populate_menu_tree()
        
    def create_central_area(self):
        """إنشاء المنطقة المركزية"""
        self.central_area = QFrame()
        self.central_area.setFrameStyle(QFrame.NoFrame)
        
        layout = QVBoxLayout(self.central_area)
        layout.setAlignment(Qt.AlignCenter)
        
        # إنشاء منطقة الشعار
        logo_widget = self.create_logo_widget()
        layout.addWidget(logo_widget)
        
        # مساحة مرنة
        layout.addStretch()
        
    def create_right_sidebar(self):
        """إنشاء الشريط الجانبي الأيمن"""
        self.right_sidebar = QFrame()
        self.right_sidebar.setFixedWidth(200)
        self.right_sidebar.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(self.right_sidebar)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # عناصر التحكم
        controls = [
            ("البحث:", QLineEdit()),
            ("التاريخ:", QDateEdit(QDate.currentDate())),
            ("النوع:", QComboBox()),
            ("الحالة:", QComboBox()),
        ]
        
        for label_text, widget in controls:
            label = QLabel(self.format_arabic_text(label_text))
            layout.addWidget(label)
            layout.addWidget(widget)
            layout.addSpacing(10)
            
        # مساحة مرنة
        layout.addStretch()
        
        # أزرار العمليات
        buttons = ["بحث", "تصفية", "تصدير", "طباعة"]
        for btn_text in buttons:
            btn = QPushButton(self.format_arabic_text(btn_text))
            btn.setMinimumHeight(30)
            layout.addWidget(btn)
            
    def create_logo_widget(self):
        """إنشاء ويدجت الشعار والخلفية الفنية"""
        widget = ArtisticBackgroundWidget()
        widget.setMinimumSize(400, 300)

        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)

        # شعار CnX ERP
        logo_label = QLabel("CnX ERP")
        logo_font = QFont("Arial", 48, QFont.Bold)
        logo_label.setFont(logo_font)
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E53E3E, stop:0.5 #3182CE, stop:1 #38A169);
                margin: 20px;
                background: transparent;
            }
        """)
        layout.addWidget(logo_label)

        # النص التوضيحي
        subtitle = QLabel("Enterprise Resource Planning Solutions")
        subtitle.setFont(QFont("Arial", 14))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("color: #666666; margin-bottom: 20px; background: transparent;")
        layout.addWidget(subtitle)

        # النص العربي
        arabic_text = QLabel(self.format_arabic_text("حلول تخطيط موارد المؤسسات"))
        arabic_text.setFont(QFont("Arial", 12))
        arabic_text.setAlignment(Qt.AlignCenter)
        arabic_text.setStyleSheet("color: #888888; background: transparent;")
        layout.addWidget(arabic_text)

        return widget
        
    def populate_menu_tree(self):
        """ملء شجرة القوائم بالبيانات"""
        menu_items = [
            "📊 التقرير الإحصائي",
            "🏢 مركز التكلفة", 
            "📋 أوامر الشراء",
            "📦 بيانات الأصناف",
            "📈 بيانات وحسابات",
            "💰 سجل الأرصدة",
            "📋 قائمة الجرد/العمل",
            "📊 تقرير الأرصدة الحالية",
            "📈 تقرير حركة المخزون",
            "📋 تقارير الحركات المالية"
        ]
        
        for item_text in menu_items:
            item = QTreeWidgetItem([self.format_arabic_text(item_text)])
            self.menu_tree.addTopLevelItem(item)
            
    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
            
    def setup_styles(self):
        """إعداد الأنماط والألوان"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F5F5F5;
            }
            
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
            }
            
            QTreeWidget {
                background-color: white;
                border: 1px solid #E0E0E0;
                font-size: 11px;
                padding: 5px;
            }
            
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F0F0F0;
            }
            
            QTreeWidget::item:hover {
                background-color: #E3F2FD;
            }
            
            QTreeWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #1976D2;
            }
            
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            
            QLineEdit, QComboBox, QDateEdit {
                padding: 6px;
                border: 1px solid #CCCCCC;
                border-radius: 4px;
                background-color: white;
            }
            
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                border-color: #2196F3;
            }
            
            QLabel {
                color: #333333;
            }
            
            QToolBar {
                background-color: #E3F2FD;
                border: none;
                spacing: 5px;
                padding: 5px;
            }
            
            QStatusBar {
                background-color: #F5F5F5;
                border-top: 1px solid #E0E0E0;
            }
        """)
        
    # وظائف الأحداث
    def show_info(self): pass
    def toggle_sound(self): pass  
    def show_notifications(self): pass
    def show_settings(self): pass
    def print_document(self): pass
    def save_document(self): pass
    def toggle_favorite(self): pass
    def show_tools(self): pass
    def open_email(self): pass

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = CnXERPMainWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
