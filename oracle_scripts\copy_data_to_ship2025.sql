-- سكريبت نسخ البيانات من ias20241 إلى ship2025
-- Copy Data Script from ias20241 to ship2025
-- تم إنشاؤه في: 2025-07-12 13:50:35

-- الاتصال بالمستخدم الجديد
CONNECT ship2025/ys123@yemensoft;

SET SERVEROUTPUT ON;

BEGIN
    DBMS_OUTPUT.PUT_LINE('🚀 بدء نسخ البيانات...');
END;
/

-- نسخ جدول BRANCHES (1,000 صف)
INSERT INTO BRANCHES
SELECT * FROM ias20241.BRANCHES;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول BRANCHES: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- نسخ جدول CURRENCIES (1,000 صف)
INSERT INTO CURRENCIES
SELECT * FROM ias20241.CURRENCIES;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول CURRENCIES: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- نسخ جدول USERS (1,000 صف)
INSERT INTO USERS
SELECT * FROM ias20241.USERS;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول USERS: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- نسخ جدول CUSTOMERS (15,000 صف)
INSERT INTO CUSTOMERS
SELECT * FROM ias20241.CUSTOMERS;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول CUSTOMERS: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- نسخ جدول SUPPLIERS (1,000 صف)
INSERT INTO SUPPLIERS
SELECT * FROM ias20241.SUPPLIERS;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول SUPPLIERS: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- نسخ جدول BANKS (1,000 صف)
INSERT INTO BANKS
SELECT * FROM ias20241.BANKS;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول BANKS: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- نسخ جدول EXCHANGE_RATES (1,000 صف)
INSERT INTO EXCHANGE_RATES
SELECT * FROM ias20241.EXCHANGE_RATES;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول EXCHANGE_RATES: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- نسخ جدول REMITTANCES (45,000 صف)
INSERT INTO REMITTANCES
SELECT * FROM ias20241.REMITTANCES;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول REMITTANCES: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- نسخ جدول TRANSACTIONS (1,000 صف)
INSERT INTO TRANSACTIONS
SELECT * FROM ias20241.TRANSACTIONS;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول TRANSACTIONS: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- نسخ جدول REPORTS (1,000 صف)
INSERT INTO REPORTS
SELECT * FROM ias20241.REPORTS;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم نسخ جدول REPORTS: ' || SQL%ROWCOUNT || ' صف');
END;
/

COMMIT;

-- تحديث المتسلسلات
-- Update Sequences

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(BRANCHE_ID), 0) + 1 INTO v_max_id FROM BRANCHES;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_BRANCHES';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_BRANCHES START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة BRANCHES: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة BRANCHES: ' || SQLERRM);
END;
/

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(CURRENCIE_ID), 0) + 1 INTO v_max_id FROM CURRENCIES;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_CURRENCIES';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_CURRENCIES START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة CURRENCIES: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة CURRENCIES: ' || SQLERRM);
END;
/

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(USER_ID), 0) + 1 INTO v_max_id FROM USERS;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_USERS';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_USERS START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة USERS: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة USERS: ' || SQLERRM);
END;
/

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(CUSTOMER_ID), 0) + 1 INTO v_max_id FROM CUSTOMERS;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_CUSTOMERS';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_CUSTOMERS START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة CUSTOMERS: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة CUSTOMERS: ' || SQLERRM);
END;
/

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(SUPPLIER_ID), 0) + 1 INTO v_max_id FROM SUPPLIERS;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_SUPPLIERS';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_SUPPLIERS START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة SUPPLIERS: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة SUPPLIERS: ' || SQLERRM);
END;
/

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(BANK_ID), 0) + 1 INTO v_max_id FROM BANKS;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_BANKS';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_BANKS START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة BANKS: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة BANKS: ' || SQLERRM);
END;
/

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(EXCHANGE_RATE_ID), 0) + 1 INTO v_max_id FROM EXCHANGE_RATES;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_EXCHANGE_RATES';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_EXCHANGE_RATES START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة EXCHANGE_RATES: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة EXCHANGE_RATES: ' || SQLERRM);
END;
/

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(REMITTANCE_ID), 0) + 1 INTO v_max_id FROM REMITTANCES;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_REMITTANCES';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_REMITTANCES START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة REMITTANCES: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة REMITTANCES: ' || SQLERRM);
END;
/

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(TRANSACTION_ID), 0) + 1 INTO v_max_id FROM TRANSACTIONS;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_TRANSACTIONS';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_TRANSACTIONS START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة TRANSACTIONS: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة TRANSACTIONS: ' || SQLERRM);
END;
/

DECLARE
    v_max_id NUMBER;
BEGIN
    SELECT NVL(MAX(REPORT_ID), 0) + 1 INTO v_max_id FROM REPORTS;
    EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_REPORTS';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_REPORTS START WITH ' || v_max_id || ' INCREMENT BY 1 NOCACHE NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('✅ تم تحديث متسلسلة REPORTS: ' || v_max_id);
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في تحديث متسلسلة REPORTS: ' || SQLERRM);
END;
/

BEGIN
    DBMS_OUTPUT.PUT_LINE('🎉 تم إكمال نسخ جميع البيانات بنجاح!');
END;
/

COMMIT;