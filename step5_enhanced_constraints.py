#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ محسن للقيود والعلاقات
Enhanced constraints copy
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class EnhancedConstraintCopier:
    """ناسخ محسن للقيود"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'primary_keys_created': 0,
            'unique_constraints_created': 0,
            'foreign_keys_created': 0,
            'failed_constraints': 0,
            'failed_list': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def copy_primary_keys(self):
        """نسخ المفاتيح الأساسية"""
        print("\n🔑 نسخ المفاتيح الأساسية...")
        
        try:
            source_cursor = self.source_conn.cursor()
            target_cursor = self.target_conn.cursor()
            
            # الحصول على المفاتيح الأساسية من المصدر
            source_cursor.execute("""
                SELECT c.constraint_name, c.table_name,
                       LISTAGG(cc.column_name, ', ') WITHIN GROUP (ORDER BY cc.position) as columns
                FROM user_constraints c, user_cons_columns cc
                WHERE c.constraint_name = cc.constraint_name
                AND c.constraint_type = 'P'
                AND c.table_name NOT LIKE 'BIN$%'
                GROUP BY c.constraint_name, c.table_name
                ORDER BY c.table_name
            """)
            
            primary_keys = source_cursor.fetchall()
            print(f"   📊 تم العثور على {len(primary_keys)} مفتاح أساسي")
            
            for pk_name, table_name, columns in primary_keys:
                try:
                    # التحقق من وجود الجدول في الهدف
                    target_cursor.execute("""
                        SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
                    """, {'table_name': table_name})
                    
                    if target_cursor.fetchone()[0] == 0:
                        continue
                    
                    # التحقق من عدم وجود المفتاح الأساسي
                    target_cursor.execute("""
                        SELECT COUNT(*) FROM user_constraints 
                        WHERE table_name = :table_name AND constraint_type = 'P'
                    """, {'table_name': table_name})
                    
                    if target_cursor.fetchone()[0] > 0:
                        continue  # يوجد مفتاح أساسي بالفعل
                    
                    # إنشاء المفتاح الأساسي
                    ddl = f"ALTER TABLE {table_name} ADD CONSTRAINT {pk_name} PRIMARY KEY ({columns})"
                    target_cursor.execute(ddl)
                    self.target_conn.commit()
                    
                    print(f"   ✅ تم إنشاء المفتاح الأساسي: {pk_name} على {table_name}")
                    self.stats['primary_keys_created'] += 1
                    
                except Exception as e:
                    print(f"   ❌ فشل في إنشاء المفتاح الأساسي {pk_name}: {e}")
                    self.stats['failed_constraints'] += 1
                    self.stats['failed_list'].append(pk_name)
            
            source_cursor.close()
            target_cursor.close()
            
            print(f"   ✅ تم إنشاء {self.stats['primary_keys_created']} مفتاح أساسي")
            
        except Exception as e:
            print(f"   ❌ خطأ في نسخ المفاتيح الأساسية: {e}")
    
    def copy_unique_constraints(self):
        """نسخ القيود الفريدة"""
        print("\n🔒 نسخ القيود الفريدة...")
        
        try:
            source_cursor = self.source_conn.cursor()
            target_cursor = self.target_conn.cursor()
            
            # الحصول على القيود الفريدة من المصدر
            source_cursor.execute("""
                SELECT c.constraint_name, c.table_name,
                       LISTAGG(cc.column_name, ', ') WITHIN GROUP (ORDER BY cc.position) as columns
                FROM user_constraints c, user_cons_columns cc
                WHERE c.constraint_name = cc.constraint_name
                AND c.constraint_type = 'U'
                AND c.table_name NOT LIKE 'BIN$%'
                GROUP BY c.constraint_name, c.table_name
                ORDER BY c.table_name
            """)
            
            unique_constraints = source_cursor.fetchall()
            print(f"   📊 تم العثور على {len(unique_constraints)} قيد فريد")
            
            for uk_name, table_name, columns in unique_constraints:
                try:
                    # التحقق من وجود الجدول في الهدف
                    target_cursor.execute("""
                        SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
                    """, {'table_name': table_name})
                    
                    if target_cursor.fetchone()[0] == 0:
                        continue
                    
                    # التحقق من عدم وجود القيد
                    target_cursor.execute("""
                        SELECT COUNT(*) FROM user_constraints 
                        WHERE constraint_name = :constraint_name
                    """, {'constraint_name': uk_name})
                    
                    if target_cursor.fetchone()[0] > 0:
                        continue  # القيد موجود بالفعل
                    
                    # إنشاء القيد الفريد
                    ddl = f"ALTER TABLE {table_name} ADD CONSTRAINT {uk_name} UNIQUE ({columns})"
                    target_cursor.execute(ddl)
                    self.target_conn.commit()
                    
                    print(f"   ✅ تم إنشاء القيد الفريد: {uk_name} على {table_name}")
                    self.stats['unique_constraints_created'] += 1
                    
                except Exception as e:
                    print(f"   ❌ فشل في إنشاء القيد الفريد {uk_name}: {e}")
                    self.stats['failed_constraints'] += 1
                    self.stats['failed_list'].append(uk_name)
            
            source_cursor.close()
            target_cursor.close()
            
            print(f"   ✅ تم إنشاء {self.stats['unique_constraints_created']} قيد فريد")
            
        except Exception as e:
            print(f"   ❌ خطأ في نسخ القيود الفريدة: {e}")
    
    def copy_foreign_keys(self):
        """نسخ المفاتيح الخارجية"""
        print("\n🔗 نسخ المفاتيح الخارجية...")
        
        try:
            source_cursor = self.source_conn.cursor()
            target_cursor = self.target_conn.cursor()
            
            # الحصول على المفاتيح الخارجية من المصدر
            source_cursor.execute("""
                SELECT c.constraint_name, c.table_name, c.r_constraint_name, c.delete_rule,
                       LISTAGG(cc.column_name, ', ') WITHIN GROUP (ORDER BY cc.position) as columns,
                       r.table_name as ref_table,
                       LISTAGG(rc.column_name, ', ') WITHIN GROUP (ORDER BY rc.position) as ref_columns
                FROM user_constraints c, user_cons_columns cc, 
                     user_constraints r, user_cons_columns rc
                WHERE c.constraint_name = cc.constraint_name
                AND c.r_constraint_name = r.constraint_name
                AND r.constraint_name = rc.constraint_name
                AND c.constraint_type = 'R'
                AND c.table_name NOT LIKE 'BIN$%'
                GROUP BY c.constraint_name, c.table_name, c.r_constraint_name, 
                         c.delete_rule, r.table_name
                ORDER BY c.table_name
            """)
            
            foreign_keys = source_cursor.fetchall()
            print(f"   📊 تم العثور على {len(foreign_keys)} مفتاح خارجي")
            
            for fk_name, table_name, r_constraint_name, delete_rule, columns, ref_table, ref_columns in foreign_keys:
                try:
                    # التحقق من وجود الجدولين في الهدف
                    target_cursor.execute("""
                        SELECT COUNT(*) FROM user_tables WHERE table_name IN (:table_name, :ref_table)
                    """, {'table_name': table_name, 'ref_table': ref_table})
                    
                    if target_cursor.fetchone()[0] < 2:
                        continue  # أحد الجدولين غير موجود
                    
                    # التحقق من وجود المفتاح الأساسي المرجعي
                    target_cursor.execute("""
                        SELECT COUNT(*) FROM user_constraints 
                        WHERE table_name = :ref_table AND constraint_type = 'P'
                    """, {'ref_table': ref_table})
                    
                    if target_cursor.fetchone()[0] == 0:
                        continue  # المفتاح الأساسي المرجعي غير موجود
                    
                    # التحقق من عدم وجود المفتاح الخارجي
                    target_cursor.execute("""
                        SELECT COUNT(*) FROM user_constraints 
                        WHERE constraint_name = :constraint_name
                    """, {'constraint_name': fk_name})
                    
                    if target_cursor.fetchone()[0] > 0:
                        continue  # المفتاح موجود بالفعل
                    
                    # إنشاء المفتاح الخارجي
                    ddl = f"ALTER TABLE {table_name} ADD CONSTRAINT {fk_name} FOREIGN KEY ({columns}) REFERENCES {ref_table} ({ref_columns})"
                    
                    # إضافة قاعدة الحذف إذا وجدت
                    if delete_rule and delete_rule != 'NO ACTION':
                        if delete_rule == 'CASCADE':
                            ddl += " ON DELETE CASCADE"
                        elif delete_rule == 'SET NULL':
                            ddl += " ON DELETE SET NULL"
                    
                    target_cursor.execute(ddl)
                    self.target_conn.commit()
                    
                    print(f"   ✅ تم إنشاء المفتاح الخارجي: {fk_name} ({table_name} → {ref_table})")
                    self.stats['foreign_keys_created'] += 1
                    
                except Exception as e:
                    print(f"   ❌ فشل في إنشاء المفتاح الخارجي {fk_name}: {e}")
                    self.stats['failed_constraints'] += 1
                    self.stats['failed_list'].append(fk_name)
            
            source_cursor.close()
            target_cursor.close()
            
            print(f"   ✅ تم إنشاء {self.stats['foreign_keys_created']} مفتاح خارجي")
            
        except Exception as e:
            print(f"   ❌ خطأ في نسخ المفاتيح الخارجية: {e}")
    
    def run_enhanced_copy(self):
        """تشغيل النسخ المحسن للقيود"""
        print("🚀 بدء النسخ المحسن للقيود والعلاقات")
        print("=" * 60)
        
        start_time = datetime.now()
        
        try:
            if not self.connect():
                return False
            
            # نسخ القيود بالترتيب الصحيح
            self.copy_primary_keys()
            self.copy_unique_constraints()
            self.copy_foreign_keys()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            # النتائج النهائية
            print("\n" + "=" * 60)
            print("🎉 تم إكمال النسخ المحسن للقيود!")
            print(f"🔑 المفاتيح الأساسية: {self.stats['primary_keys_created']}")
            print(f"🔒 القيود الفريدة: {self.stats['unique_constraints_created']}")
            print(f"🔗 المفاتيح الخارجية: {self.stats['foreign_keys_created']}")
            print(f"❌ القيود الفاشلة: {self.stats['failed_constraints']}")
            print(f"⏱️ المدة: {duration}")
            
            if self.stats['failed_list']:
                print(f"\n❌ القيود الفاشلة:")
                for constraint in self.stats['failed_list'][:10]:  # أول 10
                    print(f"   - {constraint}")
                if len(self.stats['failed_list']) > 10:
                    print(f"   ... و {len(self.stats['failed_list']) - 10} قيد آخر")
            
            print("=" * 60)
            
            total_created = (self.stats['primary_keys_created'] + 
                           self.stats['unique_constraints_created'] + 
                           self.stats['foreign_keys_created'])
            
            return total_created > 0
            
        except Exception as e:
            print(f"❌ خطأ في النسخ المحسن: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = EnhancedConstraintCopier()
    success = copier.run_enhanced_copy()
    
    if success:
        print("\n✅ النسخ المحسن للقيود مكتمل!")
        
        # تشغيل فحص النتائج
        print("\n🔍 فحص النتائج النهائية...")
        os.system("python check_constraints_results.py")
        
    else:
        print("\n❌ فشل النسخ المحسن للقيود!")
    
    return success


if __name__ == "__main__":
    main()
