#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التصميم الجديد للنافذة الرئيسية
Test New Design for Main Window
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from main_window_new_design import CnXMainWindow


def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار التصميم الجديد للنافذة الرئيسية")
    print("=" * 60)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # تطبيق ثيم إضافي
    app.setStyleSheet("""
        QApplication {
            font-family: "Segoe UI", "<PERSON><PERSON><PERSON>", "Aria<PERSON>", sans-serif;
        }
        QMessageBox {
            background-color: white;
            color: #374151;
        }
        QMessageBox QPushButton {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
            min-width: 80px;
        }
        QMessageBox QPushButton:hover {
            background-color: #2563eb;
        }
        QMessageBox QPushButton:pressed {
            background-color: #1d4ed8;
        }
    """)
    
    try:
        # إنشاء النافذة الرئيسية
        window = CnXMainWindow()
        window.show()
        
        print("✅ تم تشغيل النافذة الرئيسية بنجاح!")
        print("📋 التصميم الجديد يتضمن:")
        print("   🏠 لوحة التحكم")
        print("   📊 إنشاء شحنة جديدة")
        print("   🔍 البحث في الشحنات")
        print("   📋 قائمة الشحنات")
        print("   🏢 إدارة الشحنة")
        print("   🌐 متابعة الشحنات التجاري")
        print("   📦 إدارة الأصناف")
        print("   🏪 إدارة الموردين")
        print("   💸 إدارة الحوالات")
        print("   🗄️ قاعدة البيانات")
        print("   📊 التقارير المالية")
        print("   📈 تقارير الشحن")
        print("   ⚙️ إعدادات النظام")
        print()
        print("🎯 اختر أي عنصر من القائمة الجانبية لاختبار الوظائف")
        print("🎨 التصميم يطابق الصورة المرفقة مع الألوان المتدرجة")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة الرئيسية: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في تشغيل النافذة الرئيسية:\n{str(e)}")
        return False


if __name__ == "__main__":
    main()
