#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص شامل لجداول المستخدم ias20241
Comprehensive analysis of ias20241 user tables
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def analyze_source_database():
    """فحص شامل لقاعدة البيانات المصدر"""
    setup_environment()
    
    print("🔍 فحص شامل لجداول المستخدم ias20241")
    print("=" * 60)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        cursor = conn.cursor()
        
        # فحص الجداول
        print("📋 فحص الجداول:")
        cursor.execute("""
            SELECT COUNT(*) FROM user_tables WHERE table_name NOT LIKE 'BIN$%'
        """)
        table_count = cursor.fetchone()[0]
        print(f"   إجمالي الجداول: {table_count:,}")
        
        # فحص البيانات
        print("\n📊 فحص البيانات:")
        cursor.execute("""
            SELECT SUM(num_rows) FROM user_tables 
            WHERE table_name NOT LIKE 'BIN$%' AND num_rows IS NOT NULL
        """)
        total_rows = cursor.fetchone()[0] or 0
        print(f"   إجمالي الصفوف: {total_rows:,}")
        
        # فحص الفهارس
        print("\n📊 فحص الفهارس:")
        cursor.execute("""
            SELECT COUNT(*) FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%' AND index_name NOT LIKE 'SYS_%'
        """)
        index_count = cursor.fetchone()[0]
        print(f"   إجمالي الفهارس: {index_count:,}")
        
        # فحص القيود
        print("\n🔒 فحص القيود:")
        cursor.execute("""
            SELECT constraint_type, COUNT(*) 
            FROM user_constraints 
            WHERE constraint_type IN ('P', 'R', 'U', 'C')
            AND table_name NOT LIKE 'BIN$%'
            GROUP BY constraint_type
            ORDER BY constraint_type
        """)
        constraints = cursor.fetchall()
        
        constraint_names = {'P': 'مفاتيح أساسية', 'R': 'مفاتيح خارجية', 'U': 'قيود فريدة', 'C': 'قيود فحص'}
        for cons_type, count in constraints:
            print(f"   {constraint_names.get(cons_type, cons_type)}: {count:,}")
        
        # فحص المشاهد
        print("\n👁️ فحص المشاهد:")
        cursor.execute("SELECT COUNT(*) FROM user_views WHERE view_name NOT LIKE 'BIN$%'")
        view_count = cursor.fetchone()[0]
        print(f"   إجمالي المشاهد: {view_count:,}")
        
        # فحص الإجراءات والدوال
        print("\n⚙️ فحص الإجراءات والدوال:")
        cursor.execute("""
            SELECT object_type, COUNT(*) 
            FROM user_objects 
            WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
            AND object_name NOT LIKE 'BIN$%' AND status = 'VALID'
            GROUP BY object_type
            ORDER BY object_type
        """)
        objects = cursor.fetchall()
        
        object_names = {'FUNCTION': 'دوال', 'PACKAGE': 'حزم', 'PACKAGE BODY': 'أجسام الحزم', 'PROCEDURE': 'إجراءات'}
        for obj_type, count in objects:
            print(f"   {object_names.get(obj_type, obj_type)}: {count:,}")
        
        # فحص المشغلات
        print("\n⚡ فحص المشغلات:")
        cursor.execute("""
            SELECT COUNT(*) FROM user_triggers 
            WHERE trigger_name NOT LIKE 'BIN$%' AND status = 'ENABLED'
        """)
        trigger_count = cursor.fetchone()[0]
        print(f"   إجمالي المشغلات: {trigger_count:,}")
        
        # فحص التسلسلات
        print("\n🔢 فحص التسلسلات:")
        cursor.execute("SELECT COUNT(*) FROM user_sequences WHERE sequence_name NOT LIKE 'BIN$%'")
        sequence_count = cursor.fetchone()[0]
        print(f"   إجمالي التسلسلات: {sequence_count:,}")
        
        # عينة من الجداول الكبيرة
        print("\n📊 أكبر 10 جداول:")
        cursor.execute("""
            SELECT table_name, num_rows 
            FROM user_tables 
            WHERE table_name NOT LIKE 'BIN$%' AND num_rows IS NOT NULL
            ORDER BY num_rows DESC
            FETCH FIRST 10 ROWS ONLY
        """)
        large_tables = cursor.fetchall()
        
        for i, (table_name, num_rows) in enumerate(large_tables, 1):
            print(f"   {i:2d}. {table_name}: {num_rows:,} صف")
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 60)
        print("✅ تم إكمال فحص قاعدة البيانات المصدر!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    success = analyze_source_database()
    
    if success:
        print("🎉 فحص قاعدة البيانات مكتمل!")
    else:
        print("💥 فشل في فحص قاعدة البيانات!")
    
    return success


if __name__ == "__main__":
    main()
