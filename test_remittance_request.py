#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة طلب الحوالة المحدثة
Test Remittance Request Window
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer
from src.ui.remittances.remittance_request_window import RemittanceRequestWindow


def test_window_creation():
    """اختبار إنشاء النافذة"""
    print("🧪 اختبار إنشاء النافذة...")
    try:
        window = RemittanceRequestWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        return window
    except Exception as e:
        print(f"❌ فشل في إنشاء النافذة: {e}")
        return None


def test_database_creation(window):
    """اختبار إنشاء قاعدة البيانات"""
    print("🧪 اختبار إنشاء قاعدة البيانات...")
    try:
        window.init_database()
        
        # التحقق من وجود ملف قاعدة البيانات
        db_path = Path("data/proshipment.db")
        if db_path.exists():
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            return True
        else:
            print("❌ لم يتم إنشاء ملف قاعدة البيانات")
            return False
    except Exception as e:
        print(f"❌ فشل في إنشاء قاعدة البيانات: {e}")
        return False


def test_form_validation(window):
    """اختبار التحقق من صحة النموذج"""
    print("🧪 اختبار التحقق من صحة النموذج...")
    
    # اختبار نموذج فارغ
    if window.validate_form():
        print("❌ التحقق فشل - يجب أن يرفض النموذج الفارغ")
        return False
    
    # ملء البيانات الأساسية
    window.amount.setValue(1000.0)
    window.purpose.setText("راتب")
    window.sender_name.setText("أحمد محمد")
    window.receiver_name.setText("محمد أحمد")
    
    if window.validate_form():
        print("✅ التحقق نجح مع البيانات الصحيحة")
        return True
    else:
        print("❌ التحقق فشل مع البيانات الصحيحة")
        return False


def test_data_collection(window):
    """اختبار جمع البيانات"""
    print("🧪 اختبار جمع البيانات...")
    try:
        data = window.collect_form_data()
        
        required_fields = [
            'request_number', 'request_date', 'amount', 'currency', 'purpose',
            'sender_name', 'receiver_name', 'status', 'created_at'
        ]
        
        for field in required_fields:
            if field not in data:
                print(f"❌ الحقل المطلوب مفقود: {field}")
                return False
        
        print("✅ تم جمع البيانات بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في جمع البيانات: {e}")
        return False


def test_save_request(window):
    """اختبار حفظ الطلب"""
    print("🧪 اختبار حفظ الطلب...")
    try:
        # ملء النموذج ببيانات اختبار
        window.amount.setValue(1500.0)
        window.currency.setCurrentText("ريال سعودي")
        window.purpose.setText("مساعدة عائلية")
        window.sender_name.setText("سارة أحمد")
        window.sender_phone.setText("0501234567")
        window.receiver_name.setText("فاطمة محمد")
        window.receiver_country.setCurrentText("الأردن")
        window.notes.setPlainText("طلب عاجل")
        
        # محاولة الحفظ
        window.save_request()
        print("✅ تم حفظ الطلب بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في حفظ الطلب: {e}")
        return False


def test_refresh_list(window):
    """اختبار تحديث القائمة"""
    print("🧪 اختبار تحديث القائمة...")
    try:
        window.refresh_requests_list()
        
        # التحقق من وجود بيانات في الجدول
        row_count = window.requests_table.rowCount()
        if row_count > 0:
            print(f"✅ تم تحديث القائمة بنجاح - {row_count} طلب موجود")
            return True
        else:
            print("⚠️ القائمة فارغة - قد يكون هذا طبيعياً")
            return True
    except Exception as e:
        print(f"❌ فشل في تحديث القائمة: {e}")
        return False


def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار نافذة طلب الحوالة المحدثة")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # اختبار إنشاء النافذة
    window = test_window_creation()
    if not window:
        print("❌ فشل الاختبار - لا يمكن إنشاء النافذة")
        return False
    
    # اختبار قاعدة البيانات
    if not test_database_creation(window):
        print("❌ فشل الاختبار - مشكلة في قاعدة البيانات")
        return False
    
    # اختبار التحقق من النموذج
    if not test_form_validation(window):
        print("❌ فشل الاختبار - مشكلة في التحقق من النموذج")
        return False
    
    # اختبار جمع البيانات
    if not test_data_collection(window):
        print("❌ فشل الاختبار - مشكلة في جمع البيانات")
        return False
    
    # اختبار حفظ الطلب
    if not test_save_request(window):
        print("❌ فشل الاختبار - مشكلة في حفظ الطلب")
        return False
    
    # اختبار تحديث القائمة
    if not test_refresh_list(window):
        print("❌ فشل الاختبار - مشكلة في تحديث القائمة")
        return False
    
    print("=" * 50)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ نافذة طلب الحوالة تعمل بشكل مثالي")
    
    # عرض النافذة للاختبار اليدوي
    window.show()
    
    # إغلاق تلقائي بعد 10 ثواني (اختياري)
    QTimer.singleShot(10000, app.quit)
    
    return True


if __name__ == "__main__":
    try:
        success = run_all_tests()
        if success:
            print("\n🎊 الاختبار مكتمل بنجاح!")
            print("📝 يمكنك الآن استخدام نافذة طلب الحوالة بثقة")
        else:
            print("\n💥 فشل في الاختبار!")
            print("🔧 يرجى مراجعة الأخطاء وإصلاحها")
    except Exception as e:
        print(f"\n💥 خطأ عام في الاختبار: {e}")
        sys.exit(1)
