{"CUSTOMERS": {"name": "CUSTOMERS", "num_rows": 15000, "blocks": 250, "avg_row_len": 180, "columns": [{"name": "CUSTOMER_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "CUSTOMER_NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "CUSTOMER_NAME_EN", "data_type": "VARCHAR2", "data_length": 100, "nullable": "Y"}, {"name": "ID_NUMBER", "data_type": "VARCHAR2", "data_length": 20, "nullable": "Y"}, {"name": "PHONE", "data_type": "VARCHAR2", "data_length": 20, "nullable": "Y"}, {"name": "EMAIL", "data_type": "VARCHAR2", "data_length": 100, "nullable": "Y"}, {"name": "ADDRESS", "data_type": "VARCHAR2", "data_length": 200, "nullable": "Y"}, {"name": "NATIONALITY", "data_type": "VARCHAR2", "data_length": 50, "nullable": "Y"}, {"name": "CREATED_DATE", "data_type": "DATE", "data_length": 7, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N"}], "indexes": [{"name": "PK_CUSTOMERS", "type": "NORMAL", "uniqueness": "UNIQUE", "status": "VALID"}], "constraints": [{"name": "PK_CUSTOMERS", "type": "P", "status": "ENABLED", "validated": "VALIDATED"}]}, "REMITTANCES": {"name": "REMITTANCES", "num_rows": 45000, "blocks": 800, "avg_row_len": 220, "columns": [{"name": "REMITTANCE_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "REMITTANCE_NUMBER", "data_type": "VARCHAR2", "data_length": 20, "nullable": "N"}, {"name": "CUSTOMER_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "SENDER_NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "RECEIVER_NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "AMOUNT", "data_type": "NUMBER", "data_length": 22, "data_precision": 15, "data_scale": 2, "nullable": "N"}, {"name": "CURRENCY_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "EXCHANGE_RATE", "data_type": "NUMBER", "data_length": 22, "data_precision": 10, "data_scale": 4, "nullable": "N"}, {"name": "BRANCH_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "USER_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "REMITTANCE_DATE", "data_type": "DATE", "data_length": 7, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 20, "nullable": "N"}, {"name": "NOTES", "data_type": "CLOB", "data_length": 4000, "nullable": "Y"}], "indexes": [{"name": "PK_REMITTANCES", "type": "NORMAL", "uniqueness": "UNIQUE", "status": "VALID"}, {"name": "IDX_REMITTANCES_DATE", "type": "NORMAL", "uniqueness": "NONUNIQUE", "status": "VALID"}], "constraints": [{"name": "PK_REMITTANCES", "type": "P", "status": "ENABLED", "validated": "VALIDATED"}, {"name": "FK_REMITTANCES_CUSTOMER", "type": "R", "status": "ENABLED", "validated": "VALIDATED"}]}, "BRANCHES": {"name": "BRANCHES", "num_rows": 1000, "blocks": 50, "avg_row_len": 150, "columns": [{"name": "BRANCHE_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N"}], "indexes": [], "constraints": []}, "CURRENCIES": {"name": "CURRENCIES", "num_rows": 1000, "blocks": 50, "avg_row_len": 150, "columns": [{"name": "CURRENCIE_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N"}], "indexes": [], "constraints": []}, "EXCHANGE_RATES": {"name": "EXCHANGE_RATES", "num_rows": 1000, "blocks": 50, "avg_row_len": 150, "columns": [{"name": "EXCHANGE_RATE_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N"}], "indexes": [], "constraints": []}, "TRANSACTIONS": {"name": "TRANSACTIONS", "num_rows": 1000, "blocks": 50, "avg_row_len": 150, "columns": [{"name": "TRANSACTION_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N"}], "indexes": [], "constraints": []}, "USERS": {"name": "USERS", "num_rows": 1000, "blocks": 50, "avg_row_len": 150, "columns": [{"name": "USER_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N"}], "indexes": [], "constraints": []}, "SUPPLIERS": {"name": "SUPPLIERS", "num_rows": 1000, "blocks": 50, "avg_row_len": 150, "columns": [{"name": "SUPPLIER_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N"}], "indexes": [], "constraints": []}, "BANKS": {"name": "BANKS", "num_rows": 1000, "blocks": 50, "avg_row_len": 150, "columns": [{"name": "BANK_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N"}], "indexes": [], "constraints": []}, "REPORTS": {"name": "REPORTS", "num_rows": 1000, "blocks": 50, "avg_row_len": 150, "columns": [{"name": "REPORT_ID", "data_type": "NUMBER", "data_length": 22, "nullable": "N"}, {"name": "NAME", "data_type": "VARCHAR2", "data_length": 100, "nullable": "N"}, {"name": "STATUS", "data_type": "VARCHAR2", "data_length": 10, "nullable": "N"}], "indexes": [], "constraints": []}}