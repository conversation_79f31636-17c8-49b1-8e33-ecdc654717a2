#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص نتائج النسخ
Check copy results
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def check_source_tables():
    """فحص الجداول في المصدر"""
    try:
        print("🔍 فحص الجداول في المصدر (ias20241)...")
        
        conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM user_tables WHERE table_name NOT LIKE 'BIN$%'")
        source_count = cursor.fetchone()[0]
        
        print(f"📊 عدد الجداول في المصدر: {source_count}")
        
        cursor.close()
        conn.close()
        
        return source_count
        
    except Exception as e:
        print(f"❌ خطأ في فحص المصدر: {e}")
        return 0


def check_target_tables():
    """فحص الجداول في الهدف"""
    try:
        print("🔍 فحص الجداول في الهدف (ship2025)...")
        
        conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM user_tables")
        target_count = cursor.fetchone()[0]
        
        print(f"📊 عدد الجداول في الهدف: {target_count}")
        
        if target_count > 0:
            print("\n📋 أول 20 جدول في الهدف:")
            cursor.execute("""
                SELECT table_name, num_rows 
                FROM user_tables 
                ORDER BY table_name
            """)
            
            tables = cursor.fetchall()
            for i, (table_name, num_rows) in enumerate(tables[:20]):
                print(f"   {i+1:2d}. {table_name}: {num_rows or 0} صف")
            
            if len(tables) > 20:
                print(f"   ... و {len(tables) - 20} جدول آخر")
        
        cursor.close()
        conn.close()
        
        return target_count
        
    except Exception as e:
        print(f"❌ خطأ في فحص الهدف: {e}")
        return 0


def test_table_structure():
    """اختبار بنية جدول"""
    try:
        print("\n🧪 اختبار بنية الجداول...")
        
        conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        cursor = conn.cursor()
        
        # الحصول على أول جدول
        cursor.execute("SELECT table_name FROM user_tables WHERE ROWNUM = 1")
        result = cursor.fetchone()
        
        if result:
            table_name = result[0]
            print(f"📋 اختبار بنية الجدول: {table_name}")
            
            # فحص الأعمدة
            cursor.execute("""
                SELECT column_name, data_type, data_length, nullable
                FROM user_tab_columns
                WHERE table_name = :table_name
                ORDER BY column_id
            """, {'table_name': table_name})
            
            columns = cursor.fetchall()
            print(f"   📊 عدد الأعمدة: {len(columns)}")
            
            for i, (col_name, data_type, data_length, nullable) in enumerate(columns[:5]):
                null_info = "NULL" if nullable == 'Y' else "NOT NULL"
                print(f"   {i+1}. {col_name}: {data_type}({data_length}) {null_info}")
            
            if len(columns) > 5:
                print(f"   ... و {len(columns) - 5} عمود آخر")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البنية: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    print("🔍 فحص نتائج نسخ الجداول")
    print("=" * 50)
    
    setup_environment()
    
    # فحص المصدر والهدف
    source_count = check_source_tables()
    target_count = check_target_tables()
    
    # حساب النسبة
    if source_count > 0:
        percentage = (target_count / source_count) * 100
        print(f"\n📊 نسبة النسخ: {percentage:.1f}%")
        
        if target_count > 0:
            print("✅ تم نسخ بعض الجداول بنجاح")
            
            # اختبار البنية
            test_table_structure()
            
            print("\n🎉 النسخ يعمل بشكل صحيح!")
        else:
            print("❌ لم يتم نسخ أي جدول")
    else:
        print("❌ لا يمكن الوصول للمصدر")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
