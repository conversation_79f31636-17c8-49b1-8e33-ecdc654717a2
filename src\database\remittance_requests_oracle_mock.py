# -*- coding: utf-8 -*-
"""
إدارة طلبات الحوالات مع قاعدة بيانات Oracle - نسخة محاكاة
Remittance Requests Oracle Manager - Mock Version
"""

from datetime import datetime
import uuid
import sqlite3
from pathlib import Path


class RemittanceRequestsOracleMock:
    """إدارة طلبات الحوالات مع Oracle - نسخة محاكاة"""
    
    def __init__(self):
        self.table_name = "REMITTANCE_REQUESTS"
        self.sequence_name = "SEQ_REMITTANCE_REQUESTS"
        
        # استخدام SQLite كمحاكاة
        self.db_path = Path("data/oracle_mock.db")
        self.db_path.parent.mkdir(exist_ok=True)
        self.connection = None
        self.cursor = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات المحاكاة"""
        try:
            self.connection = sqlite3.connect(str(self.db_path))
            self.cursor = self.connection.cursor()
            return True
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
    
    def create_table_if_not_exists(self):
        """إنشاء جدول طلبات الحوالات إذا لم يكن موجوداً"""
        try:
            if not self.connect():
                return False
            
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS REMITTANCE_REQUESTS (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                REQUEST_NUMBER TEXT UNIQUE NOT NULL,
                REQUEST_DATE DATE NOT NULL,
                BRANCH TEXT,
                EXCHANGER TEXT,
                AMOUNT REAL NOT NULL,
                CURRENCY TEXT NOT NULL,
                EXCHANGE_RATE REAL DEFAULT 1.0,
                PRIORITY TEXT DEFAULT 'عادي',
                PURPOSE TEXT NOT NULL,
                
                -- معلومات المرسل
                SENDER_NAME TEXT NOT NULL,
                SENDER_ID TEXT,
                SENDER_NATIONALITY TEXT,
                SENDER_ID_TYPE TEXT,
                SENDER_PHONE TEXT,
                SENDER_LANDLINE TEXT,
                SENDER_EMAIL TEXT,
                SENDER_POBOX TEXT,
                SENDER_ADDRESS TEXT,
                
                -- معلومات المستقبل
                RECEIVER_NAME TEXT NOT NULL,
                RECEIVER_ID TEXT,
                RECEIVER_PHONE TEXT,
                RECEIVER_ACCOUNT TEXT,
                RECEIVER_BANK TEXT,
                RECEIVER_BRANCH TEXT,
                RECEIVER_SWIFT TEXT,
                RECEIVER_COUNTRY TEXT,
                RECEIVER_CITY TEXT,
                RECEIVER_BANK_COUNTRY TEXT,
                RECEIVER_ADDRESS TEXT,
                
                -- ملاحظات وخيارات
                NOTES TEXT,
                SMS_NOTIFICATION INTEGER DEFAULT 1,
                EMAIL_NOTIFICATION INTEGER DEFAULT 0,
                AUTO_CREATE_REMITTANCE INTEGER DEFAULT 1,
                PRINT_RECEIPT INTEGER DEFAULT 0,
                
                -- معلومات النظام
                STATUS TEXT DEFAULT 'معلق',
                CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UPDATED_AT TIMESTAMP
            )
            """
            
            self.cursor.execute(create_table_sql)
            self.connection.commit()
            
            print(f"✅ تم إنشاء جدول {self.table_name} (محاكاة)")
            return True
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجدول: {e}")
            return False
        finally:
            self.disconnect()
    
    def generate_request_number(self):
        """إنشاء رقم طلب تلقائي"""
        return f"REQ{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def create_request(self, data):
        """إنشاء طلب حوالة جديد"""
        try:
            if not self.connect():
                raise Exception("فشل في الاتصال بقاعدة البيانات")
            
            # التأكد من وجود الجدول
            if not self.create_table_if_not_exists():
                raise Exception("فشل في إنشاء الجدول")
            
            # إعداد البيانات
            insert_sql = f"""
            INSERT INTO {self.table_name} (
                REQUEST_NUMBER, REQUEST_DATE, BRANCH, EXCHANGER, AMOUNT, CURRENCY,
                EXCHANGE_RATE, PRIORITY, PURPOSE, SENDER_NAME, SENDER_ID, SENDER_NATIONALITY,
                SENDER_ID_TYPE, SENDER_PHONE, SENDER_LANDLINE, SENDER_EMAIL, SENDER_POBOX,
                SENDER_ADDRESS, RECEIVER_NAME, RECEIVER_ID, RECEIVER_PHONE, RECEIVER_ACCOUNT,
                RECEIVER_BANK, RECEIVER_BRANCH, RECEIVER_SWIFT, RECEIVER_COUNTRY, RECEIVER_CITY,
                RECEIVER_BANK_COUNTRY, RECEIVER_ADDRESS, NOTES, SMS_NOTIFICATION,
                EMAIL_NOTIFICATION, AUTO_CREATE_REMITTANCE, PRINT_RECEIPT, STATUS, CREATED_AT
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )
            """
            
            params = (
                data['request_number'], data['request_date'], data['branch'], data['exchanger'],
                data['amount'], data['currency'], data['exchange_rate'], data['priority'],
                data['purpose'], data['sender_name'], data['sender_id'], data['sender_nationality'],
                data['sender_id_type'], data['sender_phone'], data['sender_landline'],
                data['sender_email'], data['sender_pobox'], data['sender_address'],
                data['receiver_name'], data['receiver_id'], data['receiver_phone'],
                data['receiver_account'], data['receiver_bank'], data['receiver_branch'],
                data['receiver_swift'], data['receiver_country'], data['receiver_city'],
                data['receiver_bank_country'], data['receiver_address'], data['notes'],
                data['sms_notification'], data['email_notification'], data['auto_create_remittance'],
                data['print_receipt'], data['status'], datetime.now().isoformat()
            )
            
            self.cursor.execute(insert_sql, params)
            self.connection.commit()
            
            request_id = self.cursor.lastrowid
            print(f"✅ تم إنشاء طلب الحوالة بنجاح - ID: {request_id} (محاكاة)")
            return request_id
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء طلب الحوالة: {e}")
            raise
        finally:
            self.disconnect()
    
    def get_all_requests(self):
        """الحصول على جميع طلبات الحوالات"""
        try:
            if not self.connect():
                return []
            
            query = f"""
            SELECT ID, REQUEST_NUMBER, SENDER_NAME, RECEIVER_NAME, AMOUNT, CURRENCY,
                   REQUEST_DATE, STATUS
            FROM {self.table_name}
            ORDER BY CREATED_AT DESC
            """
            
            self.cursor.execute(query)
            return self.cursor.fetchall()
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على الطلبات: {e}")
            return []
        finally:
            self.disconnect()
    
    def get_request_by_id(self, request_id):
        """الحصول على طلب حوالة بالمعرف"""
        try:
            if not self.connect():
                return None
            
            query = f"""
            SELECT * FROM {self.table_name}
            WHERE ID = ?
            """
            
            self.cursor.execute(query, (request_id,))
            result = self.cursor.fetchone()
            return result
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على الطلب: {e}")
            return None
        finally:
            self.disconnect()
    
    def update_request(self, request_id, data):
        """تحديث طلب حوالة"""
        try:
            if not self.connect():
                return False
            
            update_sql = f"""
            UPDATE {self.table_name} SET
                REQUEST_DATE = ?, BRANCH = ?, EXCHANGER = ?, AMOUNT = ?, CURRENCY = ?,
                EXCHANGE_RATE = ?, PRIORITY = ?, PURPOSE = ?, SENDER_NAME = ?, SENDER_ID = ?,
                SENDER_NATIONALITY = ?, SENDER_ID_TYPE = ?, SENDER_PHONE = ?, SENDER_LANDLINE = ?,
                SENDER_EMAIL = ?, SENDER_POBOX = ?, SENDER_ADDRESS = ?, RECEIVER_NAME = ?,
                RECEIVER_ID = ?, RECEIVER_PHONE = ?, RECEIVER_ACCOUNT = ?, RECEIVER_BANK = ?,
                RECEIVER_BRANCH = ?, RECEIVER_SWIFT = ?, RECEIVER_COUNTRY = ?, RECEIVER_CITY = ?,
                RECEIVER_BANK_COUNTRY = ?, RECEIVER_ADDRESS = ?, NOTES = ?, SMS_NOTIFICATION = ?,
                EMAIL_NOTIFICATION = ?, AUTO_CREATE_REMITTANCE = ?, PRINT_RECEIPT = ?,
                UPDATED_AT = ?
            WHERE ID = ?
            """
            
            params = (
                data['request_date'], data['branch'], data['exchanger'], data['amount'],
                data['currency'], data['exchange_rate'], data['priority'], data['purpose'],
                data['sender_name'], data['sender_id'], data['sender_nationality'],
                data['sender_id_type'], data['sender_phone'], data['sender_landline'],
                data['sender_email'], data['sender_pobox'], data['sender_address'],
                data['receiver_name'], data['receiver_id'], data['receiver_phone'],
                data['receiver_account'], data['receiver_bank'], data['receiver_branch'],
                data['receiver_swift'], data['receiver_country'], data['receiver_city'],
                data['receiver_bank_country'], data['receiver_address'], data['notes'],
                data['sms_notification'], data['email_notification'], data['auto_create_remittance'],
                data['print_receipt'], datetime.now().isoformat(), request_id
            )
            
            self.cursor.execute(update_sql, params)
            rows_affected = self.cursor.rowcount
            self.connection.commit()
            
            if rows_affected > 0:
                print(f"✅ تم تحديث طلب الحوالة بنجاح - ID: {request_id} (محاكاة)")
                return True
            else:
                print(f"⚠️ لم يتم العثور على طلب الحوالة - ID: {request_id}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحديث طلب الحوالة: {e}")
            raise
        finally:
            self.disconnect()
    
    def delete_request(self, request_id):
        """حذف طلب حوالة"""
        try:
            if not self.connect():
                return False
            
            delete_sql = f"DELETE FROM {self.table_name} WHERE ID = ?"
            
            self.cursor.execute(delete_sql, (request_id,))
            rows_affected = self.cursor.rowcount
            self.connection.commit()
            
            if rows_affected > 0:
                print(f"✅ تم حذف طلب الحوالة بنجاح - ID: {request_id} (محاكاة)")
                return True
            else:
                print(f"⚠️ لم يتم العثور على طلب الحوالة - ID: {request_id}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في حذف طلب الحوالة: {e}")
            raise
        finally:
            self.disconnect()
