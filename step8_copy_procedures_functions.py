#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 8: نسخ الإجراءات المخزنة والدوال
Step 8: Copy procedures and functions
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class ProcedureFunctionCopier:
    """ناسخ الإجراءات والدوال"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'procedures': 0,
            'functions': 0,
            'packages': 0,
            'package_bodies': 0,
            'failed_objects': 0,
            'skipped_objects': 0,
            'failed_list': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def get_objects_info(self):
        """الحصول على معلومات الكائنات"""
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على الإجراءات والدوال والحزم
            cursor.execute("""
                SELECT object_name, object_type, status, created, last_ddl_time
                FROM user_objects
                WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
                AND object_name NOT LIKE 'BIN$%'
                AND status = 'VALID'
                ORDER BY 
                    CASE object_type 
                        WHEN 'PACKAGE' THEN 1
                        WHEN 'FUNCTION' THEN 2
                        WHEN 'PROCEDURE' THEN 3
                        WHEN 'PACKAGE BODY' THEN 4
                    END,
                    object_name
            """)
            
            objects = cursor.fetchall()
            
            print(f"📊 تم العثور على {len(objects)} كائن للنسخ")
            
            # تصنيف الكائنات
            object_types = {}
            for obj in objects:
                obj_type = obj[1]
                if obj_type not in object_types:
                    object_types[obj_type] = 0
                object_types[obj_type] += 1
            
            print("📋 تصنيف الكائنات:")
            type_names = {
                'PROCEDURE': 'إجراءات مخزنة',
                'FUNCTION': 'دوال', 
                'PACKAGE': 'حزم',
                'PACKAGE BODY': 'أجسام الحزم'
            }
            for obj_type, count in object_types.items():
                print(f"   {type_names.get(obj_type, obj_type)}: {count}")
            
            # الحصول على نص كل كائن
            objects_info = []
            for obj_data in objects:
                object_name = obj_data[0]
                object_type = obj_data[1]
                
                try:
                    # الحصول على نص الكائن
                    cursor.execute("""
                        SELECT text
                        FROM user_source
                        WHERE name = :object_name
                        AND type = :object_type
                        ORDER BY line
                    """, {'object_name': object_name, 'object_type': object_type})
                    
                    source_lines = cursor.fetchall()
                    
                    if source_lines:
                        source_text = ''.join([line[0] for line in source_lines if line[0]])
                        
                        object_info = {
                            'name': object_name,
                            'type': object_type,
                            'status': obj_data[2],
                            'created': obj_data[3],
                            'last_ddl_time': obj_data[4],
                            'source_text': source_text
                        }
                        
                        objects_info.append(object_info)
                    else:
                        print(f"   ⚠️ لا يمكن الحصول على نص الكائن: {object_name} ({object_type})")
                        
                except Exception as e:
                    print(f"   ⚠️ خطأ في الحصول على نص الكائن {object_name}: {e}")
                    continue
            
            cursor.close()
            return objects_info
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات الكائنات: {e}")
            return []
    
    def clean_source_text(self, source_text, object_type):
        """تنظيف نص الكائن"""
        try:
            if not source_text:
                return None
            
            # إزالة المسافات الزائدة
            cleaned_text = source_text.strip()
            
            # إزالة التعليقات المتعددة الأسطر
            import re
            cleaned_text = re.sub(r'/\*.*?\*/', '', cleaned_text, flags=re.DOTALL)
            
            # تنظيف الأسطر
            lines = cleaned_text.split('\n')
            cleaned_lines = []
            
            for line in lines:
                # إزالة التعليقات أحادية السطر
                if '--' in line:
                    line = line[:line.index('--')]
                
                line = line.strip()
                if line:
                    cleaned_lines.append(line)
            
            cleaned_text = '\n'.join(cleaned_lines)
            
            # التأكد من وجود CREATE OR REPLACE
            if not cleaned_text.upper().startswith('CREATE'):
                # النص يبدأ مباشرة بالاسم، نحتاج إضافة CREATE OR REPLACE
                if object_type == 'PACKAGE':
                    cleaned_text = f"CREATE OR REPLACE PACKAGE {cleaned_text}"
                elif object_type == 'PACKAGE BODY':
                    cleaned_text = f"CREATE OR REPLACE PACKAGE BODY {cleaned_text}"
                elif object_type == 'PROCEDURE':
                    cleaned_text = f"CREATE OR REPLACE PROCEDURE {cleaned_text}"
                elif object_type == 'FUNCTION':
                    cleaned_text = f"CREATE OR REPLACE FUNCTION {cleaned_text}"
            elif 'CREATE OR REPLACE' not in cleaned_text.upper():
                # يحتوي على CREATE لكن بدون REPLACE
                cleaned_text = cleaned_text.replace('CREATE ', 'CREATE OR REPLACE ', 1)
            
            return cleaned_text if cleaned_text else None
            
        except Exception as e:
            print(f"   ⚠️ خطأ في تنظيف نص الكائن: {e}")
            return source_text
    
    def create_object(self, object_info):
        """إنشاء كائن في الهدف"""
        try:
            object_name = object_info['name']
            object_type = object_info['type']
            source_text = object_info['source_text']
            
            target_cursor = self.target_conn.cursor()
            
            # التحقق من وجود الكائن
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_objects 
                WHERE object_name = :object_name AND object_type = :object_type
            """, {'object_name': object_name, 'object_type': object_type})
            
            if target_cursor.fetchone()[0] > 0:
                print(f"   ⚠️ الكائن {object_name} ({object_type}) موجود بالفعل - سيتم تخطيه")
                target_cursor.close()
                return 'skipped'
            
            # تنظيف نص الكائن
            cleaned_text = self.clean_source_text(source_text, object_type)
            if not cleaned_text:
                print(f"   ❌ نص الكائن {object_name} فارغ بعد التنظيف")
                target_cursor.close()
                return 'failed'
            
            # إنشاء الكائن
            target_cursor.execute(cleaned_text)
            target_cursor.close()
            
            # تحديث الإحصائيات
            if object_type == 'PROCEDURE':
                self.stats['procedures'] += 1
            elif object_type == 'FUNCTION':
                self.stats['functions'] += 1
            elif object_type == 'PACKAGE':
                self.stats['packages'] += 1
            elif object_type == 'PACKAGE BODY':
                self.stats['package_bodies'] += 1
            
            return 'created'
            
        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ خطأ في إنشاء الكائن {object_info['name']}: {error_msg}")
            
            # تسجيل الأخطاء الشائعة
            if 'ORA-00942' in error_msg:
                print(f"      السبب: جدول أو كائن غير موجود")
            elif 'ORA-00904' in error_msg:
                print(f"      السبب: عمود غير موجود")
            elif 'ORA-00955' in error_msg:
                print(f"      السبب: اسم الكائن موجود بالفعل")
            elif 'ORA-06550' in error_msg:
                print(f"      السبب: خطأ في بناء الجملة PL/SQL")
            
            self.stats['failed_objects'] += 1
            self.stats['failed_list'].append(f"{object_name} ({object_type})")
            return 'failed'
    
    def copy_objects(self, batch_size=5):
        """نسخ جميع الكائنات"""
        print("🚀 بدء نسخ الإجراءات والدوال والحزم")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # الحصول على معلومات الكائنات
        objects_info = self.get_objects_info()
        if not objects_info:
            print("❌ لم يتم العثور على كائنات للنسخ")
            return False
        
        print(f"📊 سيتم نسخ {len(objects_info)} كائن")
        
        # نسخ الكائنات على دفعات صغيرة (لأنها معقدة)
        total_created = 0
        total_skipped = 0
        
        for i in range(0, len(objects_info), batch_size):
            batch = objects_info[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(objects_info) + batch_size - 1) // batch_size
            
            print(f"\n📦 الدفعة {batch_num}/{total_batches} ({len(batch)} كائن)")
            print("-" * 40)
            
            for j, object_info in enumerate(batch, 1):
                object_name = object_info['name']
                object_type = object_info['type']
                
                # تحديد نوع الكائن للعرض
                type_names = {
                    'PROCEDURE': 'إجراء مخزن',
                    'FUNCTION': 'دالة', 
                    'PACKAGE': 'حزمة',
                    'PACKAGE BODY': 'جسم حزمة'
                }
                type_display = type_names.get(object_type, object_type)
                
                print(f"[{i + j:4d}/{len(objects_info)}] إنشاء {type_display}: {object_name}")
                
                result = self.create_object(object_info)
                
                if result == 'created':
                    print(f"   ✅ تم إنشاء {type_display}: {object_name}")
                    total_created += 1
                elif result == 'skipped':
                    print(f"   ⚠️ تم تخطي {type_display}: {object_name}")
                    total_skipped += 1
                    self.stats['skipped_objects'] += 1
                # failed تم حسابه في الدالة
            
            # عرض تقدم الدفعة
            print(f"✅ تمت الدفعة {batch_num}: {total_created} نجح، {self.stats['failed_objects']} فشل، {total_skipped} تخطي")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("🎉 تم إكمال نسخ الإجراءات والدوال والحزم!")
        print(f"📋 الإجراءات المخزنة: {self.stats['procedures']}")
        print(f"⚙️ الدوال: {self.stats['functions']}")
        print(f"📦 الحزم: {self.stats['packages']}")
        print(f"🔧 أجسام الحزم: {self.stats['package_bodies']}")
        print(f"❌ الكائنات الفاشلة: {self.stats['failed_objects']}")
        print(f"⚠️ الكائنات المتخطاة: {self.stats['skipped_objects']}")
        print(f"⏱️ المدة: {duration}")
        
        if self.stats['failed_list']:
            print(f"\n❌ الكائنات الفاشلة:")
            for obj in self.stats['failed_list'][:10]:  # أول 10
                print(f"   - {obj}")
            if len(self.stats['failed_list']) > 10:
                print(f"   ... و {len(self.stats['failed_list']) - 10} كائن آخر")
        
        print("=" * 60)
        
        return total_created > 0
    
    def verify_objects(self):
        """التحقق من الكائنات المنسوخة"""
        print("\n🔍 التحقق من الكائنات المنسوخة...")
        
        try:
            # عد الكائنات في الهدف
            target_cursor = self.target_conn.cursor()
            target_cursor.execute("""
                SELECT object_type, COUNT(*) 
                FROM user_objects 
                WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
                AND object_name NOT LIKE 'BIN$%'
                GROUP BY object_type
                ORDER BY object_type
            """)
            target_counts = dict(target_cursor.fetchall())
            
            # عد الكائنات في المصدر
            source_cursor = self.source_conn.cursor()
            source_cursor.execute("""
                SELECT object_type, COUNT(*) 
                FROM user_objects 
                WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
                AND object_name NOT LIKE 'BIN$%'
                AND status = 'VALID'
                GROUP BY object_type
                ORDER BY object_type
            """)
            source_counts = dict(source_cursor.fetchall())
            
            type_names = {
                'PROCEDURE': 'إجراءات مخزنة',
                'FUNCTION': 'دوال', 
                'PACKAGE': 'حزم',
                'PACKAGE BODY': 'أجسام الحزم'
            }
            
            print("📊 مقارنة الكائنات:")
            for obj_type in ['FUNCTION', 'PACKAGE', 'PACKAGE BODY', 'PROCEDURE']:
                source_count = source_counts.get(obj_type, 0)
                target_count = target_counts.get(obj_type, 0)
                percentage = (target_count / source_count * 100) if source_count > 0 else 0
                
                print(f"   {type_names[obj_type]}: {target_count}/{source_count} ({percentage:.1f}%)")
            
            # فحص حالة الكائنات
            target_cursor.execute("""
                SELECT status, COUNT(*)
                FROM user_objects
                WHERE object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'PACKAGE BODY')
                AND object_name NOT LIKE 'BIN$%'
                GROUP BY status
                ORDER BY status
            """)
            
            status_counts = target_cursor.fetchall()
            
            if status_counts:
                print("\n📊 حالة الكائنات:")
                for status, count in status_counts:
                    status_icon = "✅" if status == "VALID" else "⚠️"
                    print(f"   {status_icon} {status}: {count} كائن")
            
            target_cursor.close()
            source_cursor.close()
            
            return sum(target_counts.values()) > 0
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_copy_process(self):
        """تشغيل عملية نسخ الكائنات"""
        try:
            if not self.connect():
                return False
            
            # نسخ الكائنات
            success = self.copy_objects()
            
            if success:
                # التحقق من النتائج
                self.verify_objects()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في عملية النسخ: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = ProcedureFunctionCopier()
    success = copier.run_copy_process()
    
    if success:
        print("\n✅ الخطوة 8 مكتملة - تم نسخ الإجراءات والدوال والحزم")
    else:
        print("\n❌ فشل في الخطوة 8")
    
    return success


if __name__ == "__main__":
    main()
