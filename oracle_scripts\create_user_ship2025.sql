-- سك<PERSON>ي<PERSON><PERSON> إنشاء مستخدم Oracle جديد
-- Create Oracle User Script for ship2025
-- يج<PERSON> تشغيل هذا السكريبت بصلاحيات DBA

-- إن<PERSON>اء المستخدم الجديد
CREATE USER ship2025 IDENTIFIED BY ys123;

-- منح الصلاحيات الأساسية
GRANT CONNECT TO ship2025;
GRANT RESOURCE TO ship2025;
GRANT CREATE SESSION TO ship2025;
GRANT CREATE TABLE TO ship2025;
GRANT CREATE VIEW TO ship2025;
GRANT CREATE SEQUENCE TO ship2025;
GRANT CREATE PROCEDURE TO ship2025;
GRANT CREATE TRIGGER TO ship2025;
GRANT CREATE SYNONYM TO ship2025;

-- من<PERSON> صلاحيات إضافية للتطوير
GRANT CREATE ANY TABLE TO ship2025;
GRANT ALTER ANY TABLE TO ship2025;
GRANT DROP ANY TABLE TO ship2025;
GRANT SELECT ANY TABLE TO ship2025;
GRANT INSERT ANY TABLE TO ship2025;
GRANT UPDATE ANY TABLE TO ship2025;
GRANT DELETE ANY TABLE TO ship2025;

-- منح صلاحيات الفهارس
GRANT CREATE ANY INDEX TO ship2025;
GRANT ALTER ANY INDEX TO ship2025;
GRANT DROP ANY INDEX TO ship2025;

-- منح صلاحيات المتسلسلات
GRANT CREATE ANY SEQUENCE TO ship2025;
GRANT ALTER ANY SEQUENCE TO ship2025;
GRANT DROP ANY SEQUENCE TO ship2025;
GRANT SELECT ANY SEQUENCE TO ship2025;

-- تحديد مساحة التخزين الافتراضية
ALTER USER ship2025 DEFAULT TABLESPACE USERS;
ALTER USER ship2025 TEMPORARY TABLESPACE TEMP;

-- منح حصة تخزين غير محدودة
ALTER USER ship2025 QUOTA UNLIMITED ON USERS;

-- منح صلاحيات للوصول إلى جداول المستخدم ias20241
GRANT SELECT ON ias20241.CUSTOMERS TO ship2025;
GRANT SELECT ON ias20241.REMITTANCES TO ship2025;
GRANT SELECT ON ias20241.BRANCHES TO ship2025;
GRANT SELECT ON ias20241.CURRENCIES TO ship2025;
GRANT SELECT ON ias20241.EXCHANGE_RATES TO ship2025;
GRANT SELECT ON ias20241.TRANSACTIONS TO ship2025;
GRANT SELECT ON ias20241.USERS TO ship2025;
GRANT SELECT ON ias20241.SUPPLIERS TO ship2025;
GRANT SELECT ON ias20241.BANKS TO ship2025;
GRANT SELECT ON ias20241.REPORTS TO ship2025;

-- منح صلاحيات للوصول إلى المتسلسلات
GRANT SELECT ON ias20241.SEQ_CUSTOMERS TO ship2025;
GRANT SELECT ON ias20241.SEQ_REMITTANCES TO ship2025;
GRANT SELECT ON ias20241.SEQ_BRANCHES TO ship2025;
GRANT SELECT ON ias20241.SEQ_CURRENCIES TO ship2025;

-- إنشاء مرادفات للوصول السهل للجداول
CREATE OR REPLACE SYNONYM ship2025.ias_customers FOR ias20241.CUSTOMERS;
CREATE OR REPLACE SYNONYM ship2025.ias_remittances FOR ias20241.REMITTANCES;
CREATE OR REPLACE SYNONYM ship2025.ias_branches FOR ias20241.BRANCHES;
CREATE OR REPLACE SYNONYM ship2025.ias_currencies FOR ias20241.CURRENCIES;
CREATE OR REPLACE SYNONYM ship2025.ias_exchange_rates FOR ias20241.EXCHANGE_RATES;
CREATE OR REPLACE SYNONYM ship2025.ias_transactions FOR ias20241.TRANSACTIONS;
CREATE OR REPLACE SYNONYM ship2025.ias_users FOR ias20241.USERS;
CREATE OR REPLACE SYNONYM ship2025.ias_suppliers FOR ias20241.SUPPLIERS;
CREATE OR REPLACE SYNONYM ship2025.ias_banks FOR ias20241.BANKS;
CREATE OR REPLACE SYNONYM ship2025.ias_reports FOR ias20241.REPORTS;

-- رسالة تأكيد
SELECT 'تم إنشاء المستخدم ship2025 بنجاح!' AS STATUS FROM DUAL;

-- عرض معلومات المستخدم الجديد
SELECT 
    username,
    account_status,
    default_tablespace,
    temporary_tablespace,
    created
FROM dba_users 
WHERE username = 'SHIP2025';

-- عرض الصلاحيات الممنوحة
SELECT 
    grantee,
    privilege,
    admin_option
FROM dba_sys_privs 
WHERE grantee = 'SHIP2025'
ORDER BY privilege;

COMMIT;
