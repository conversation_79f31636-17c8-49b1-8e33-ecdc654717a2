#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 6: نسخ الفهارس
Step 6: Copy indexes
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class IndexCopier:
    """ناسخ الفهارس"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'normal_indexes': 0,
            'unique_indexes': 0,
            'bitmap_indexes': 0,
            'function_indexes': 0,
            'failed_indexes': 0,
            'skipped_indexes': 0,
            'failed_list': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def get_indexes_info(self):
        """الحصول على معلومات الفهارس"""
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على الفهارس (باستثناء فهارس النظام والقيود)
            cursor.execute("""
                SELECT i.index_name, i.table_name, i.index_type, i.uniqueness,
                       i.compression, i.prefix_length, i.tablespace_name,
                       i.logging, i.status, i.degree, i.partitioned
                FROM user_indexes i
                WHERE i.table_name NOT LIKE 'BIN$%'
                AND i.index_name NOT LIKE 'SYS_%'
                AND i.generated = 'N'
                AND i.index_name NOT IN (
                    SELECT c.index_name FROM user_constraints c
                    WHERE c.index_name IS NOT NULL
                    AND c.constraint_type IN ('P', 'U')
                )
                ORDER BY i.table_name, i.index_name
            """)
            
            indexes = cursor.fetchall()
            
            print(f"📊 تم العثور على {len(indexes)} فهرس للنسخ")
            
            # تصنيف الفهارس
            index_types = {}
            for idx in indexes:
                idx_type = idx[2]  # index_type
                uniqueness = idx[3]  # uniqueness
                
                if uniqueness == 'UNIQUE':
                    category = 'UNIQUE'
                elif idx_type == 'BITMAP':
                    category = 'BITMAP'
                elif idx_type == 'FUNCTION-BASED NORMAL':
                    category = 'FUNCTION'
                else:
                    category = 'NORMAL'
                
                if category not in index_types:
                    index_types[category] = 0
                index_types[category] += 1
            
            print("📋 تصنيف الفهارس:")
            type_names = {
                'NORMAL': 'فهارس عادية',
                'UNIQUE': 'فهارس فريدة', 
                'BITMAP': 'فهارس بت ماب',
                'FUNCTION': 'فهارس دوال'
            }
            for idx_type, count in index_types.items():
                print(f"   {type_names.get(idx_type, idx_type)}: {count}")
            
            # الحصول على أعمدة كل فهرس
            indexes_info = []
            for idx_data in indexes:
                index_name = idx_data[0]
                
                # الحصول على أعمدة الفهرس
                cursor.execute("""
                    SELECT column_name, column_position, descend
                    FROM user_ind_columns
                    WHERE index_name = :index_name
                    ORDER BY column_position
                """, {'index_name': index_name})

                columns = cursor.fetchall()

                # الحصول على تعبيرات الفهارس الوظيفية (تجنب LONG)
                expressions = []
                if idx_data[2] == 'FUNCTION-BASED NORMAL':
                    try:
                        cursor.execute("""
                            SELECT COUNT(*)
                            FROM user_ind_expressions
                            WHERE index_name = :index_name
                        """, {'index_name': index_name})

                        expr_count = cursor.fetchone()[0]
                        if expr_count > 0:
                            expressions = ['FUNCTION_BASED_EXPRESSION']  # placeholder
                    except:
                        expressions = []
                
                index_info = {
                    'name': index_name,
                    'table_name': idx_data[1],
                    'index_type': idx_data[2],
                    'uniqueness': idx_data[3],
                    'compression': idx_data[4],
                    'prefix_length': idx_data[5],
                    'tablespace_name': idx_data[6],
                    'logging': idx_data[7],
                    'status': idx_data[8],
                    'degree': idx_data[9],
                    'partitioned': idx_data[10],
                    'columns': columns,
                    'expressions': expressions
                }
                
                indexes_info.append(index_info)
            
            cursor.close()
            return indexes_info
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات الفهارس: {e}")
            return []
    
    def generate_index_ddl(self, index_info):
        """إنشاء DDL للفهرس"""
        try:
            index_name = index_info['name']
            table_name = index_info['table_name']
            index_type = index_info['index_type']
            uniqueness = index_info['uniqueness']
            columns = index_info['columns']
            expressions = index_info['expressions']
            
            if not columns and not expressions:
                return None
            
            # بناء DDL
            ddl = "CREATE "
            
            # إضافة UNIQUE إذا كان الفهرس فريد
            if uniqueness == 'UNIQUE':
                ddl += "UNIQUE "
            
            # إضافة نوع الفهرس
            if index_type == 'BITMAP':
                ddl += "BITMAP "
            
            ddl += f"INDEX {index_name} ON {table_name} ("
            
            # إضافة الأعمدة
            column_parts = []
            for col_name, col_pos, descend in columns:
                if col_name:
                    col_part = col_name
                    if descend == 'DESC':
                        col_part += " DESC"
                    column_parts.append(col_part)

            if not column_parts:
                return None

            ddl += ", ".join(column_parts)
            
            ddl += ")"
            
            # إضافة خصائص إضافية
            compression = index_info.get('compression')
            if compression and compression != 'DISABLED':
                ddl += f" COMPRESS"
                prefix_length = index_info.get('prefix_length')
                if prefix_length and prefix_length > 0:
                    ddl += f" {prefix_length}"
            
            # إضافة tablespace إذا كان مختلف عن الافتراضي
            tablespace = index_info.get('tablespace_name')
            if tablespace and tablespace not in ('USERS', 'SYSTEM', 'SYSAUX'):
                ddl += f" TABLESPACE {tablespace}"
            
            return ddl
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء DDL للفهرس {index_info['name']}: {e}")
            return None
    
    def create_index(self, index_info):
        """إنشاء فهرس في الهدف"""
        try:
            index_name = index_info['name']
            table_name = index_info['table_name']
            index_type = index_info['index_type']
            uniqueness = index_info['uniqueness']
            
            target_cursor = self.target_conn.cursor()
            
            # التحقق من وجود الجدول في الهدف
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_tables WHERE table_name = :table_name
            """, {'table_name': table_name})
            
            if target_cursor.fetchone()[0] == 0:
                print(f"   ⚠️ الجدول {table_name} غير موجود - تخطي الفهرس {index_name}")
                target_cursor.close()
                return 'skipped'
            
            # التحقق من وجود الفهرس
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_indexes WHERE index_name = :index_name
            """, {'index_name': index_name})
            
            if target_cursor.fetchone()[0] > 0:
                print(f"   ⚠️ الفهرس {index_name} موجود بالفعل - سيتم تخطيه")
                target_cursor.close()
                return 'skipped'
            
            # إنشاء DDL
            ddl = self.generate_index_ddl(index_info)
            if not ddl:
                target_cursor.close()
                return 'failed'
            
            # إنشاء الفهرس
            target_cursor.execute(ddl)
            target_cursor.close()
            
            # تحديث الإحصائيات
            if uniqueness == 'UNIQUE':
                self.stats['unique_indexes'] += 1
            elif index_type == 'BITMAP':
                self.stats['bitmap_indexes'] += 1
            elif index_type == 'FUNCTION-BASED NORMAL':
                self.stats['function_indexes'] += 1
            else:
                self.stats['normal_indexes'] += 1
            
            return 'created'
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الفهرس {index_info['name']}: {e}")
            self.stats['failed_indexes'] += 1
            self.stats['failed_list'].append(index_name)
            return 'failed'
    
    def copy_indexes(self, batch_size=20):
        """نسخ جميع الفهارس"""
        print("🚀 بدء نسخ الفهارس")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # الحصول على معلومات الفهارس
        indexes_info = self.get_indexes_info()
        if not indexes_info:
            print("❌ لم يتم العثور على فهارس للنسخ")
            return False
        
        print(f"📊 سيتم نسخ {len(indexes_info)} فهرس")
        
        # نسخ الفهارس على دفعات
        total_created = 0
        total_skipped = 0
        
        for i in range(0, len(indexes_info), batch_size):
            batch = indexes_info[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(indexes_info) + batch_size - 1) // batch_size
            
            print(f"\n📦 الدفعة {batch_num}/{total_batches} ({len(batch)} فهرس)")
            print("-" * 40)
            
            for j, index_info in enumerate(batch, 1):
                index_name = index_info['name']
                table_name = index_info['table_name']
                index_type = index_info['index_type']
                uniqueness = index_info['uniqueness']
                
                # تحديد نوع الفهرس للعرض
                if uniqueness == 'UNIQUE':
                    type_display = "فهرس فريد"
                elif index_type == 'BITMAP':
                    type_display = "فهرس بت ماب"
                elif index_type == 'FUNCTION-BASED NORMAL':
                    type_display = "فهرس وظيفي"
                else:
                    type_display = "فهرس عادي"
                
                print(f"[{i + j:4d}/{len(indexes_info)}] إنشاء {type_display}: {index_name} على {table_name}")
                
                result = self.create_index(index_info)
                
                if result == 'created':
                    print(f"   ✅ تم إنشاء الفهرس {index_name}")
                    total_created += 1
                elif result == 'skipped':
                    print(f"   ⚠️ تم تخطي الفهرس {index_name}")
                    total_skipped += 1
                    self.stats['skipped_indexes'] += 1
                # failed تم حسابه في الدالة
            
            # عرض تقدم الدفعة
            print(f"✅ تمت الدفعة {batch_num}: {total_created} نجح، {self.stats['failed_indexes']} فشل، {total_skipped} تخطي")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("🎉 تم إكمال نسخ الفهارس!")
        print(f"📊 الفهارس العادية: {self.stats['normal_indexes']}")
        print(f"🔒 الفهارس الفريدة: {self.stats['unique_indexes']}")
        print(f"🗂️ فهارس بت ماب: {self.stats['bitmap_indexes']}")
        print(f"⚙️ الفهارس الوظيفية: {self.stats['function_indexes']}")
        print(f"❌ الفهارس الفاشلة: {self.stats['failed_indexes']}")
        print(f"⚠️ الفهارس المتخطاة: {self.stats['skipped_indexes']}")
        print(f"⏱️ المدة: {duration}")
        
        if self.stats['failed_list']:
            print(f"\n❌ الفهارس الفاشلة:")
            for index in self.stats['failed_list'][:10]:  # أول 10
                print(f"   - {index}")
            if len(self.stats['failed_list']) > 10:
                print(f"   ... و {len(self.stats['failed_list']) - 10} فهرس آخر")
        
        print("=" * 60)
        
        return total_created > 0
    
    def run_copy_process(self):
        """تشغيل عملية نسخ الفهارس"""
        try:
            if not self.connect():
                return False
            
            # نسخ الفهارس
            success = self.copy_indexes()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في عملية النسخ: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = IndexCopier()
    success = copier.run_copy_process()
    
    if success:
        print("\n✅ الخطوة 6 مكتملة - تم نسخ الفهارس")
    else:
        print("\n❌ فشل في الخطوة 6")
    
    return success


if __name__ == "__main__":
    main()
