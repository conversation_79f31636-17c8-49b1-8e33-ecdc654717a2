#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير اتصال Oracle المحسن
Enhanced Oracle Connection Manager
"""

import os
import cx_Oracle
import json
from pathlib import Path
from datetime import datetime


class OracleConnectionManager:
    """مدير اتصال Oracle المحسن"""
    
    def __init__(self):
        # تعيين متغير البيئة TNS_ADMIN
        tns_admin_path = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin_path.absolute())
        
        print(f"🔧 تم تعيين TNS_ADMIN: {os.environ['TNS_ADMIN']}")
        
        self.connections = {}
        self.configs = {
            'ias20241': {
                'username': 'ias20241',
                'password': 'ys123',
                'dsn': 'yemensoft'
            },
            'ship2025': {
                'username': 'ship2025',
                'password': 'ys123', 
                'dsn': 'yemensoft'
            },
            'system': {
                'username': 'system',
                'password': 'oracle',
                'dsn': 'yemensoft'
            }
        }
    
    def connect(self, user_type='ias20241', timeout=10):
        """الاتصال بقاعدة البيانات مع timeout"""
        try:
            config = self.configs[user_type]
            
            print(f"🔌 الاتصال بـ {config['username']}@{config['dsn']}...")
            
            # إنشاء connection string
            conn_string = f"{config['username']}/{config['password']}@{config['dsn']}"
            
            # محاولة الاتصال مع timeout
            connection = cx_Oracle.connect(
                conn_string,
                encoding="UTF-8",
                nencoding="UTF-8"
            )
            
            # اختبار الاتصال
            cursor = connection.cursor()
            cursor.execute("SELECT USER, SYSDATE FROM DUAL")
            user, sysdate = cursor.fetchone()
            
            print(f"✅ تم الاتصال بنجاح!")
            print(f"   المستخدم: {user}")
            print(f"   الوقت: {sysdate}")
            
            self.connections[user_type] = {
                'connection': connection,
                'cursor': cursor,
                'user': user,
                'connected_at': datetime.now()
            }
            
            return True
            
        except cx_Oracle.Error as e:
            error, = e.args
            print(f"❌ خطأ Oracle: {error.message}")
            if error.code:
                print(f"   الكود: {error.code}")
            return False
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
            return False
    
    def disconnect(self, user_type=None):
        """قطع الاتصال"""
        if user_type:
            users = [user_type]
        else:
            users = list(self.connections.keys())
        
        for user in users:
            if user in self.connections:
                try:
                    conn_info = self.connections[user]
                    if conn_info['cursor']:
                        conn_info['cursor'].close()
                    if conn_info['connection']:
                        conn_info['connection'].close()
                    del self.connections[user]
                    print(f"🔌 تم قطع الاتصال: {user}")
                except Exception as e:
                    print(f"❌ خطأ في قطع الاتصال {user}: {e}")
    
    def execute_query(self, query, user_type='ias20241', params=None, fetch=True):
        """تنفيذ استعلام"""
        if user_type not in self.connections:
            if not self.connect(user_type):
                raise Exception(f"فشل الاتصال بـ {user_type}")
        
        try:
            cursor = self.connections[user_type]['cursor']
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch and query.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            else:
                self.connections[user_type]['connection'].commit()
                return cursor.rowcount
                
        except Exception as e:
            print(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            if user_type in self.connections:
                self.connections[user_type]['connection'].rollback()
            raise
    
    def get_tables_info(self, user_type='ias20241'):
        """الحصول على معلومات الجداول"""
        query = """
            SELECT table_name, num_rows, blocks, avg_row_len, last_analyzed
            FROM user_tables
            ORDER BY table_name
        """
        
        tables = self.execute_query(query, user_type)
        
        tables_info = {}
        for table in tables:
            table_name = table[0]
            tables_info[table_name] = {
                'name': table_name,
                'num_rows': table[1] or 0,
                'blocks': table[2] or 0,
                'avg_row_len': table[3] or 0,
                'last_analyzed': str(table[4]) if table[4] else None
            }
        
        return tables_info
    
    def get_table_ddl(self, table_name, user_type='ias20241'):
        """الحصول على DDL للجدول"""
        # الحصول على أعمدة الجدول
        columns_query = """
            SELECT column_name, data_type, data_length, data_precision, 
                   data_scale, nullable, column_id
            FROM user_tab_columns
            WHERE table_name = :table_name
            ORDER BY column_id
        """
        
        columns = self.execute_query(columns_query, user_type, {'table_name': table_name})
        
        # بناء DDL
        ddl_lines = [f"CREATE TABLE {table_name} ("]
        
        for i, col in enumerate(columns):
            col_name, data_type, data_length, data_precision, data_scale, nullable, col_id = col
            
            # تنسيق نوع البيانات
            if data_type == 'NUMBER':
                if data_precision and data_scale:
                    col_type = f"NUMBER({data_precision},{data_scale})"
                elif data_precision:
                    col_type = f"NUMBER({data_precision})"
                else:
                    col_type = "NUMBER"
            elif data_type == 'VARCHAR2':
                col_type = f"VARCHAR2({data_length})"
            elif data_type == 'DATE':
                col_type = "DATE"
            elif data_type == 'CLOB':
                col_type = "CLOB"
            else:
                col_type = data_type
            
            # إضافة NOT NULL إذا لزم الأمر
            null_clause = " NOT NULL" if nullable == 'N' else ""
            
            # إضافة فاصلة إذا لم يكن آخر عمود
            comma = "," if i < len(columns) - 1 else ""
            
            ddl_lines.append(f"    {col_name} {col_type}{null_clause}{comma}")
        
        ddl_lines.append(");")
        
        return "\n".join(ddl_lines)
    
    def create_user_ship2025(self):
        """إنشاء المستخدم ship2025"""
        print("👤 إنشاء المستخدم ship2025...")
        
        # الاتصال كـ system
        if not self.connect('system'):
            print("❌ فشل الاتصال كـ system")
            return False
        
        try:
            commands = [
                "CREATE USER ship2025 IDENTIFIED BY ys123",
                "GRANT CONNECT TO ship2025",
                "GRANT RESOURCE TO ship2025", 
                "GRANT CREATE SESSION TO ship2025",
                "GRANT CREATE TABLE TO ship2025",
                "GRANT CREATE VIEW TO ship2025",
                "GRANT CREATE SEQUENCE TO ship2025",
                "ALTER USER ship2025 DEFAULT TABLESPACE USERS",
                "ALTER USER ship2025 QUOTA UNLIMITED ON USERS"
            ]
            
            for command in commands:
                try:
                    self.execute_query(command, 'system', fetch=False)
                    print(f"   ✅ {command}")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        print(f"   ⚠️ المستخدم موجود بالفعل")
                    else:
                        print(f"   ❌ خطأ: {e}")
            
            print("✅ تم إنشاء المستخدم ship2025 بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إنشاء المستخدم: {e}")
            return False
    
    def copy_table_structure(self, table_name, source_user='ias20241', target_user='ship2025'):
        """نسخ بنية الجدول"""
        print(f"📋 نسخ بنية جدول {table_name}...")
        
        try:
            # الحصول على DDL من المصدر
            ddl = self.get_table_ddl(table_name, source_user)
            
            # إنشاء الجدول في الهدف
            self.execute_query(ddl, target_user, fetch=False)
            
            print(f"   ✅ تم إنشاء جدول {table_name}")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في نسخ جدول {table_name}: {e}")
            return False
    
    def copy_table_data(self, table_name, source_user='ias20241', target_user='ship2025'):
        """نسخ بيانات الجدول"""
        print(f"📊 نسخ بيانات جدول {table_name}...")
        
        try:
            # إنشاء database link أو استخدام INSERT مباشر
            copy_query = f"INSERT INTO {table_name} SELECT * FROM {source_user}.{table_name}"
            
            rows_copied = self.execute_query(copy_query, target_user, fetch=False)
            
            print(f"   ✅ تم نسخ {rows_copied} صف من جدول {table_name}")
            return rows_copied
            
        except Exception as e:
            print(f"   ❌ فشل في نسخ بيانات جدول {table_name}: {e}")
            return 0


def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار مدير اتصال Oracle المحسن")
    print("=" * 60)
    
    manager = OracleConnectionManager()
    
    try:
        # اختبار الاتصال بـ ias20241
        print("\n1️⃣ اختبار الاتصال بـ ias20241:")
        if manager.connect('ias20241'):
            # الحصول على معلومات الجداول
            tables_info = manager.get_tables_info('ias20241')
            print(f"📊 عدد الجداول: {len(tables_info)}")
            
            # عرض أول 3 جداول
            for i, (table_name, info) in enumerate(tables_info.items()):
                if i < 3:
                    print(f"   📋 {table_name}: {info['num_rows']} صف")
        
        # اختبار إنشاء المستخدم ship2025
        print("\n2️⃣ إنشاء المستخدم ship2025:")
        manager.create_user_ship2025()
        
        # اختبار الاتصال بـ ship2025
        print("\n3️⃣ اختبار الاتصال بـ ship2025:")
        if manager.connect('ship2025'):
            print("✅ تم الاتصال بـ ship2025 بنجاح")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
    
    finally:
        # قطع جميع الاتصالات
        manager.disconnect()


if __name__ == "__main__":
    main()
