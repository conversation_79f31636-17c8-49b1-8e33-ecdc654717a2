#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الخطوة 7: نسخ المشاهد
Step 7: Copy views
"""

import os
import cx_Oracle
from pathlib import Path
from datetime import datetime


class ViewCopier:
    """ناسخ المشاهد"""
    
    def __init__(self):
        # إعداد البيئة
        tns_admin = Path(__file__).parent / "network" / "admin"
        os.environ['TNS_ADMIN'] = str(tns_admin.absolute())
        
        self.source_conn = None
        self.target_conn = None
        
        # إحصائيات النسخ
        self.stats = {
            'simple_views': 0,
            'complex_views': 0,
            'failed_views': 0,
            'skipped_views': 0,
            'failed_list': []
        }
    
    def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            print("🔌 الاتصال بقواعد البيانات...")
            
            self.source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
            self.target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
            
            print("✅ تم الاتصال بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def get_views_info(self):
        """الحصول على معلومات المشاهد"""
        try:
            cursor = self.source_conn.cursor()
            
            # الحصول على المشاهد
            cursor.execute("""
                SELECT view_name, text_length, type_text_length, oid_text_length,
                       view_type_owner, view_type, superview_name
                FROM user_views
                WHERE view_name NOT LIKE 'BIN$%'
                ORDER BY view_name
            """)
            
            views = cursor.fetchall()
            
            print(f"📊 تم العثور على {len(views)} مشهد للنسخ")
            
            # تصنيف المشاهد
            view_types = {'SIMPLE': 0, 'COMPLEX': 0}
            for view in views:
                text_length = view[1] or 0
                if text_length > 4000:  # مشاهد معقدة
                    view_types['COMPLEX'] += 1
                else:
                    view_types['SIMPLE'] += 1
            
            print("📋 تصنيف المشاهد:")
            print(f"   مشاهد بسيطة: {view_types['SIMPLE']}")
            print(f"   مشاهد معقدة: {view_types['COMPLEX']}")
            
            # الحصول على نص كل مشهد
            views_info = []
            for view_data in views:
                view_name = view_data[0]
                
                try:
                    # الحصول على نص المشهد
                    cursor.execute("""
                        SELECT text
                        FROM user_views
                        WHERE view_name = :view_name
                    """, {'view_name': view_name})
                    
                    result = cursor.fetchone()
                    view_text = result[0] if result and result[0] else None
                    
                    if view_text:
                        view_info = {
                            'name': view_name,
                            'text_length': view_data[1],
                            'type_text_length': view_data[2],
                            'oid_text_length': view_data[3],
                            'view_type_owner': view_data[4],
                            'view_type': view_data[5],
                            'superview_name': view_data[6],
                            'text': view_text
                        }
                        
                        views_info.append(view_info)
                    else:
                        print(f"   ⚠️ لا يمكن الحصول على نص المشهد: {view_name}")
                        
                except Exception as e:
                    print(f"   ⚠️ خطأ في الحصول على نص المشهد {view_name}: {e}")
                    continue
            
            cursor.close()
            return views_info
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات المشاهد: {e}")
            return []
    
    def clean_view_text(self, view_text):
        """تنظيف نص المشهد"""
        try:
            if not view_text:
                return None
            
            # إزالة المسافات الزائدة والأسطر الفارغة
            cleaned_text = view_text.strip()
            
            # إزالة التعليقات المتعددة الأسطر
            import re
            cleaned_text = re.sub(r'/\*.*?\*/', '', cleaned_text, flags=re.DOTALL)
            
            # إزالة التعليقات أحادية السطر
            lines = cleaned_text.split('\n')
            cleaned_lines = []
            for line in lines:
                # إزالة التعليقات التي تبدأ بـ --
                if '--' in line:
                    line = line[:line.index('--')]
                line = line.strip()
                if line:
                    cleaned_lines.append(line)
            
            cleaned_text = '\n'.join(cleaned_lines)
            
            return cleaned_text if cleaned_text else None
            
        except Exception as e:
            print(f"   ⚠️ خطأ في تنظيف نص المشهد: {e}")
            return view_text
    
    def create_view(self, view_info):
        """إنشاء مشهد في الهدف"""
        try:
            view_name = view_info['name']
            view_text = view_info['text']
            
            target_cursor = self.target_conn.cursor()
            
            # التحقق من وجود المشهد
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_views WHERE view_name = :view_name
            """, {'view_name': view_name})
            
            if target_cursor.fetchone()[0] > 0:
                print(f"   ⚠️ المشهد {view_name} موجود بالفعل - سيتم تخطيه")
                target_cursor.close()
                return 'skipped'
            
            # تنظيف نص المشهد
            cleaned_text = self.clean_view_text(view_text)
            if not cleaned_text:
                print(f"   ❌ نص المشهد {view_name} فارغ بعد التنظيف")
                target_cursor.close()
                return 'failed'
            
            # إنشاء DDL للمشهد
            ddl = f"CREATE OR REPLACE VIEW {view_name} AS\n{cleaned_text}"
            
            # إنشاء المشهد
            target_cursor.execute(ddl)
            target_cursor.close()
            
            # تحديد نوع المشهد للإحصائيات
            text_length = view_info.get('text_length', 0) or 0
            if text_length > 4000:
                self.stats['complex_views'] += 1
            else:
                self.stats['simple_views'] += 1
            
            return 'created'
            
        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ خطأ في إنشاء المشهد {view_info['name']}: {error_msg}")
            
            # تسجيل الأخطاء الشائعة
            if 'ORA-00942' in error_msg:
                print(f"      السبب: جدول أو مشهد غير موجود في الاستعلام")
            elif 'ORA-00904' in error_msg:
                print(f"      السبب: عمود غير موجود")
            elif 'ORA-00955' in error_msg:
                print(f"      السبب: اسم المشهد موجود بالفعل")
            
            self.stats['failed_views'] += 1
            self.stats['failed_list'].append(view_name)
            return 'failed'
    
    def copy_views(self, batch_size=10):
        """نسخ جميع المشاهد"""
        print("🚀 بدء نسخ المشاهد")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # الحصول على معلومات المشاهد
        views_info = self.get_views_info()
        if not views_info:
            print("❌ لم يتم العثور على مشاهد للنسخ")
            return False
        
        print(f"📊 سيتم نسخ {len(views_info)} مشهد")
        
        # ترتيب المشاهد حسب التعقيد (البسيطة أولاً)
        views_info.sort(key=lambda x: x.get('text_length', 0) or 0)
        
        # نسخ المشاهد على دفعات
        total_created = 0
        total_skipped = 0
        
        for i in range(0, len(views_info), batch_size):
            batch = views_info[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(views_info) + batch_size - 1) // batch_size
            
            print(f"\n📦 الدفعة {batch_num}/{total_batches} ({len(batch)} مشهد)")
            print("-" * 40)
            
            for j, view_info in enumerate(batch, 1):
                view_name = view_info['name']
                text_length = view_info.get('text_length', 0) or 0
                
                # تحديد نوع المشهد للعرض
                if text_length > 4000:
                    type_display = "مشهد معقد"
                else:
                    type_display = "مشهد بسيط"
                
                print(f"[{i + j:4d}/{len(views_info)}] إنشاء {type_display}: {view_name}")
                
                result = self.create_view(view_info)
                
                if result == 'created':
                    print(f"   ✅ تم إنشاء المشهد {view_name}")
                    total_created += 1
                elif result == 'skipped':
                    print(f"   ⚠️ تم تخطي المشهد {view_name}")
                    total_skipped += 1
                    self.stats['skipped_views'] += 1
                # failed تم حسابه في الدالة
            
            # عرض تقدم الدفعة
            print(f"✅ تمت الدفعة {batch_num}: {total_created} نجح، {self.stats['failed_views']} فشل، {total_skipped} تخطي")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("🎉 تم إكمال نسخ المشاهد!")
        print(f"📊 المشاهد البسيطة: {self.stats['simple_views']}")
        print(f"🔧 المشاهد المعقدة: {self.stats['complex_views']}")
        print(f"❌ المشاهد الفاشلة: {self.stats['failed_views']}")
        print(f"⚠️ المشاهد المتخطاة: {self.stats['skipped_views']}")
        print(f"⏱️ المدة: {duration}")
        
        if self.stats['failed_list']:
            print(f"\n❌ المشاهد الفاشلة:")
            for view in self.stats['failed_list'][:10]:  # أول 10
                print(f"   - {view}")
            if len(self.stats['failed_list']) > 10:
                print(f"   ... و {len(self.stats['failed_list']) - 10} مشهد آخر")
        
        print("=" * 60)
        
        return total_created > 0
    
    def verify_views(self):
        """التحقق من المشاهد المنسوخة"""
        print("\n🔍 التحقق من المشاهد المنسوخة...")
        
        try:
            # عد المشاهد في الهدف
            target_cursor = self.target_conn.cursor()
            target_cursor.execute("""
                SELECT COUNT(*) FROM user_views WHERE view_name NOT LIKE 'BIN$%'
            """)
            target_count = target_cursor.fetchone()[0]
            
            # عد المشاهد في المصدر
            source_cursor = self.source_conn.cursor()
            source_cursor.execute("""
                SELECT COUNT(*) FROM user_views WHERE view_name NOT LIKE 'BIN$%'
            """)
            source_count = source_cursor.fetchone()[0]
            
            print("📊 مقارنة المشاهد:")
            print(f"   المصدر: {source_count}")
            print(f"   الهدف: {target_count}")
            if source_count > 0:
                percentage = (target_count / source_count) * 100
                print(f"   النسبة: {percentage:.1f}%")
            
            # فحص حالة المشاهد
            target_cursor.execute("""
                SELECT status, COUNT(*)
                FROM user_objects
                WHERE object_type = 'VIEW'
                AND object_name NOT LIKE 'BIN$%'
                GROUP BY status
                ORDER BY status
            """)
            
            status_counts = target_cursor.fetchall()
            
            if status_counts:
                print("\n📊 حالة المشاهد:")
                for status, count in status_counts:
                    status_icon = "✅" if status == "VALID" else "⚠️"
                    print(f"   {status_icon} {status}: {count} مشهد")
            
            target_cursor.close()
            source_cursor.close()
            
            return target_count > 0
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return False
    
    def run_copy_process(self):
        """تشغيل عملية نسخ المشاهد"""
        try:
            if not self.connect():
                return False
            
            # نسخ المشاهد
            success = self.copy_views()
            
            if success:
                # التحقق من النتائج
                self.verify_views()
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في عملية النسخ: {e}")
            return False
            
        finally:
            if self.source_conn:
                self.source_conn.close()
            if self.target_conn:
                self.target_conn.close()


def main():
    """الدالة الرئيسية"""
    copier = ViewCopier()
    success = copier.run_copy_process()
    
    if success:
        print("\n✅ الخطوة 7 مكتملة - تم نسخ المشاهد")
    else:
        print("\n❌ فشل في الخطوة 7")
    
    return success


if __name__ == "__main__":
    main()
