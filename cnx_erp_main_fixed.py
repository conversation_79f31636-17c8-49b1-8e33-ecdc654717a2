#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CnX ERP - Main Window Interface (Fixed Version)
واجهة نظام CnX ERP الرئيسية - النسخة المُصححة
"""

import sys
import math
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTreeWidget, QTreeWidgetItem, QFrame,
    QLineEdit, QComboBox, QDateEdit, QToolBar
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import (
    QFont, QPainter, QLinearGradient, QColor, 
    QAction, QPen, QPainterPath
)

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
    print("⚠️  مكتبات دعم العربية غير متوفرة - سيتم العمل بدون دعم العربية")

class ArtisticBackgroundWidget(QWidget):
    """ويدجت الخلفية الفنية مع الخطوط المنحنية المتدرجة"""
    
    def __init__(self):
        super().__init__()
        self.setAutoFillBackground(True)
        
    def paintEvent(self, event):
        """رسم الخلفية الفنية"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية بيضاء مع تدرج خفيف
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(255, 255, 255))
        gradient.setColorAt(1, QColor(248, 250, 252))
        painter.fillRect(self.rect(), gradient)
        
        # رسم الخطوط المنحنية الفنية
        self.draw_artistic_curves(painter)
        
    def draw_artistic_curves(self, painter):
        """رسم الخطوط المنحنية الملونة بأسلوب فني"""
        width = self.width()
        height = self.height()
        
        if width <= 0 or height <= 0:
            return
            
        # الخطوط الحمراء المتدرجة (أعلى يمين)
        self.draw_flowing_curves(painter, 
                               start_color=QColor(229, 62, 62, 40),
                               start_x=width * 0.7, start_y=0,
                               direction="down_left", num_curves=8)
        
        # الخطوط الزرقاء المتدرجة (وسط)
        self.draw_flowing_curves(painter,
                               start_color=QColor(49, 130, 206, 35),
                               start_x=width * 0.1, start_y=height * 0.4,
                               direction="horizontal", num_curves=6)
        
        # الخطوط الخضراء المتدرجة (أسفل يسار)
        self.draw_flowing_curves(painter,
                               start_color=QColor(56, 161, 105, 30),
                               start_x=0, start_y=height * 0.8,
                               direction="up_right", num_curves=5)
                               
    def draw_flowing_curves(self, painter, start_color, start_x, start_y, direction, num_curves):
        """رسم مجموعة من المنحنيات المتدفقة"""
        
        for i in range(num_curves):
            # تدرج اللون
            alpha_ratio = 1 - (i / num_curves)
            current_color = QColor(start_color)
            current_color.setAlpha(int(start_color.alpha() * alpha_ratio))
            
            # سمك الخط متدرج
            pen_width = max(1, 3 - (i // 3))
            painter.setPen(QPen(current_color, pen_width))
            
            # إنشاء المسار المنحني
            path = QPainterPath()
            
            # حساب نقاط المنحنى حسب الاتجاه
            if direction == "down_left":
                x1 = start_x - (i * 12)
                y1 = start_y + (i * 6)
                x2 = x1 - 150 - (i * 8)
                y2 = y1 + 120 + (i * 10)
                
                # نقاط التحكم للمنحنى
                ctrl1_x = x1 - 60 + math.sin(i * 0.3) * 15
                ctrl1_y = y1 + 30 + math.cos(i * 0.2) * 10
                ctrl2_x = x2 + 40 + math.sin(i * 0.4) * 20
                ctrl2_y = y2 - 60 + math.cos(i * 0.3) * 15
                
            elif direction == "horizontal":
                x1 = start_x + (i * 10)
                y1 = start_y + math.sin(i * 0.5) * 20
                x2 = x1 + 250 + (i * 6)
                y2 = y1 + math.sin(i * 0.3) * 30
                
                ctrl1_x = x1 + 80 + math.cos(i * 0.4) * 20
                ctrl1_y = y1 - 40 + math.sin(i * 0.6) * 15
                ctrl2_x = x2 - 80 + math.cos(i * 0.3) * 25
                ctrl2_y = y2 + 20 + math.sin(i * 0.4) * 15
                
            else:  # up_right
                x1 = start_x + (i * 15)
                y1 = start_y - (i * 8)
                x2 = x1 + 140 + (i * 10)
                y2 = y1 - 100 - (i * 6)
                
                ctrl1_x = x1 + 50 + math.sin(i * 0.4) * 20
                ctrl1_y = y1 - 25 + math.cos(i * 0.5) * 15
                ctrl2_x = x2 - 40 + math.sin(i * 0.3) * 25
                ctrl2_y = y2 + 50 + math.cos(i * 0.4) * 10
            
            # رسم المنحنى
            path.moveTo(x1, y1)
            path.cubicTo(ctrl1_x, ctrl1_y, ctrl2_x, ctrl2_y, x2, y2)
            painter.drawPath(path)

class CnXERPMainWindow(QMainWindow):
    """النافذة الرئيسية لنظام CnX ERP"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_styles()
        
    def init_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("CnX ERP - شركة القدس للتجارة والتوريدات المحدودة - الإدارة المالية 1.7/2024")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1024, 768)
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)
        
        # إنشاء المكونات الرئيسية
        self.create_toolbar()
        
        # الشريط الجانبي الأيمن
        right_sidebar = self.create_right_sidebar()
        
        # المنطقة المركزية مع الخلفية الفنية
        central_area = self.create_central_area()
        
        # الشريط الجانبي الأيسر
        left_sidebar = self.create_left_sidebar()
        
        # ترتيب المكونات (RTL)
        main_layout.addWidget(right_sidebar)
        main_layout.addWidget(central_area, 1)
        main_layout.addWidget(left_sidebar)
        
        # شريط الحالة
        self.statusBar().showMessage("مرحباً بك في نظام CnX ERP - جاهز للعمل")
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات العلوي"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)
        toolbar.setFloatable(False)
        
        # إضافة الأدوات
        tools = [
            ("ℹ️", "معلومات"),
            ("🔊", "الصوت"),
            ("🔔", "التنبيهات"),
            ("⚙️", "الإعدادات"),
            ("🖨️", "طباعة"),
            ("💾", "حفظ"),
            ("⭐", "المفضلة"),
            ("🔧", "الأدوات"),
            ("📧", "البريد الإلكتروني")
        ]
        
        for icon, tooltip in tools:
            action = QAction(f"{icon} {tooltip}", self)
            action.setToolTip(tooltip)
            toolbar.addAction(action)
            
    def create_left_sidebar(self):
        """إنشاء الشريط الجانبي الأيسر"""
        sidebar = QFrame()
        sidebar.setFixedWidth(240)
        sidebar.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # عنوان القسم
        title = QLabel(self.format_arabic_text("📋 القوائم الرئيسية"))
        title.setFont(QFont("Arial", 13, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                padding: 10px;
                border-radius: 6px;
                color: #1976D2;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title)
        
        # شجرة القوائم
        self.menu_tree = QTreeWidget()
        self.menu_tree.setHeaderHidden(True)
        self.menu_tree.setRootIsDecorated(True)
        self.menu_tree.setAlternatingRowColors(True)
        
        # إضافة القوائم مع أيقونات
        menu_items = [
            ("📊", "التقرير الإحصائي"),
            ("🏢", "مركز التكلفة"), 
            ("📋", "أوامر الشراء"),
            ("📦", "بيانات الأصناف"),
            ("📈", "بيانات وحسابات"),
            ("💰", "سجل الأرصدة"),
            ("📋", "قائمة الجرد/العمل"),
            ("📊", "تقرير الأرصدة الحالية"),
            ("📈", "تقرير حركة المخزون"),
            ("📋", "تقارير الحركات المالية")
        ]
        
        for icon, text in menu_items:
            item = QTreeWidgetItem([self.format_arabic_text(f"{icon} {text}")])
            item.setFont(0, QFont("Arial", 10))
            self.menu_tree.addTopLevelItem(item)
            
        layout.addWidget(self.menu_tree)
        return sidebar

    def create_central_area(self):
        """إنشاء المنطقة المركزية مع الخلفية الفنية"""
        # استخدام الويدجت الفني كخلفية
        central_area = ArtisticBackgroundWidget()

        layout = QVBoxLayout(central_area)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(40, 40, 40, 40)

        # شعار CnX ERP مع تأثيرات
        logo_label = QLabel("CnX ERP")
        logo_font = QFont("Arial", 52, QFont.Bold)
        logo_label.setFont(logo_font)
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E53E3E, stop:0.3 #3182CE, stop:0.7 #38A169, stop:1 #E53E3E);
                background: transparent;
                margin: 25px;
            }
        """)
        layout.addWidget(logo_label)

        # النص التوضيحي الإنجليزي
        subtitle = QLabel("Enterprise Resource Planning Solutions")
        subtitle.setFont(QFont("Arial", 16, QFont.Normal))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: #555555;
                background: transparent;
                margin: 15px;
                font-style: italic;
            }
        """)
        layout.addWidget(subtitle)

        # النص العربي
        arabic_text = QLabel(self.format_arabic_text("حلول تخطيط موارد المؤسسات المتكاملة"))
        arabic_text.setFont(QFont("Arial", 14, QFont.Normal))
        arabic_text.setAlignment(Qt.AlignCenter)
        arabic_text.setStyleSheet("""
            QLabel {
                color: #777777;
                background: transparent;
                margin: 10px;
            }
        """)
        layout.addWidget(arabic_text)

        # مساحة مرنة
        layout.addStretch()

        return central_area

    def create_right_sidebar(self):
        """إنشاء الشريط الجانبي الأيمن"""
        sidebar = QFrame()
        sidebar.setFixedWidth(220)
        sidebar.setFrameStyle(QFrame.StyledPanel)

        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(8, 8, 8, 8)

        # عنوان قسم التحكم
        control_title = QLabel(self.format_arabic_text("🔧 لوحة التحكم"))
        control_title.setFont(QFont("Arial", 13, QFont.Bold))
        control_title.setAlignment(Qt.AlignCenter)
        control_title.setStyleSheet("""
            QLabel {
                background-color: #FFF3E0;
                padding: 10px;
                border-radius: 6px;
                color: #F57C00;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(control_title)

        # عناصر التحكم
        controls = [
            ("🔍 البحث:", QLineEdit()),
            ("📅 التاريخ:", QDateEdit(QDate.currentDate())),
            ("📂 النوع:", QComboBox()),
            ("📊 الحالة:", QComboBox()),
        ]

        for label_text, widget in controls:
            label = QLabel(self.format_arabic_text(label_text))
            label.setFont(QFont("Arial", 10, QFont.Bold))
            label.setStyleSheet("color: #444444; margin-top: 8px;")
            layout.addWidget(label)

            if isinstance(widget, QComboBox):
                if "النوع" in label_text:
                    widget.addItems(["الكل", "مبيعات", "مشتريات", "مخزون", "حسابات"])
                elif "الحالة" in label_text:
                    widget.addItems(["الكل", "نشط", "معلق", "مكتمل", "ملغي"])

            widget.setStyleSheet("""
                QLineEdit, QComboBox, QDateEdit {
                    padding: 8px;
                    border: 2px solid #E0E0E0;
                    border-radius: 6px;
                    background-color: white;
                    margin-bottom: 5px;
                }
                QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                    border-color: #2196F3;
                }
            """)
            layout.addWidget(widget)

        # مساحة مرنة
        layout.addStretch()

        # أزرار العمليات
        buttons_data = [
            ("🔍", "بحث", "#4CAF50"),
            ("🔽", "تصفية", "#2196F3"),
            ("📤", "تصدير", "#FF9800"),
            ("🖨️", "طباعة", "#9C27B0")
        ]

        for icon, text, color in buttons_data:
            btn = QPushButton(self.format_arabic_text(f"{icon} {text}"))
            btn.setFont(QFont("Arial", 10, QFont.Bold))
            btn.setMinimumHeight(35)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    margin: 3px;
                }}
                QPushButton:hover {{
                    background-color: {color}DD;
                }}
                QPushButton:pressed {{
                    background-color: {color}BB;
                }}
            """)
            layout.addWidget(btn)

        return sidebar

    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        if not ARABIC_SUPPORT:
            return text
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text

    def setup_styles(self):
        """إعداد الأنماط والألوان"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #FAFAFA;
                font-family: Arial;
                font-size: 10px;
            }

            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
            }

            QTreeWidget {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 6px;
                font-size: 11px;
                padding: 5px;
                outline: none;
            }

            QTreeWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid #F5F5F5;
                border-radius: 4px;
                margin: 1px;
            }

            QTreeWidget::item:hover {
                background-color: #E3F2FD;
                color: #1976D2;
            }

            QTreeWidget::item:selected {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
            }

            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                border: none;
                spacing: 8px;
                padding: 8px;
                font-size: 9px;
            }

            QToolBar QToolButton {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 6px;
                padding: 6px;
                margin: 2px;
            }

            QToolBar QToolButton:hover {
                background-color: rgba(33, 150, 243, 0.1);
                border-color: #2196F3;
            }

            QStatusBar {
                background-color: #F5F5F5;
                border-top: 1px solid #E0E0E0;
                color: #666666;
                font-weight: bold;
            }
        """)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)

    # إعداد الخط والاتجاه للعربية
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين خط افتراضي يدعم العربية
    font = QFont("Arial", 10)
    app.setFont(font)

    # إنشاء النافذة الرئيسية
    window = CnXERPMainWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
