-- سكريب<PERSON> إنشاء الجداول في المستخدم ship2025
-- Create Tables Script for ship2025 User
-- تم إنشاؤه في: 2025-07-12 13:50:35

-- الات<PERSON>ال بالمستخدم الجديد
CONNECT ship2025/ys123@yemensoft;

-- تفعيل عرض النتائج
SET SERVEROUTPUT ON;

BEGIN
    DBMS_OUTPUT.PUT_LINE('🚀 بدء إنشاء الجداول...');
END;
/

-- إنشاء جدول BRANCHES
CREATE TABLE BRANCHES (
    BRANCHE_ID NUMBER NOT NULL,
    NAME VARCHAR2(100) NOT NULL,
    STATUS VARCHAR2(10) NOT NULL
);

-- إضا<PERSON>ة المفتاح الأساسي
ALTER TABLE BRANCHES ADD CONSTRAINT PK_BRANCHES PRIMARY KEY (BRANCHE_ID);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول CURRENCIES
CREATE TABLE CURRENCIES (
    CURRENCIE_ID NUMBER NOT NULL,
    NAME VARCHAR2(100) NOT NULL,
    STATUS VARCHAR2(10) NOT NULL
);

-- إضافة المفتاح الأساسي
ALTER TABLE CURRENCIES ADD CONSTRAINT PK_CURRENCIES PRIMARY KEY (CURRENCIE_ID);

-- إنشاء جدول USERS
CREATE TABLE USERS (
    USER_ID NUMBER NOT NULL,
    NAME VARCHAR2(100) NOT NULL,
    STATUS VARCHAR2(10) NOT NULL
);

-- إضافة المفتاح الأساسي
ALTER TABLE USERS ADD CONSTRAINT PK_USERS PRIMARY KEY (USER_ID);

-- إنشاء جدول CUSTOMERS
CREATE TABLE CUSTOMERS (
    CUSTOMER_ID NUMBER NOT NULL,
    CUSTOMER_NAME VARCHAR2(100) NOT NULL,
    CUSTOMER_NAME_EN VARCHAR2(100),
    ID_NUMBER VARCHAR2(20),
    PHONE VARCHAR2(20),
    EMAIL VARCHAR2(100),
    ADDRESS VARCHAR2(200),
    NATIONALITY VARCHAR2(50),
    CREATED_DATE DATE NOT NULL,
    STATUS VARCHAR2(10) NOT NULL
);

-- إضافة المفتاح الأساسي
ALTER TABLE CUSTOMERS ADD CONSTRAINT PK_CUSTOMERS PRIMARY KEY (CUSTOMER_ID);

-- إنشاء جدول SUPPLIERS
CREATE TABLE SUPPLIERS (
    SUPPLIER_ID NUMBER NOT NULL,
    NAME VARCHAR2(100) NOT NULL,
    STATUS VARCHAR2(10) NOT NULL
);

-- إضافة المفتاح الأساسي
ALTER TABLE SUPPLIERS ADD CONSTRAINT PK_SUPPLIERS PRIMARY KEY (SUPPLIER_ID);

-- إنشاء جدول BANKS
CREATE TABLE BANKS (
    BANK_ID NUMBER NOT NULL,
    NAME VARCHAR2(100) NOT NULL,
    STATUS VARCHAR2(10) NOT NULL
);

-- إضافة المفتاح الأساسي
ALTER TABLE BANKS ADD CONSTRAINT PK_BANKS PRIMARY KEY (BANK_ID);

-- إنشاء جدول REMITTANCES
CREATE TABLE REMITTANCES (
    REMITTANCE_ID NUMBER NOT NULL,
    REMITTANCE_NUMBER VARCHAR2(20) NOT NULL,
    CUSTOMER_ID NUMBER NOT NULL,
    SENDER_NAME VARCHAR2(100) NOT NULL,
    RECEIVER_NAME VARCHAR2(100) NOT NULL,
    AMOUNT NUMBER(15,2) NOT NULL,
    CURRENCY_ID NUMBER NOT NULL,
    EXCHANGE_RATE NUMBER(10,4) NOT NULL,
    BRANCH_ID NUMBER NOT NULL,
    USER_ID NUMBER NOT NULL,
    REMITTANCE_DATE DATE NOT NULL,
    STATUS VARCHAR2(20) NOT NULL,
    NOTES CLOB
);

-- إضافة المفتاح الأساسي
ALTER TABLE REMITTANCES ADD CONSTRAINT PK_REMITTANCES PRIMARY KEY (REMITTANCE_ID);

-- إنشاء جدول EXCHANGE_RATES
CREATE TABLE EXCHANGE_RATES (
    EXCHANGE_RATE_ID NUMBER NOT NULL,
    NAME VARCHAR2(100) NOT NULL,
    STATUS VARCHAR2(10) NOT NULL
);

-- إضافة المفتاح الأساسي
ALTER TABLE EXCHANGE_RATES ADD CONSTRAINT PK_EXCHANGE_RATES PRIMARY KEY (EXCHANGE_RATE_ID);

-- إنشاء جدول TRANSACTIONS
CREATE TABLE TRANSACTIONS (
    TRANSACTION_ID NUMBER NOT NULL,
    NAME VARCHAR2(100) NOT NULL,
    STATUS VARCHAR2(10) NOT NULL
);

-- إضافة المفتاح الأساسي
ALTER TABLE TRANSACTIONS ADD CONSTRAINT PK_TRANSACTIONS PRIMARY KEY (TRANSACTION_ID);

-- إنشاء جدول REPORTS
CREATE TABLE REPORTS (
    REPORT_ID NUMBER NOT NULL,
    NAME VARCHAR2(100) NOT NULL,
    STATUS VARCHAR2(10) NOT NULL
);

-- إضافة المفتاح الأساسي
ALTER TABLE REPORTS ADD CONSTRAINT PK_REPORTS PRIMARY KEY (REPORT_ID);

-- إنشاء المتسلسلات
-- Create Sequences

CREATE SEQUENCE SEQ_BRANCHES
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

CREATE SEQUENCE SEQ_CURRENCIES
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

CREATE SEQUENCE SEQ_USERS
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

CREATE SEQUENCE SEQ_CUSTOMERS
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

CREATE SEQUENCE SEQ_SUPPLIERS
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

CREATE SEQUENCE SEQ_BANKS
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

CREATE SEQUENCE SEQ_REMITTANCES
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

CREATE SEQUENCE SEQ_EXCHANGE_RATES
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

CREATE SEQUENCE SEQ_TRANSACTIONS
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

CREATE SEQUENCE SEQ_REPORTS
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

BEGIN
    DBMS_OUTPUT.PUT_LINE('✅ تم إنشاء جميع الجداول والمتسلسلات بنجاح!');
END;
/

COMMIT;