# -*- coding: utf-8 -*-
"""
نظام إدارة قاعدة بيانات Oracle المبسط
Simplified Oracle Database Management System
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QGroupBox, QGridLayout, QLabel,
                               QPushButton, QLineEdit, QSpinBox, QComboBox,
                               QCheckBox, QTextEdit, QProgressBar, QMessageBox,
                               QFileDialog, QTableWidget, QTableWidgetItem,
                               QHeaderView, QAbstractItemView, QFrame,
                               QScrollArea, QSlider, QDoubleSpinBox,
                               QDateTimeEdit, QToolBar, QStatusBar, QSplitter,
                               QTreeWidget, QTreeWidgetItem, QPlainTextEdit)
from PySide6.QtCore import Qt, Q<PERSON><PERSON><PERSON>, <PERSON>, QThread, QDateTime
from PySide6.QtGui import QFont, QIcon, QPixmap

import json
import os
from pathlib import Path
from datetime import datetime, timedelta
import threading
import time

try:
    import cx_Oracle
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False
    print("تحذير: مكتبة cx_Oracle غير متاحة")

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("تحذير: مكتبة psutil غير متاحة")

from ...utils.arabic_support import reshape_arabic_text


class OracleDatabaseThread(QThread):
    """خيط تنفيذ عمليات قاعدة البيانات"""
    progress_updated = Signal(int)
    status_updated = Signal(str)
    result_ready = Signal(object)
    error_occurred = Signal(str)
    
    def __init__(self, operation, connection_params, query=None, options=None):
        super().__init__()
        self.operation = operation
        self.connection_params = connection_params
        self.query = query
        self.options = options or {}
        
    def run(self):
        try:
            if self.operation == "execute_query":
                self.execute_query()
            elif self.operation == "get_database_info":
                self.get_database_info()
            elif self.operation == "test_connection":
                self.test_connection()
                
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def test_connection(self):
        """اختبار الاتصال"""
        try:
            if not ORACLE_AVAILABLE:
                self.error_occurred.emit("مكتبة cx_Oracle غير متاحة")
                return
                
            self.status_updated.emit("اختبار الاتصال...")
            self.progress_updated.emit(25)
            
            conn = cx_Oracle.connect(**self.connection_params)
            cursor = conn.cursor()
            
            self.progress_updated.emit(75)
            cursor.execute("SELECT 1 FROM dual")
            result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            self.progress_updated.emit(100)
            if result:
                self.result_ready.emit("نجح الاتصال")
            else:
                self.error_occurred.emit("فشل الاتصال")
                
        except Exception as e:
            self.error_occurred.emit(f"خطأ في الاتصال: {str(e)}")
    
    def execute_query(self):
        """تنفيذ استعلام SQL"""
        try:
            if not ORACLE_AVAILABLE:
                self.error_occurred.emit("مكتبة cx_Oracle غير متاحة")
                return
                
            self.status_updated.emit("الاتصال بقاعدة البيانات...")
            conn = cx_Oracle.connect(**self.connection_params)
            cursor = conn.cursor()
            
            self.progress_updated.emit(25)
            self.status_updated.emit("تنفيذ الاستعلام...")
            
            # تنفيذ الاستعلام
            cursor.execute(self.query)
            
            self.progress_updated.emit(75)
            
            # الحصول على النتائج
            if cursor.description:
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()
                result = {'columns': columns, 'rows': rows, 'type': 'select'}
            else:
                result = {'message': 'تم تنفيذ الاستعلام بنجاح', 'type': 'dml'}
                conn.commit()
            
            self.progress_updated.emit(100)
            self.result_ready.emit(result)
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            self.error_occurred.emit(f"خطأ في تنفيذ الاستعلام: {str(e)}")
    
    def get_database_info(self):
        """الحصول على معلومات قاعدة البيانات"""
        try:
            if not ORACLE_AVAILABLE:
                self.error_occurred.emit("مكتبة cx_Oracle غير متاحة")
                return
                
            self.status_updated.emit("جمع معلومات قاعدة البيانات...")
            conn = cx_Oracle.connect(**self.connection_params)
            cursor = conn.cursor()
            
            info = {}
            
            # معلومات عامة
            self.progress_updated.emit(20)
            try:
                cursor.execute("SELECT * FROM v$version WHERE ROWNUM = 1")
                info['oracle_version'] = cursor.fetchone()[0]
            except:
                info['oracle_version'] = "غير محدد"
            
            try:
                cursor.execute("SELECT name FROM v$database")
                info['database_name'] = cursor.fetchone()[0]
            except:
                info['database_name'] = "غير محدد"
            
            try:
                cursor.execute("SELECT username FROM user_users")
                info['current_user'] = cursor.fetchone()[0]
            except:
                info['current_user'] = self.connection_params.get('user', 'غير محدد')
            
            # إحصائيات الجداول
            self.progress_updated.emit(40)
            cursor.execute("SELECT COUNT(*) FROM user_tables")
            info['tables_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM user_views")
            info['views_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM user_indexes")
            info['indexes_count'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM user_sequences")
            info['sequences_count'] = cursor.fetchone()[0]
            
            # إحصائيات الكائنات
            self.progress_updated.emit(60)
            cursor.execute("""
                SELECT object_type, COUNT(*) 
                FROM user_objects 
                WHERE status = 'VALID'
                GROUP BY object_type
                ORDER BY object_type
            """)
            info['objects_by_type'] = dict(cursor.fetchall())
            
            # حجم البيانات
            self.progress_updated.emit(80)
            try:
                cursor.execute("""
                    SELECT SUM(bytes)/1024/1024 as size_mb 
                    FROM user_segments
                """)
                result = cursor.fetchone()
                info['data_size_mb'] = result[0] if result[0] else 0
            except:
                info['data_size_mb'] = 0
            
            self.progress_updated.emit(100)
            self.result_ready.emit(info)
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            self.error_occurred.emit(f"خطأ في جمع معلومات قاعدة البيانات: {str(e)}")


class OracleDatabaseManager(QMainWindow):
    """نظام إدارة قاعدة بيانات Oracle المبسط"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("نظام إدارة قاعدة بيانات Oracle - ProShipment")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # متغيرات النافذة
        self.connection_params = {
            'user': 'ship2025',
            'password': 'ys123',
            'dsn': 'yemensoft',
            'encoding': 'UTF-8'
        }
        
        self.db_thread = None
        self.monitoring_timer = QTimer()
        self.query_history = []
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # تحميل الإعدادات وبدء المراقبة
        self.load_settings()
        self.start_monitoring()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        self.create_header_section(layout)
        
        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        
        # تبويب لوحة التحكم
        dashboard_tab = self.create_dashboard_tab()
        self.tabs.addTab(dashboard_tab, "🏠 لوحة التحكم")
        
        # تبويب محرر SQL
        sql_editor_tab = self.create_sql_editor_tab()
        self.tabs.addTab(sql_editor_tab, "📝 محرر SQL")
        
        # تبويب إدارة الكائنات
        objects_tab = self.create_objects_management_tab()
        self.tabs.addTab(objects_tab, "🗂️ إدارة الكائنات")
        
        # تبويب مراقبة الأداء
        performance_tab = self.create_performance_monitoring_tab()
        self.tabs.addTab(performance_tab, "📊 مراقبة الأداء")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ الإعدادات")
        
        layout.addWidget(self.tabs)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1e3a8a, stop:0.5 #3b82f6, stop:1 #60a5fa);
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # أيقونة Oracle
        icon_label = QLabel("🗄️")
        icon_label.setStyleSheet("font-size: 42px; color: white;")
        header_layout.addWidget(icon_label)
        
        # معلومات العنوان
        info_layout = QVBoxLayout()
        
        title_label = QLabel("نظام إدارة قاعدة بيانات Oracle")
        title_label.setFont(QFont("Arial", 22, QFont.Bold))
        title_label.setStyleSheet("color: white; margin-bottom: 5px;")
        
        subtitle_label = QLabel("إدارة شاملة لقاعدة بيانات Oracle مع أدوات احترافية")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: #e0e7ff;")
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # معلومات سريعة عن قاعدة البيانات
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.15);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        
        stats_layout = QGridLayout(stats_frame)
        
        self.db_name_label = QLabel("اسم قاعدة البيانات\nship2025")
        self.db_status_label = QLabel("حالة الاتصال\n🟢 متصل")
        self.db_version_label = QLabel("إصدار Oracle\nOracle 19c")
        self.db_objects_label = QLabel("عدد الكائنات\n0")
        
        for i, label in enumerate([self.db_name_label, self.db_status_label, 
                                 self.db_version_label, self.db_objects_label]):
            label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-weight: bold;
                    text-align: center;
                    font-size: 11px;
                    padding: 5px;
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            stats_layout.addWidget(label, 0, i)
        
        header_layout.addWidget(stats_frame)
        
        layout.addWidget(header_frame)

    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # معلومات قاعدة البيانات
        db_info_group = QGroupBox("معلومات قاعدة البيانات")
        db_info_layout = QGridLayout(db_info_group)

        # معلومات الاتصال
        db_info_layout.addWidget(QLabel("المستخدم:"), 0, 0)
        self.current_user_label = QLabel(self.connection_params['user'])
        self.current_user_label.setStyleSheet("font-weight: bold; color: #1e40af;")
        db_info_layout.addWidget(self.current_user_label, 0, 1)

        db_info_layout.addWidget(QLabel("قاعدة البيانات:"), 0, 2)
        self.database_name_label = QLabel(self.connection_params['dsn'])
        self.database_name_label.setStyleSheet("font-weight: bold; color: #1e40af;")
        db_info_layout.addWidget(self.database_name_label, 0, 3)

        # إحصائيات سريعة
        db_info_layout.addWidget(QLabel("عدد الجداول:"), 1, 0)
        self.tables_count_label = QLabel("0")
        db_info_layout.addWidget(self.tables_count_label, 1, 1)

        db_info_layout.addWidget(QLabel("عدد المشاهد:"), 1, 2)
        self.views_count_label = QLabel("0")
        db_info_layout.addWidget(self.views_count_label, 1, 3)

        db_info_layout.addWidget(QLabel("حجم البيانات:"), 2, 0)
        self.data_size_label = QLabel("0 MB")
        db_info_layout.addWidget(self.data_size_label, 2, 1)

        db_info_layout.addWidget(QLabel("آخر تحديث:"), 2, 2)
        self.last_update_label = QLabel(datetime.now().strftime("%Y-%m-%d %H:%M"))
        db_info_layout.addWidget(self.last_update_label, 2, 3)

        layout.addWidget(db_info_group)

        # أزرار الإجراءات السريعة
        quick_actions_group = QGroupBox("إجراءات سريعة")
        quick_actions_layout = QGridLayout(quick_actions_group)

        # اختبار الاتصال
        test_connection_btn = QPushButton("🔌 اختبار الاتصال")
        test_connection_btn.clicked.connect(self.test_connection)
        quick_actions_layout.addWidget(test_connection_btn, 0, 0)

        # تحديث المعلومات
        refresh_info_btn = QPushButton("🔄 تحديث المعلومات")
        refresh_info_btn.clicked.connect(self.refresh_database_info)
        quick_actions_layout.addWidget(refresh_info_btn, 0, 1)

        # نسخة احتياطية سريعة
        quick_backup_btn = QPushButton("💾 نسخة احتياطية سريعة")
        quick_backup_btn.clicked.connect(self.create_quick_backup)
        quick_actions_layout.addWidget(quick_backup_btn, 0, 2)

        layout.addWidget(quick_actions_group)

        # جدول الكائنات الحديثة
        recent_objects_group = QGroupBox("الكائنات المُحدثة مؤخراً")
        recent_objects_layout = QVBoxLayout(recent_objects_group)

        self.recent_objects_table = QTableWidget()
        self.setup_recent_objects_table()
        recent_objects_layout.addWidget(self.recent_objects_table)

        layout.addWidget(recent_objects_group)

        return tab

    def create_sql_editor_tab(self):
        """إنشاء تبويب محرر SQL"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # شريط أدوات المحرر
        editor_toolbar = QFrame()
        editor_toolbar_layout = QHBoxLayout(editor_toolbar)

        # أزرار التحكم
        execute_btn = QPushButton("▶️ تنفيذ (F5)")
        execute_btn.setShortcut("F5")
        execute_btn.clicked.connect(self.execute_sql_query)
        execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        editor_toolbar_layout.addWidget(execute_btn)

        clear_btn = QPushButton("🗑️ مسح")
        clear_btn.clicked.connect(self.clear_sql_editor)
        editor_toolbar_layout.addWidget(clear_btn)

        save_query_btn = QPushButton("💾 حفظ الاستعلام")
        save_query_btn.clicked.connect(self.save_sql_query)
        editor_toolbar_layout.addWidget(save_query_btn)

        load_query_btn = QPushButton("📁 تحميل استعلام")
        load_query_btn.clicked.connect(self.load_sql_query)
        editor_toolbar_layout.addWidget(load_query_btn)

        editor_toolbar_layout.addStretch()

        # معلومات الاستعلام
        self.query_info_label = QLabel("جاهز")
        self.query_info_label.setStyleSheet("color: #059669; font-weight: bold;")
        editor_toolbar_layout.addWidget(self.query_info_label)

        layout.addWidget(editor_toolbar)

        # المحرر والنتائج
        splitter = QSplitter(Qt.Vertical)

        # محرر SQL
        editor_group = QGroupBox("محرر SQL")
        editor_layout = QVBoxLayout(editor_group)

        self.sql_editor = QPlainTextEdit()
        self.sql_editor.setPlainText("-- اكتب استعلام SQL هنا\nSELECT * FROM user_tables WHERE ROWNUM <= 10;")
        self.sql_editor.setFont(QFont("Consolas", 12))

        editor_layout.addWidget(self.sql_editor)
        splitter.addWidget(editor_group)

        # نتائج الاستعلام
        results_group = QGroupBox("نتائج الاستعلام")
        results_layout = QVBoxLayout(results_group)

        # شريط تقدم التنفيذ
        self.query_progress = QProgressBar()
        self.query_progress.setVisible(False)
        results_layout.addWidget(self.query_progress)

        # جدول النتائج
        self.results_table = QTableWidget()
        self.setup_results_table()
        results_layout.addWidget(self.results_table)

        splitter.addWidget(results_group)

        # تعيين النسب
        splitter.setSizes([300, 400])
        layout.addWidget(splitter)

        return tab

    def create_objects_management_tab(self):
        """إنشاء تبويب إدارة الكائنات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        info_label = QLabel("🗂️ إدارة الكائنات")
        info_label.setFont(QFont("Arial", 16, QFont.Bold))
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #1e40af; padding: 20px;")
        layout.addWidget(info_label)

        desc_label = QLabel("هذا القسم قيد التطوير. سيتم إضافة أدوات إدارة الجداول والمشاهد والفهارس قريباً.")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #6b7280; padding: 10px;")
        layout.addWidget(desc_label)

        layout.addStretch()
        return tab

    def create_performance_monitoring_tab(self):
        """إنشاء تبويب مراقبة الأداء"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # مؤشرات الأداء الرئيسية
        kpi_group = QGroupBox("مؤشرات الأداء الرئيسية")
        kpi_layout = QGridLayout(kpi_group)

        # استخدام المعالج
        kpi_layout.addWidget(QLabel("استخدام المعالج:"), 0, 0)
        self.cpu_usage_label = QLabel("0%")
        self.cpu_progress = QProgressBar()
        kpi_layout.addWidget(self.cpu_usage_label, 0, 1)
        kpi_layout.addWidget(self.cpu_progress, 0, 2)

        # استخدام الذاكرة
        kpi_layout.addWidget(QLabel("استخدام الذاكرة:"), 1, 0)
        self.memory_usage_label = QLabel("0 MB")
        self.memory_progress = QProgressBar()
        kpi_layout.addWidget(self.memory_usage_label, 1, 1)
        kpi_layout.addWidget(self.memory_progress, 1, 2)

        # الجلسات النشطة
        kpi_layout.addWidget(QLabel("الجلسات النشطة:"), 2, 0)
        self.active_sessions_label = QLabel("0")
        kpi_layout.addWidget(self.active_sessions_label, 2, 1)

        # متوسط وقت الاستجابة
        kpi_layout.addWidget(QLabel("متوسط وقت الاستجابة:"), 2, 2)
        self.response_time_label = QLabel("0 ms")
        kpi_layout.addWidget(self.response_time_label, 2, 3)

        layout.addWidget(kpi_group)

        layout.addStretch()
        return tab

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات الاتصال
        connection_group = QGroupBox("إعدادات الاتصال")
        connection_layout = QGridLayout(connection_group)

        connection_layout.addWidget(QLabel("المستخدم:"), 0, 0)
        self.username_input = QLineEdit()
        self.username_input.setText(self.connection_params['user'])
        connection_layout.addWidget(self.username_input, 0, 1)

        connection_layout.addWidget(QLabel("كلمة المرور:"), 1, 0)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText(self.connection_params['password'])
        connection_layout.addWidget(self.password_input, 1, 1)

        connection_layout.addWidget(QLabel("DSN:"), 2, 0)
        self.dsn_input = QLineEdit()
        self.dsn_input.setText(self.connection_params['dsn'])
        connection_layout.addWidget(self.dsn_input, 2, 1)

        # أزرار الاتصال
        connection_buttons_layout = QHBoxLayout()
        test_connection_btn = QPushButton("🔌 اختبار الاتصال")
        test_connection_btn.clicked.connect(self.test_connection)
        connection_buttons_layout.addWidget(test_connection_btn)

        save_connection_btn = QPushButton("💾 حفظ الإعدادات")
        save_connection_btn.clicked.connect(self.save_connection_settings)
        connection_buttons_layout.addWidget(save_connection_btn)

        connection_buttons_layout.addStretch()
        connection_layout.addLayout(connection_buttons_layout, 3, 0, 1, 2)

        layout.addWidget(connection_group)

        # إعدادات الواجهة
        ui_settings_group = QGroupBox("إعدادات الواجهة")
        ui_settings_layout = QGridLayout(ui_settings_group)

        # تحديث تلقائي
        ui_settings_layout.addWidget(QLabel("تحديث تلقائي (ثانية):"), 0, 0)
        self.auto_refresh_spinbox = QSpinBox()
        self.auto_refresh_spinbox.setRange(5, 300)
        self.auto_refresh_spinbox.setValue(30)
        ui_settings_layout.addWidget(self.auto_refresh_spinbox, 0, 1)

        # عدد الصفوف المعروضة
        ui_settings_layout.addWidget(QLabel("عدد الصفوف المعروضة:"), 1, 0)
        self.max_rows_spinbox = QSpinBox()
        self.max_rows_spinbox.setRange(10, 10000)
        self.max_rows_spinbox.setValue(1000)
        ui_settings_layout.addWidget(self.max_rows_spinbox, 1, 1)

        # حفظ تاريخ الاستعلامات
        self.save_query_history_checkbox = QCheckBox("حفظ تاريخ الاستعلامات")
        self.save_query_history_checkbox.setChecked(True)
        ui_settings_layout.addWidget(self.save_query_history_checkbox, 2, 0, 1, 2)

        layout.addWidget(ui_settings_group)

        layout.addStretch()
        return tab

    # دوال إعداد الجداول
    def setup_recent_objects_table(self):
        """إعداد جدول الكائنات الحديثة"""
        headers = ["اسم الكائن", "النوع", "آخر تعديل", "الحالة"]
        self.recent_objects_table.setColumnCount(len(headers))
        self.recent_objects_table.setHorizontalHeaderLabels(headers)
        self.recent_objects_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.recent_objects_table.setAlternatingRowColors(True)

        # تعديل عرض الأعمدة
        header = self.recent_objects_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_results_table(self):
        """إعداد جدول النتائج"""
        self.results_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.results_table.horizontalHeader()
        header.setStretchLastSection(True)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # اختبار الاتصال
        test_connection_action = toolbar.addAction("🔌 اختبار الاتصال")
        test_connection_action.setToolTip("اختبار الاتصال بقاعدة البيانات")
        test_connection_action.triggered.connect(self.test_connection)

        toolbar.addSeparator()

        # تحديث المعلومات
        refresh_action = toolbar.addAction("🔄 تحديث")
        refresh_action.setToolTip("تحديث جميع المعلومات")
        refresh_action.triggered.connect(self.refresh_all_data)

        # تنفيذ SQL
        execute_sql_action = toolbar.addAction("▶️ تنفيذ SQL")
        execute_sql_action.setToolTip("تنفيذ استعلام SQL (F5)")
        execute_sql_action.triggered.connect(self.execute_sql_query)

        toolbar.addSeparator()

        # إعدادات
        settings_action = toolbar.addAction("⚙️ الإعدادات")
        settings_action.setToolTip("إعدادات النظام")
        settings_action.triggered.connect(lambda: self.tabs.setCurrentIndex(4))

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        self.statusbar = self.statusBar()

        # حالة الاتصال
        self.connection_status_label = QLabel("🟡 غير محدد")
        self.connection_status_label.setStyleSheet("color: orange; font-weight: bold;")
        self.statusbar.addWidget(self.connection_status_label)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # معلومات المستخدم
        self.user_info_label = QLabel(f"المستخدم: {self.connection_params['user']}")
        self.statusbar.addPermanentWidget(self.user_info_label)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # آخر تحديث
        self.last_refresh_label = QLabel("آخر تحديث: غير محدد")
        self.statusbar.addPermanentWidget(self.last_refresh_label)

        self.statusbar.addPermanentWidget(QLabel("|"))

        # شريط التقدم العام
        self.general_progress = QProgressBar()
        self.general_progress.setVisible(False)
        self.general_progress.setMaximumWidth(200)
        self.statusbar.addPermanentWidget(self.general_progress)

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديث دوري للمراقبة
        self.monitoring_timer.timeout.connect(self.update_monitoring_data)

        # ربط تغيير التبويبات
        self.tabs.currentChanged.connect(self.on_tab_changed)

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            settings_file = Path("config/oracle_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # تطبيق إعدادات الاتصال
                if 'connection' in settings:
                    conn_settings = settings['connection']
                    self.connection_params.update(conn_settings)

                # تطبيق إعدادات الواجهة
                if 'ui' in settings:
                    ui_settings = settings['ui']
                    if 'auto_refresh_interval' in ui_settings:
                        self.auto_refresh_spinbox.setValue(ui_settings['auto_refresh_interval'])
                    if 'max_rows' in ui_settings:
                        self.max_rows_spinbox.setValue(ui_settings['max_rows'])

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        self.monitoring_timer.start(5000)  # كل 5 ثوانٍ
        self.test_connection()  # اختبار الاتصال عند البداية

    # الوظائف الأساسية
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        if not ORACLE_AVAILABLE:
            self.connection_status_label.setText("🔴 cx_Oracle غير متاح")
            self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")
            QMessageBox.warning(self, "تحذير", "مكتبة cx_Oracle غير متاحة. يرجى تثبيتها أولاً.")
            return

        if self.db_thread and self.db_thread.isRunning():
            return

        self.connection_status_label.setText("🟡 جاري الاختبار...")
        self.connection_status_label.setStyleSheet("color: orange; font-weight: bold;")

        self.general_progress.setVisible(True)
        self.general_progress.setValue(0)

        # إنشاء خيط لاختبار الاتصال
        self.db_thread = OracleDatabaseThread("test_connection", self.connection_params)
        self.db_thread.progress_updated.connect(self.general_progress.setValue)
        self.db_thread.result_ready.connect(self.on_connection_test_success)
        self.db_thread.error_occurred.connect(self.on_connection_test_error)

        self.db_thread.start()

    def on_connection_test_success(self, message):
        """معالج نجاح اختبار الاتصال"""
        self.general_progress.setVisible(False)
        self.connection_status_label.setText("🟢 متصل")
        self.connection_status_label.setStyleSheet("color: green; font-weight: bold;")

        # تحديث معلومات قاعدة البيانات
        self.refresh_database_info()

    def on_connection_test_error(self, error_message):
        """معالج فشل اختبار الاتصال"""
        self.general_progress.setVisible(False)
        self.connection_status_label.setText("🔴 غير متصل")
        self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")
        QMessageBox.critical(self, "فشل الاتصال", f"فشل في الاتصال بقاعدة البيانات:\n{error_message}")

    def refresh_database_info(self):
        """تحديث معلومات قاعدة البيانات"""
        if not ORACLE_AVAILABLE:
            return

        if self.db_thread and self.db_thread.isRunning():
            return

        self.general_progress.setVisible(True)
        self.general_progress.setValue(0)

        # إنشاء خيط لجمع المعلومات
        self.db_thread = OracleDatabaseThread("get_database_info", self.connection_params)
        self.db_thread.progress_updated.connect(self.general_progress.setValue)
        self.db_thread.result_ready.connect(self.on_database_info_ready)
        self.db_thread.error_occurred.connect(self.on_database_error)

        self.db_thread.start()

    def on_database_info_ready(self, info):
        """معالج استلام معلومات قاعدة البيانات"""
        self.general_progress.setVisible(False)

        # تحديث المعلومات في الواجهة
        self.db_version_label.setText(f"إصدار Oracle\n{info.get('oracle_version', 'غير محدد')[:20]}")
        self.db_name_label.setText(f"اسم قاعدة البيانات\n{info.get('database_name', 'غير محدد')}")

        # تحديث الإحصائيات
        self.tables_count_label.setText(str(info.get('tables_count', 0)))
        self.views_count_label.setText(str(info.get('views_count', 0)))
        self.data_size_label.setText(f"{info.get('data_size_mb', 0):.1f} MB")

        # تحديث عدد الكائنات الإجمالي
        total_objects = sum(info.get('objects_by_type', {}).values())
        self.db_objects_label.setText(f"عدد الكائنات\n{total_objects}")

        # تحديث وقت آخر تحديث
        self.last_update_label.setText(datetime.now().strftime("%Y-%m-%d %H:%M"))
        self.last_refresh_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")

    def on_database_error(self, error_message):
        """معالج أخطاء قاعدة البيانات"""
        self.general_progress.setVisible(False)
        self.connection_status_label.setText("🔴 خطأ")
        self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")
        QMessageBox.critical(self, "خطأ في قاعدة البيانات", error_message)

    def execute_sql_query(self):
        """تنفيذ استعلام SQL"""
        query = self.sql_editor.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", "يرجى كتابة استعلام SQL أولاً")
            return

        if not ORACLE_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "مكتبة cx_Oracle غير متاحة")
            return

        if self.db_thread and self.db_thread.isRunning():
            QMessageBox.warning(self, "تحذير", "عملية أخرى قيد التنفيذ")
            return

        # إضافة الاستعلام إلى التاريخ
        if self.save_query_history_checkbox.isChecked():
            self.query_history.append({
                'query': query,
                'timestamp': datetime.now(),
                'user': self.connection_params['user']
            })

        self.query_progress.setVisible(True)
        self.query_progress.setValue(0)
        self.query_info_label.setText("جاري التنفيذ...")
        self.query_info_label.setStyleSheet("color: #f59e0b; font-weight: bold;")

        # إنشاء خيط لتنفيذ الاستعلام
        self.db_thread = OracleDatabaseThread("execute_query", self.connection_params, query)
        self.db_thread.progress_updated.connect(self.query_progress.setValue)
        self.db_thread.result_ready.connect(self.on_query_result_ready)
        self.db_thread.error_occurred.connect(self.on_query_error)

        self.db_thread.start()

    def on_query_result_ready(self, result):
        """معالج نتائج الاستعلام"""
        self.query_progress.setVisible(False)

        if result['type'] == 'select':
            # عرض نتائج SELECT
            columns = result['columns']
            rows = result['rows']

            self.results_table.setColumnCount(len(columns))
            self.results_table.setHorizontalHeaderLabels(columns)
            self.results_table.setRowCount(len(rows))

            for row_idx, row_data in enumerate(rows):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data) if cell_data is not None else "")
                    self.results_table.setItem(row_idx, col_idx, item)

            self.query_info_label.setText(f"تم العثور على {len(rows)} صف")
            self.query_info_label.setStyleSheet("color: #059669; font-weight: bold;")

        else:
            # عرض رسالة نجاح للعمليات الأخرى
            self.results_table.setColumnCount(1)
            self.results_table.setHorizontalHeaderLabels(["النتيجة"])
            self.results_table.setRowCount(1)
            self.results_table.setItem(0, 0, QTableWidgetItem(result['message']))

            self.query_info_label.setText("تم تنفيذ الاستعلام بنجاح")
            self.query_info_label.setStyleSheet("color: #059669; font-weight: bold;")

    def on_query_error(self, error_message):
        """معالج أخطاء الاستعلام"""
        self.query_progress.setVisible(False)
        self.query_info_label.setText("فشل في التنفيذ")
        self.query_info_label.setStyleSheet("color: #ef4444; font-weight: bold;")

        # عرض الخطأ في جدول النتائج
        self.results_table.setColumnCount(1)
        self.results_table.setHorizontalHeaderLabels(["خطأ"])
        self.results_table.setRowCount(1)
        self.results_table.setItem(0, 0, QTableWidgetItem(error_message))

        QMessageBox.critical(self, "خطأ في الاستعلام", error_message)

    def clear_sql_editor(self):
        """مسح محرر SQL"""
        self.sql_editor.clear()
        self.results_table.clear()
        self.results_table.setRowCount(0)
        self.results_table.setColumnCount(0)
        self.query_info_label.setText("جاهز")
        self.query_info_label.setStyleSheet("color: #059669; font-weight: bold;")

    def save_sql_query(self):
        """حفظ الاستعلام"""
        query = self.sql_editor.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", "لا يوجد استعلام لحفظه")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ الاستعلام", "", "SQL Files (*.sql);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(query)
                QMessageBox.information(self, "نجح", "تم حفظ الاستعلام بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حفظ الاستعلام: {str(e)}")

    def load_sql_query(self):
        """تحميل استعلام"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "تحميل استعلام", "", "SQL Files (*.sql);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    query = f.read()
                self.sql_editor.setPlainText(query)
                QMessageBox.information(self, "نجح", "تم تحميل الاستعلام بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تحميل الاستعلام: {str(e)}")

    def update_monitoring_data(self):
        """تحديث بيانات المراقبة"""
        try:
            if PSUTIL_AVAILABLE:
                # استخدام المعالج
                cpu_percent = psutil.cpu_percent()
                self.cpu_usage_label.setText(f"{cpu_percent:.1f}%")
                self.cpu_progress.setValue(int(cpu_percent))

                # استخدام الذاكرة
                memory = psutil.virtual_memory()
                memory_mb = memory.used / (1024 * 1024)
                memory_percent = memory.percent
                self.memory_usage_label.setText(f"{memory_mb:.0f} MB")
                self.memory_progress.setValue(int(memory_percent))
            else:
                # قيم افتراضية
                self.cpu_usage_label.setText("غير متاح")
                self.memory_usage_label.setText("غير متاح")

            # محاكاة بيانات الجلسات والاستجابة
            self.active_sessions_label.setText("1")
            self.response_time_label.setText("25 ms")

        except Exception as e:
            print(f"خطأ في تحديث بيانات المراقبة: {e}")

    def save_connection_settings(self):
        """حفظ إعدادات الاتصال"""
        try:
            # تحديث معاملات الاتصال
            self.connection_params['user'] = self.username_input.text()
            self.connection_params['password'] = self.password_input.text()
            self.connection_params['dsn'] = self.dsn_input.text()

            # حفظ الإعدادات
            settings = {
                'connection': self.connection_params,
                'ui': {
                    'auto_refresh_interval': self.auto_refresh_spinbox.value(),
                    'max_rows': self.max_rows_spinbox.value(),
                    'save_query_history': self.save_query_history_checkbox.isChecked()
                }
            }

            # إنشاء مجلد الإعدادات
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)

            # حفظ الإعدادات
            with open(config_dir / "oracle_settings.json", 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")

            # تحديث تسميات الواجهة
            self.current_user_label.setText(self.connection_params['user'])
            self.database_name_label.setText(self.connection_params['dsn'])
            self.user_info_label.setText(f"المستخدم: {self.connection_params['user']}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الإعدادات: {e}")

    # دوال معالجة الأحداث
    def on_tab_changed(self, index):
        """معالج تغيير التبويب"""
        if index == 3:  # تبويب مراقبة الأداء
            self.update_monitoring_data()

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.refresh_database_info()
        self.update_monitoring_data()

    def create_quick_backup(self):
        """نسخة احتياطية سريعة"""
        QMessageBox.information(self, "قريباً", "النسخ الاحتياطية قيد التطوير")


# دالة لفتح نظام إدارة قاعدة البيانات
def open_oracle_database_manager(parent=None):
    """فتح نظام إدارة قاعدة بيانات Oracle"""
    try:
        manager = OracleDatabaseManager(parent)
        manager.show()
        return manager
    except Exception as e:
        QMessageBox.critical(parent, "خطأ", f"فشل في فتح نظام إدارة قاعدة البيانات: {str(e)}")
        return None


if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # تطبيق الثيم
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f8fafc;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #1e40af;
        }
        QPushButton {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2563eb;
        }
        QPushButton:pressed {
            background-color: #1d4ed8;
        }
        QTableWidget {
            gridline-color: #e2e8f0;
            background-color: white;
            alternate-background-color: #f8fafc;
        }
        QHeaderView::section {
            background-color: #f1f5f9;
            padding: 8px;
            border: 1px solid #e2e8f0;
            font-weight: bold;
        }
        QTabWidget::pane {
            border: 1px solid #e2e8f0;
            background-color: white;
        }
        QTabBar::tab {
            background-color: #f1f5f9;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        QTabBar::tab:selected {
            background-color: #3b82f6;
            color: white;
        }
        QLineEdit, QTextEdit, QPlainTextEdit {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 8px;
            background-color: white;
        }
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #3b82f6;
        }
        QProgressBar {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            text-align: center;
        }
        QProgressBar::chunk {
            background-color: #10b981;
            border-radius: 5px;
        }
    """)

    window = OracleDatabaseManager()
    window.show()

    sys.exit(app.exec())
