#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بسيط للفهارس
Simple index check
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def simple_index_check():
    """فحص بسيط للفهارس"""
    setup_environment()
    
    print("🔍 فحص بسيط للفهارس")
    print("=" * 40)
    
    try:
        # الاتصال بالمصدر
        print("📊 المصدر (ias20241)...")
        source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        source_cursor = source_conn.cursor()
        
        # عد الفهارس في المصدر
        source_cursor.execute("""
            SELECT COUNT(*) 
            FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
        """)
        source_total = source_cursor.fetchone()[0]
        
        # الاتصال بالهدف
        print("📊 الهدف (ship2025)...")
        target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        target_cursor = target_conn.cursor()
        
        # عد الفهارس في الهدف
        target_cursor.execute("""
            SELECT COUNT(*) 
            FROM user_indexes 
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
        """)
        target_total = target_cursor.fetchone()[0]
        
        print(f"\n📋 النتائج:")
        print(f"   المصدر: {source_total} فهرس")
        print(f"   الهدف: {target_total} فهرس")
        
        if source_total > 0:
            percentage = (target_total / source_total) * 100
            print(f"   النسبة: {percentage:.1f}%")
        
        # فحص عينة من الفهارس في الهدف
        print(f"\n📋 عينة من الفهارس في الهدف:")
        target_cursor.execute("""
            SELECT index_name, table_name, uniqueness
            FROM user_indexes
            WHERE table_name NOT LIKE 'BIN$%'
            AND index_name NOT LIKE 'SYS_%'
            AND generated = 'N'
            AND ROWNUM <= 10
            ORDER BY table_name
        """)
        
        sample_indexes = target_cursor.fetchall()
        
        if sample_indexes:
            for i, (idx_name, table_name, uniqueness) in enumerate(sample_indexes, 1):
                unique_mark = "🔒" if uniqueness == "UNIQUE" else "📊"
                print(f"   {i:2d}. {unique_mark} {idx_name} على {table_name}")
        else:
            print("   ❌ لم يتم العثور على فهارس")
        
        # إغلاق الاتصالات
        source_cursor.close()
        source_conn.close()
        target_cursor.close()
        target_conn.close()
        
        print("\n" + "=" * 40)
        
        if target_total > 0:
            print("✅ تم العثور على فهارس في الهدف!")
            return True
        else:
            print("❌ لم يتم العثور على فهارس في الهدف")
            return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    success = simple_index_check()
    
    if success:
        print("🎉 الفحص مكتمل!")
    else:
        print("💥 فشل الفحص!")
    
    return success


if __name__ == "__main__":
    main()
