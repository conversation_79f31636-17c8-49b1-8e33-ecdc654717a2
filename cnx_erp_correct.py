#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CnX ERP - الحل الصحيح مع دعم العربية الكامل
"""

import sys
import math
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTreeWidget, QTreeWidgetItem, QFrame,
    QLineEdit, QComboBox, QDateEdit, QToolBar
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QPainter, QLinearGradient, QColor, QAction, QPen, QPainterPath

# تثبيت مكتبات العربية إذا لم تكن موجودة
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_OK = True
except ImportError:
    ARABIC_OK = False

class ArtisticBackground(QWidget):
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية بيضاء
        painter.fillRect(self.rect(), QColor(255, 255, 255))
        
        w, h = self.width(), self.height()
        if w <= 0 or h <= 0:
            return
            
        # خطوط حمراء
        for i in range(8):
            painter.setPen(QPen(QColor(229, 62, 62, 30-i*3), 2))
            path = QPainterPath()
            x1 = w*0.7 - i*15
            y1 = i*10
            x2 = x1 - 150
            y2 = y1 + 120
            path.moveTo(x1, y1)
            path.cubicTo(x1-50, y1+30, x2+40, y2-40, x2, y2)
            painter.drawPath(path)
            
        # خطوط زرقاء
        for i in range(6):
            painter.setPen(QPen(QColor(49, 130, 206, 25-i*3), 2))
            path = QPainterPath()
            x1 = w*0.1 + i*12
            y1 = h*0.4 + math.sin(i*0.5)*20
            x2 = x1 + 200
            y2 = y1 + math.sin(i*0.3)*30
            path.moveTo(x1, y1)
            path.cubicTo(x1+60, y1-30, x2-60, y2+20, x2, y2)
            painter.drawPath(path)
            
        # خطوط خضراء
        for i in range(5):
            painter.setPen(QPen(QColor(56, 161, 105, 20-i*3), 2))
            path = QPainterPath()
            x1 = i*18
            y1 = h*0.8 - i*8
            x2 = x1 + 140
            y2 = y1 - 100
            path.moveTo(x1, y1)
            path.cubicTo(x1+50, y1-25, x2-40, y2+50, x2, y2)
            painter.drawPath(path)

class CnXERP(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def fix_arabic(self, text):
        """إصلاح النص العربي"""
        if not ARABIC_OK:
            return text
        try:
            reshaped = arabic_reshaper.reshape(text)
            return get_display(reshaped)
        except:
            return text
            
    def setup_ui(self):
        # النافذة في وضع ملء الشاشة
        self.setWindowTitle(self.fix_arabic("CnX ERP - شركة القدس للتجارة والتوريدات المحدودة"))
        self.showMaximized()  # ملء الشاشة
        
        # الويدجت المركزي
        central = QWidget()
        self.setCentralWidget(central)
        
        # التخطيط الأساسي - من اليسار لليمين (عادي)
        main_layout = QHBoxLayout(central)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # إنشاء الأجزاء
        left_panel = self.create_left_panel()    # القوائم
        center_panel = self.create_center_panel()  # الشعار
        right_panel = self.create_right_panel()   # التحكم
        
        # ترتيب صحيح للعربية: يمين - وسط - يسار
        main_layout.addWidget(right_panel)   # التحكم على اليمين
        main_layout.addWidget(center_panel, 1)  # الوسط
        main_layout.addWidget(left_panel)    # القوائم على اليسار
        
        # شريط الأدوات
        self.create_toolbar()
        
        # شريط الحالة
        self.statusBar().showMessage(self.fix_arabic("مرحباً بك في نظام CnX ERP"))
        
        # الأنماط
        self.setStyleSheet("""
            QMainWindow { background-color: #FAFAFA; }
            QFrame { 
                background-color: white; 
                border: 1px solid #E0E0E0; 
                border-radius: 8px; 
                margin: 2px;
            }
            QTreeWidget {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 6px;
                font-size: 12px;
                padding: 8px;
            }
            QTreeWidget::item {
                padding: 10px;
                border-bottom: 1px solid #F0F0F0;
                text-align: right;
            }
            QTreeWidget::item:hover {
                background-color: #E3F2FD;
                color: #1976D2;
            }
            QTreeWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 6px;
                font-weight: bold;
                margin: 2px;
            }
            QPushButton:hover { background-color: #1976D2; }
            QLineEdit, QComboBox, QDateEdit {
                padding: 8px;
                border: 2px solid #E0E0E0;
                border-radius: 6px;
                background-color: white;
                margin: 2px;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                border-color: #2196F3;
            }
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                border: none;
                padding: 8px;
            }
        """)
        
    def create_toolbar(self):
        toolbar = self.addToolBar(self.fix_arabic("الأدوات"))
        tools = ["ℹ️ معلومات", "🔊 صوت", "🔔 تنبيهات", "⚙️ إعدادات", 
                "🖨️ طباعة", "💾 حفظ", "⭐ مفضلة", "🔧 أدوات", "📧 بريد"]
        
        for tool in tools:
            action = QAction(self.fix_arabic(tool), self)
            toolbar.addAction(action)
            
    def create_left_panel(self):
        """القوائم الرئيسية - على اليسار"""
        panel = QFrame()
        panel.setFixedWidth(280)
        layout = QVBoxLayout(panel)
        
        # العنوان
        title = QLabel(self.fix_arabic("📋 القوائم الرئيسية"))
        title.setFont(QFont("Tahoma", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            background-color: #E3F2FD;
            padding: 12px;
            border-radius: 8px;
            color: #1976D2;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الشجرة
        tree = QTreeWidget()
        tree.setHeaderHidden(True)
        
        # القوائم
        menus = [
            "📊 التقرير الإحصائي",
            "🏢 مركز التكلفة",
            "📋 أوامر الشراء", 
            "📦 بيانات الأصناف",
            "📈 بيانات وحسابات",
            "💰 سجل الأرصدة",
            "📋 قائمة الجرد والعمل",
            "📊 تقرير الأرصدة الحالية",
            "📈 تقرير حركة المخزون",
            "📋 تقارير الحركات المالية"
        ]
        
        for menu in menus:
            item = QTreeWidgetItem([self.fix_arabic(menu)])
            item.setFont(0, QFont("Tahoma", 11))
            tree.addTopLevelItem(item)
            
        layout.addWidget(tree)
        return panel
        
    def create_center_panel(self):
        """المنطقة المركزية مع الشعار"""
        panel = ArtisticBackground()
        layout = QVBoxLayout(panel)
        layout.setAlignment(Qt.AlignCenter)
        
        # الشعار
        logo = QLabel("CnX ERP")
        logo.setFont(QFont("Arial", 56, QFont.Bold))
        logo.setAlignment(Qt.AlignCenter)
        logo.setStyleSheet("""
            color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #E53E3E, stop:0.3 #3182CE, stop:0.7 #38A169, stop:1 #E53E3E);
            background: transparent;
            margin: 30px;
        """)
        layout.addWidget(logo)
        
        # النص الإنجليزي
        subtitle = QLabel("Enterprise Resource Planning Solutions")
        subtitle.setFont(QFont("Arial", 18))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("color: #555555; background: transparent; margin: 20px;")
        layout.addWidget(subtitle)
        
        # النص العربي
        arabic = QLabel(self.fix_arabic("حلول تخطيط موارد المؤسسات المتكاملة"))
        arabic.setFont(QFont("Tahoma", 16))
        arabic.setAlignment(Qt.AlignCenter)
        arabic.setStyleSheet("color: #777777; background: transparent; margin: 15px;")
        layout.addWidget(arabic)
        
        layout.addStretch()
        return panel
        
    def create_right_panel(self):
        """لوحة التحكم - على اليمين"""
        panel = QFrame()
        panel.setFixedWidth(250)
        layout = QVBoxLayout(panel)
        
        # العنوان
        title = QLabel(self.fix_arabic("🔧 لوحة التحكم"))
        title.setFont(QFont("Tahoma", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            background-color: #FFF3E0;
            padding: 12px;
            border-radius: 8px;
            color: #F57C00;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # عناصر التحكم
        controls = [
            ("🔍 البحث:", QLineEdit()),
            ("📅 التاريخ:", QDateEdit(QDate.currentDate())),
            ("📂 النوع:", QComboBox()),
            ("📊 الحالة:", QComboBox())
        ]
        
        for label_text, widget in controls:
            label = QLabel(self.fix_arabic(label_text))
            label.setFont(QFont("Tahoma", 11, QFont.Bold))
            layout.addWidget(label)
            
            if isinstance(widget, QComboBox):
                if "النوع" in label_text:
                    items = ["الكل", "مبيعات", "مشتريات", "مخزون", "حسابات"]
                elif "الحالة" in label_text:
                    items = ["الكل", "نشط", "معلق", "مكتمل", "ملغي"]
                else:
                    items = []
                    
                for item in items:
                    widget.addItem(self.fix_arabic(item))
                    
            elif isinstance(widget, QLineEdit):
                widget.setPlaceholderText(self.fix_arabic("ابحث هنا..."))
                
            layout.addWidget(widget)
            
        layout.addStretch()
        
        # الأزرار
        buttons = [
            ("🔍 بحث", "#4CAF50"),
            ("🔽 تصفية", "#2196F3"),
            ("📤 تصدير", "#FF9800"),
            ("🖨️ طباعة", "#9C27B0")
        ]
        
        for text, color in buttons:
            btn = QPushButton(self.fix_arabic(text))
            btn.setFont(QFont("Tahoma", 11, QFont.Bold))
            btn.setMinimumHeight(40)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: 8px;
                    margin: 4px;
                }}
                QPushButton:hover {{ background-color: {color}DD; }}
            """)
            layout.addWidget(btn)
            
        return panel

def main():
    app = QApplication(sys.argv)
    
    # تعيين خط يدعم العربية
    font = QFont("Tahoma", 11)
    app.setFont(font)
    
    window = CnXERP()
    window.show()
    
    print("✅ تم تشغيل CnX ERP بنجاح!")
    print("📱 النافذة مفتوحة في وضع ملء الشاشة")
    print("🌐 دعم العربية مُفعل")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
