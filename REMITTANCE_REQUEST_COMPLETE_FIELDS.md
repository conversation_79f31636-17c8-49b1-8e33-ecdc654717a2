# نافذة طلب الحوالة - الحقول الكاملة

## نظرة عامة
تم إضافة جميع الحقول المفقودة إلى نافذة طلب الحوالة لتصبح شاملة ومتكاملة مع النظام الأصلي.

## 📋 الحقول المضافة

### 1. البيانات الأساسية المحسنة:
```python
# الحقول الجديدة المضافة:
self.branch = QComboBox()              # الفرع
self.exchanger = QComboBox()           # الصراف
self.exchange_rate = QDoubleSpinBox()  # معدل الصرف
self.priority = QComboBox()            # الأولوية (عادي، عاجل، عاجل جداً)

# الحقول المحسنة:
self.currency = QComboBox()            # عملات إضافية (8 عملات)
self.amount.setSuffix(" ريال")        # وحدة العملة
```

### 2. معلومات المرسل المفصلة:
```python
# الحقول الجديدة:
self.sender_nationality = QComboBox()  # الجنسية (18 جنسية)
self.sender_id_type = QComboBox()      # نوع الهوية
self.sender_landline = QLineEdit()     # الهاتف الثابت
self.sender_pobox = QLineEdit()        # صندوق البريد

# الحقول المحسنة:
self.sender_id.setPlaceholder("رقم الهوية أو الإقامة")
self.sender_phone.setPlaceholder("05xxxxxxxx")
```

### 3. معلومات المستقبل الشاملة:
```python
# الحقول الجديدة:
self.receiver_id = QLineEdit()         # رقم هوية المستقبل
self.receiver_phone = QLineEdit()      # رقم جوال المستقبل
self.receiver_swift = QLineEdit()      # رمز SWIFT
self.receiver_city = QLineEdit()       # المدينة
self.receiver_bank_country = QComboBox() # بلد البنك

# الحقول المحسنة:
self.receiver_country = QComboBox()    # 24 دولة
```

### 4. خيارات الإشعارات والتحويل:
```python
# خيارات جديدة:
self.sms_notification = QCheckBox()           # إشعار SMS
self.email_notification = QCheckBox()         # إشعار بريد إلكتروني
self.auto_create_remittance = QCheckBox()     # إنشاء حوالة تلقائياً
self.print_receipt = QCheckBox()              # طباعة الإيصال
```

## 🗃️ قاعدة البيانات المحدثة

### الجدول الجديد:
```sql
CREATE TABLE remittance_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- البيانات الأساسية
    request_number TEXT UNIQUE NOT NULL,
    request_date TEXT NOT NULL,
    branch TEXT,
    exchanger TEXT,
    amount REAL NOT NULL,
    currency TEXT NOT NULL,
    exchange_rate REAL DEFAULT 1.0,
    priority TEXT DEFAULT 'عادي',
    purpose TEXT NOT NULL,
    
    -- معلومات المرسل
    sender_name TEXT NOT NULL,
    sender_id TEXT,
    sender_nationality TEXT,
    sender_id_type TEXT,
    sender_phone TEXT,
    sender_landline TEXT,
    sender_email TEXT,
    sender_pobox TEXT,
    sender_address TEXT,
    
    -- معلومات المستقبل
    receiver_name TEXT NOT NULL,
    receiver_id TEXT,
    receiver_phone TEXT,
    receiver_account TEXT,
    receiver_bank TEXT,
    receiver_branch TEXT,
    receiver_swift TEXT,
    receiver_country TEXT,
    receiver_city TEXT,
    receiver_bank_country TEXT,
    receiver_address TEXT,
    
    -- الملاحظات والخيارات
    notes TEXT,
    sms_notification INTEGER DEFAULT 1,
    email_notification INTEGER DEFAULT 0,
    auto_create_remittance INTEGER DEFAULT 1,
    print_receipt INTEGER DEFAULT 0,
    
    -- معلومات النظام
    status TEXT DEFAULT 'معلق',
    created_at TEXT NOT NULL,
    updated_at TEXT
);
```

## 📊 مقارنة قبل وبعد

### قبل التحديث:
- ❌ **17 حقل فقط** - بيانات أساسية محدودة
- ❌ **معلومات ناقصة** - لا توجد تفاصيل كافية
- ❌ **لا توجد خيارات** - لا يمكن التحكم في الإشعارات
- ❌ **عملات محدودة** - 4 عملات فقط

### بعد التحديث:
- ✅ **37 حقل شامل** - تغطية كاملة لجميع البيانات
- ✅ **معلومات مفصلة** - تفاصيل شاملة للمرسل والمستقبل
- ✅ **خيارات متقدمة** - تحكم كامل في الإشعارات والخيارات
- ✅ **عملات شاملة** - 8 عملات رئيسية

## 🎯 الحقول بالتفصيل

### البيانات الأساسية (9 حقول):
1. **رقم الطلب** - تلقائي (REQ + التاريخ والوقت)
2. **التاريخ** - تاريخ الطلب مع تقويم منبثق
3. **الفرع** - قائمة الفروع المتاحة
4. **الصراف** - قائمة الصرافين
5. **مبلغ الحوالة** - مع دعم الكسور العشرية
6. **العملة** - 8 عملات رئيسية
7. **معدل الصرف** - 4 خانات عشرية
8. **الأولوية** - عادي/عاجل/عاجل جداً
9. **الغرض من التحويل** - نص حر

### معلومات المرسل (9 حقول):
1. **الاسم الكامل** - مطلوب
2. **رقم الهوية** - هوية أو إقامة
3. **الجنسية** - 18 جنسية
4. **نوع الهوية** - هوية/إقامة/جواز/رخصة
5. **رقم الجوال** - مع تنسيق سعودي
6. **الهاتف الثابت** - اختياري
7. **البريد الإلكتروني** - اختياري
8. **صندوق البريد** - اختياري
9. **العنوان** - العنوان الكامل

### معلومات المستقبل (11 حقل):
1. **الاسم الكامل** - مطلوب
2. **رقم الهوية** - اختياري
3. **رقم الجوال** - اختياري
4. **رقم الحساب** - الحساب البنكي
5. **اسم البنك** - اسم البنك المستقبل
6. **فرع البنك** - فرع البنك
7. **رمز SWIFT** - للتحويلات الدولية
8. **البلد** - 24 دولة
9. **المدينة** - مدينة المستقبل
10. **بلد البنك** - قد يختلف عن بلد المستقبل
11. **العنوان** - العنوان الكامل

### الملاحظات والخيارات (6 حقول):
1. **الملاحظات** - نص متعدد الأسطر
2. **إشعار SMS** - مفعل افتراضياً
3. **إشعار بريد إلكتروني** - معطل افتراضياً
4. **إنشاء حوالة تلقائياً** - مفعل افتراضياً
5. **طباعة الإيصال** - معطل افتراضياً
6. **الحالة** - معلق/مؤكد/مرفوض/مكتمل

## 🚀 المميزات الجديدة

### 1. تحقق شامل من البيانات:
- فحص جميع الحقول المطلوبة
- التحقق من صحة أرقام الهواتف
- التحقق من صحة البريد الإلكتروني
- التحقق من صحة معدل الصرف

### 2. واجهة مستخدم محسنة:
- تجميع منطقي للحقول
- نصوص إرشادية واضحة
- قوائم منسدلة شاملة
- خيارات افتراضية ذكية

### 3. مرونة في الاستخدام:
- حقول اختيارية وإجبارية واضحة
- دعم التحويلات المحلية والدولية
- خيارات متقدمة للإشعارات
- إمكانية الطباعة المباشرة

## 📝 كيفية الاستخدام

### للمستخدم العادي:
1. **املأ البيانات الأساسية** - الفرع والصراف والمبلغ
2. **أدخل معلومات المرسل** - الاسم والهوية والجوال (مطلوب)
3. **أدخل معلومات المستقبل** - الاسم والحساب والبنك (مطلوب)
4. **اختر الخيارات** - الإشعارات والطباعة حسب الحاجة
5. **احفظ الطلب** - سيتم إنشاء رقم طلب تلقائياً

### للمطور:
```python
# الوصول لجميع البيانات
data = window.collect_form_data()

# البيانات تشمل الآن 37 حقل:
print(f"رقم الطلب: {data['request_number']}")
print(f"الفرع: {data['branch']}")
print(f"الصراف: {data['exchanger']}")
print(f"معدل الصرف: {data['exchange_rate']}")
print(f"جنسية المرسل: {data['sender_nationality']}")
print(f"رمز SWIFT: {data['receiver_swift']}")
print(f"إشعار SMS: {data['sms_notification']}")
```

## 🎉 النتيجة النهائية

نافذة طلب الحوالة أصبحت الآن:
- **شاملة** - تغطي جميع احتياجات العمل
- **مرنة** - تدعم التحويلات المحلية والدولية  
- **سهلة الاستخدام** - واجهة واضحة ومنظمة
- **متكاملة** - تتوافق مع النظام الأصلي
- **قابلة للتوسع** - يمكن إضافة حقول جديدة بسهولة

**جميع الحقول المفقودة تم إضافتها بنجاح! 🎊**

---

**تاريخ التحديث**: 2025-07-11  
**الإصدار**: 4.1.0  
**عدد الحقول**: 37 حقل شامل  
**حالة المشروع**: مكتمل ✅
