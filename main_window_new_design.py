#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية مع التصميم الجديد حسب الصورة
Main Window with New Design Based on Image
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QLabel, QPushButton, QFrame,
    QScrollArea, QSplitter, QTextEdit, QTreeWidget, QTreeWidgetItem,
    QSizePolicy, QSpacerItem, QMessageBox
)
from PySide6.QtCore import Qt, QSize, QTimer
from PySide6.QtGui import (
    <PERSON><PERSON><PERSON><PERSON>p, <PERSON><PERSON><PERSON><PERSON>, QLinearGradient, QColor, QFont, QIcon,
    QPalette, QBrush, QPen
)

# استيراد الأنظمة من src/ui
try:
    from src.ui.settings.settings_window import SettingsWindow
    from src.ui.items.items_window import ItemsWindow
    from src.ui.suppliers.suppliers_window import SuppliersWindow
    from src.ui.shipments.shipments_window import ShipmentsWindow
    from src.ui.shipments.live_tracking_window import LiveTrackingWindow
    from src.ui.shipments.shipment_maps_window import ShipmentMapsWindow
    from src.ui.shipments.shipping_routes_window import ShippingRoutesWindow
    from src.ui.remittances.remittances_window import RemittancesWindow
    from src.ui.remittances.remittance_request_window import RemittanceRequestWindow
    from src.ui.remittances.banks_management_window import BanksManagementWindow
    from src.ui.remittances.new_remittance_dialog import NewRemittanceDialog
    from src.ui.remittances.supplier_accounts_management_window import SupplierAccountsManagementWindow
    from src.ui.database.database_settings_window import DatabaseSettingsWindow
    from src.ui.database.oracle_database_manager_simple import OracleDatabaseManager
    from src.utils.arabic_support import setup_arabic_support
    SYSTEMS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: بعض الأنظمة غير متاحة: {e}")
    SYSTEMS_AVAILABLE = False

class GradientWidget(QWidget):
    """ويدجت مع خلفية متدرجة"""
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def paintEvent(self, event):
        painter = QPainter(self)
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor("#1e3a8a"))
        gradient.setColorAt(0.5, QColor("#3b82f6"))
        gradient.setColorAt(1, QColor("#60a5fa"))
        painter.fillRect(self.rect(), gradient)

class CnXMainWindow(QMainWindow):
    """النافذة الرئيسية لنظام CnX ERP"""
    
    def __init__(self):
        super().__init__()
        
        # إعداد النافذة الأساسية
        self.setWindowTitle("CnX ERP - شركة الأنظمة الذكية والمعلوماتية المتقدمة")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # متغيرات النوافذ المنفصلة
        self.settings_window = None
        self.items_window = None
        self.suppliers_window = None
        self.shipments_window = None
        self.live_tracking_window = None
        self.shipment_maps_window = None
        self.shipping_routes_window = None
        self.remittances_window = None
        self.remittance_request_window = None
        self.banks_management_window = None
        self.supplier_accounts_management_window = None
        self.database_settings_window = None
        self.oracle_database_manager = None
        
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء الشريط الجانبي
        sidebar = self.create_sidebar()
        main_layout.addWidget(sidebar)
        
        # إنشاء المنطقة الرئيسية
        main_area = self.create_main_area()
        main_layout.addWidget(main_area, 1)
        
        # إعداد شريط الحالة
        self.setup_status_bar()
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = QFrame()
        sidebar_frame.setFixedWidth(280)
        sidebar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                            stop:0 #2b6cb0, stop:1 #3182ce);
                border: none;
            }
        """)
        
        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # عنوان النظام
        title_label = QLabel("🏠 لوحة التحكم")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
                background-color: rgba(255, 255, 255, 0.1);
                border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            }
        """)
        sidebar_layout.addWidget(title_label)
        
        # إنشاء شجرة الأنظمة
        self.tree = QTreeWidget()
        self.tree.setHeaderHidden(True)
        self.tree.setRootIsDecorated(False)
        self.tree.setIndentation(0)
        
        # تطبيق أنماط CnX ERP على الشجرة
        self.tree.setStyleSheet("""
            QTreeWidget {
                background: transparent;
                border: none;
                color: #f8fafc;
                font-size: 14px;
                font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
                font-weight: 500;
            }
            QTreeWidget::item {
                padding: 15px 20px;
                border: none;
                min-height: 45px;
                color: #f1f5f9;
                background-color: transparent;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
            QTreeWidget::item:hover {
                background-color: rgba(255, 255, 255, 0.15);
                border-right: 4px solid #60a5fa;
                color: #ffffff;
                font-weight: 600;
            }
            QTreeWidget::item:selected {
                background-color: rgba(255, 255, 255, 0.25);
                color: #ffffff;
                border-right: 4px solid #3b82f6;
                font-weight: bold;
            }
        """)
        
        self.setup_tree_items()
        sidebar_layout.addWidget(self.tree)
        
        # ربط الأحداث
        self.tree.itemClicked.connect(self.on_item_clicked)
        
        return sidebar_frame
    
    def setup_tree_items(self):
        """إعداد عناصر الشجرة حسب التصميم الجديد"""
        
        # 🏠 لوحة التحكم
        dashboard_item = QTreeWidgetItem(self.tree, ["🏠 لوحة التحكم"])
        dashboard_item.setData(0, Qt.UserRole, "dashboard")
        
        # 📊 إنشاء شحنة جديدة
        new_shipment_item = QTreeWidgetItem(self.tree, ["📊 إنشاء شحنة جديدة"])
        new_shipment_item.setData(0, Qt.UserRole, "new_shipment")
        
        # 🔍 البحث في الشحنات
        search_shipments_item = QTreeWidgetItem(self.tree, ["🔍 البحث في الشحنات"])
        search_shipments_item.setData(0, Qt.UserRole, "search_shipments")
        
        # 📋 قائمة الشحنات
        shipments_list_item = QTreeWidgetItem(self.tree, ["📋 قائمة الشحنات"])
        shipments_list_item.setData(0, Qt.UserRole, "shipments_list")
        
        # 🏢 إدارة الشحنة
        shipment_management_item = QTreeWidgetItem(self.tree, ["🏢 إدارة الشحنة"])
        shipment_management_item.setData(0, Qt.UserRole, "shipment_management")
        
        # 🌐 متابعة الشحنات التجاري
        commercial_tracking_item = QTreeWidgetItem(self.tree, ["🌐 متابعة الشحنات التجاري"])
        commercial_tracking_item.setData(0, Qt.UserRole, "commercial_tracking")
        
        # 📦 إدارة الأصناف
        items_management_item = QTreeWidgetItem(self.tree, ["📦 إدارة الأصناف"])
        items_management_item.setData(0, Qt.UserRole, "items_management")
        
        # 🏪 إدارة الموردين
        suppliers_management_item = QTreeWidgetItem(self.tree, ["🏪 إدارة الموردين"])
        suppliers_management_item.setData(0, Qt.UserRole, "suppliers_management")
        
        # 💸 إدارة الحوالات
        remittances_management_item = QTreeWidgetItem(self.tree, ["💸 إدارة الحوالات"])
        remittances_management_item.setData(0, Qt.UserRole, "remittances_management")
        
        # 🗄️ قاعدة البيانات
        database_item = QTreeWidgetItem(self.tree, ["🗄️ قاعدة البيانات"])
        database_item.setData(0, Qt.UserRole, "database_system")
        
        # 📊 التقارير المالية
        financial_reports_item = QTreeWidgetItem(self.tree, ["📊 التقارير المالية"])
        financial_reports_item.setData(0, Qt.UserRole, "financial_reports")
        
        # 📈 تقارير الشحن
        shipping_reports_item = QTreeWidgetItem(self.tree, ["📈 تقارير الشحن"])
        shipping_reports_item.setData(0, Qt.UserRole, "shipping_reports")
        
        # ⚙️ إعدادات النظام
        system_settings_item = QTreeWidgetItem(self.tree, ["⚙️ إعدادات النظام"])
        system_settings_item.setData(0, Qt.UserRole, "system_settings")
    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        main_frame = QFrame()
        main_frame.setStyleSheet("""
            QFrame {
                background-color: #f8fafc;
                border: none;
            }
        """)
        
        main_layout = QVBoxLayout(main_frame)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الترحيب
        welcome_label = QLabel("مرحباً بك في نظام CnX ERP")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #1e40af;
                padding: 40px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f0f9ff, stop:1 #e0f2fe);
                border-radius: 12px;
                border: 2px solid #bfdbfe;
            }
        """)
        main_layout.addWidget(welcome_label)
        
        # منطقة المحتوى
        content_area = QScrollArea()
        content_area.setWidgetResizable(True)
        content_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        # محتوى افتراضي
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        info_label = QLabel("""
        <h2 style="color: #1e40af;">نظام إدارة الشحنات والحوالات</h2>
        <p style="font-size: 16px; color: #374151; line-height: 1.6;">
        نظام شامل لإدارة عمليات الشحن والحوالات المالية مع أدوات متقدمة للتتبع والمراقبة.
        </p>
        <ul style="font-size: 14px; color: #6b7280; line-height: 1.8;">
        <li>🚢 إدارة شاملة للشحنات مع التتبع المباشر</li>
        <li>💰 نظام حوالات متطور مع إدارة البنوك</li>
        <li>📦 إدارة الأصناف والمخزون</li>
        <li>🏪 إدارة الموردين والحسابات</li>
        <li>📊 تقارير مالية وإحصائيات شاملة</li>
        <li>🗄️ نظام قاعدة بيانات Oracle متقدم</li>
        </ul>
        """)
        info_label.setWordWrap(True)
        content_layout.addWidget(info_label)
        
        content_layout.addStretch()
        content_area.setWidget(content_widget)
        main_layout.addWidget(content_area)
        
        return main_frame

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f1f5f9;
                border-top: 1px solid #e2e8f0;
                color: #374151;
                font-size: 12px;
            }
        """)

        # معلومات النظام
        system_label = QLabel("نظام CnX ERP v2.0")
        system_label.setStyleSheet("color: #1e40af; font-weight: bold;")
        status_bar.addWidget(system_label)

        # فاصل
        status_bar.addPermanentWidget(QLabel(" | "))

        # المستخدم الحالي
        user_label = QLabel("المستخدم: مدير النظام")
        status_bar.addPermanentWidget(user_label)

        # فاصل
        status_bar.addPermanentWidget(QLabel(" | "))

        # حالة الاتصال
        connection_label = QLabel("متصل بقاعدة البيانات")
        connection_label.setStyleSheet("color: green;")
        status_bar.addPermanentWidget(connection_label)

    def setup_style(self):
        """إعداد أنماط CnX ERP"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8fafc;
            }
            QMenuBar {
                background-color: #1e40af;
                color: white;
                border: none;
                padding: 5px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: rgba(255, 255, 255, 0.2);
            }
            QToolBar {
                background-color: #3b82f6;
                border: none;
                spacing: 3px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: transparent;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QToolBar QToolButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)

    def on_item_clicked(self, item, column):
        """معالج النقر على عنصر في الشجرة"""
        item_text = item.text(0)
        action_data = item.data(0, Qt.UserRole)

        try:
            # لوحة التحكم
            if action_data == "dashboard":
                self.show_dashboard()

            # إدارة الشحنات
            elif action_data == "new_shipment":
                self.open_shipments_window()
            elif action_data == "search_shipments":
                self.open_shipments_window()
            elif action_data == "shipments_list":
                self.open_shipments_window()
            elif action_data == "shipment_management":
                self.open_shipments_window()
            elif action_data == "commercial_tracking":
                self.open_live_tracking_window()

            # إدارة الأصناف
            elif action_data == "items_management":
                self.open_items_window()

            # إدارة الموردين
            elif action_data == "suppliers_management":
                self.open_suppliers_window()

            # إدارة الحوالات
            elif action_data == "remittances_management":
                self.open_remittances_window()

            # قاعدة البيانات
            elif action_data == "database_system":
                self.open_oracle_database_manager()

            # التقارير
            elif action_data in ["financial_reports", "shipping_reports"]:
                QMessageBox.information(self, "التقارير", f"نظام {item_text} قيد التطوير")

            # الإعدادات
            elif action_data == "system_settings":
                self.open_settings_window()

            else:
                QMessageBox.information(self, "تحت التطوير", f"الوظيفة '{item_text}' قيد التطوير")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح {item_text}: {str(e)}")

    def show_dashboard(self):
        """عرض لوحة التحكم"""
        QMessageBox.information(self, "لوحة التحكم", "مرحباً بك في لوحة التحكم الرئيسية")

    # دوال فتح النوافذ
    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام الإعدادات غير متاح")
            return

        try:
            if self.settings_window is None or not self.settings_window.isVisible():
                self.settings_window = SettingsWindow(self)
            self.settings_window.show()
            self.settings_window.raise_()
            self.settings_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات: {str(e)}")

    def open_items_window(self):
        """فتح نافذة إدارة الأصناف"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام إدارة الأصناف غير متاح")
            return

        try:
            if self.items_window is None or not self.items_window.isVisible():
                self.items_window = ItemsWindow(self)
            self.items_window.show()
            self.items_window.raise_()
            self.items_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الأصناف: {str(e)}")

    def open_suppliers_window(self):
        """فتح نافذة إدارة الموردين"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام إدارة الموردين غير متاح")
            return

        try:
            if self.suppliers_window is None or not self.suppliers_window.isVisible():
                self.suppliers_window = SuppliersWindow(self)
            self.suppliers_window.show()
            self.suppliers_window.raise_()
            self.suppliers_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الموردين: {str(e)}")

    def open_shipments_window(self):
        """فتح نافذة إدارة الشحنات"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام إدارة الشحنات غير متاح")
            return

        try:
            if self.shipments_window is None or not self.shipments_window.isVisible():
                self.shipments_window = ShipmentsWindow(self)
            self.shipments_window.show()
            self.shipments_window.raise_()
            self.shipments_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الشحنات: {str(e)}")

    def open_live_tracking_window(self):
        """فتح نافذة التتبع المباشر"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام التتبع المباشر غير متاح")
            return

        try:
            if self.live_tracking_window is None or not self.live_tracking_window.isVisible():
                self.live_tracking_window = LiveTrackingWindow(self)
            self.live_tracking_window.show()
            self.live_tracking_window.raise_()
            self.live_tracking_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التتبع المباشر: {str(e)}")

    def open_remittances_window(self):
        """فتح نافذة إدارة الحوالات"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام إدارة الحوالات غير متاح")
            return

        try:
            if self.remittances_window is None or not self.remittances_window.isVisible():
                self.remittances_window = RemittancesWindow(self)
            self.remittances_window.show()
            self.remittances_window.raise_()
            self.remittances_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الحوالات: {str(e)}")

    def open_oracle_database_manager(self):
        """فتح نظام إدارة قاعدة بيانات Oracle المتقدم"""
        if not SYSTEMS_AVAILABLE:
            QMessageBox.warning(self, "تحذير", "نظام إدارة قاعدة البيانات غير متاح")
            return

        try:
            if self.oracle_database_manager is None or not self.oracle_database_manager.isVisible():
                self.oracle_database_manager = OracleDatabaseManager(self)
            self.oracle_database_manager.show()
            self.oracle_database_manager.raise_()
            self.oracle_database_manager.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نظام إدارة قاعدة البيانات: {str(e)}")


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)

    # إعداد الخط العربي
    if SYSTEMS_AVAILABLE:
        setup_arabic_support(app)

    # إعداد أيقونة التطبيق
    app.setWindowIcon(QIcon("assets/icons/cnx_logo.png"))

    # إنشاء النافذة الرئيسية
    window = CnXMainWindow()
    window.showMaximized()  # فتح في وضع ملء الشاشة

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
