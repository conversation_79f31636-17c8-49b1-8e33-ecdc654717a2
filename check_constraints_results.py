#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص نتائج نسخ القيود
Check constraints copy results
"""

import os
import cx_Oracle
from pathlib import Path


def setup_environment():
    """إعداد البيئة"""
    tns_admin = Path(__file__).parent / "network" / "admin"
    os.environ['TNS_ADMIN'] = str(tns_admin.absolute())


def check_constraints():
    """فحص القيود في المصدر والهدف"""
    setup_environment()
    
    print("🔍 فحص القيود والعلاقات")
    print("=" * 50)
    
    try:
        # الاتصال بالمصدر
        print("📊 فحص القيود في المصدر (ias20241)...")
        source_conn = cx_Oracle.connect("ias20241", "ys123", "yemensoft", encoding="UTF-8")
        source_cursor = source_conn.cursor()
        
        source_cursor.execute("""
            SELECT constraint_type, COUNT(*)
            FROM user_constraints
            WHERE constraint_type IN ('P', 'R', 'U', 'C')
            AND table_name NOT LIKE 'BIN$%'
            GROUP BY constraint_type
            ORDER BY constraint_type
        """)
        source_counts = dict(source_cursor.fetchall())
        
        # الاتصال بالهدف
        print("📊 فحص القيود في الهدف (ship2025)...")
        target_conn = cx_Oracle.connect("ship2025", "ys123", "yemensoft", encoding="UTF-8")
        target_cursor = target_conn.cursor()
        
        target_cursor.execute("""
            SELECT constraint_type, COUNT(*) 
            FROM user_constraints 
            WHERE constraint_type IN ('P', 'R', 'U', 'C')
            GROUP BY constraint_type
            ORDER BY constraint_type
        """)
        target_counts = dict(target_cursor.fetchall())
        
        # عرض النتائج
        type_names = {
            'P': 'مفاتيح أساسية (Primary Keys)',
            'R': 'مفاتيح خارجية (Foreign Keys)', 
            'U': 'قيود فريدة (Unique Constraints)',
            'C': 'قيود فحص (Check Constraints)'
        }
        
        print("\n📋 مقارنة القيود:")
        print("-" * 50)
        
        total_source = 0
        total_target = 0
        
        for cons_type in ['P', 'R', 'U', 'C']:
            source_count = source_counts.get(cons_type, 0)
            target_count = target_counts.get(cons_type, 0)
            percentage = (target_count / source_count * 100) if source_count > 0 else 0
            
            total_source += source_count
            total_target += target_count
            
            print(f"{type_names[cons_type]}:")
            print(f"   المصدر: {source_count}")
            print(f"   الهدف: {target_count}")
            print(f"   النسبة: {percentage:.1f}%")
            print()
        
        print("-" * 50)
        print(f"إجمالي القيود:")
        print(f"   المصدر: {total_source}")
        print(f"   الهدف: {total_target}")
        if total_source > 0:
            overall_percentage = (total_target / total_source) * 100
            print(f"   النسبة الإجمالية: {overall_percentage:.1f}%")
        
        # فحص عينة من القيود
        print("\n📋 عينة من القيود في الهدف:")
        target_cursor.execute("""
            SELECT constraint_name, constraint_type, table_name, status
            FROM user_constraints
            WHERE constraint_type IN ('P', 'R', 'U', 'C')
            ORDER BY constraint_type, table_name
        """)
        
        constraints = target_cursor.fetchall()
        
        if constraints:
            print(f"   تم العثور على {len(constraints)} قيد")
            
            # عرض أول 15 قيد
            for i, (cons_name, cons_type, table_name, status) in enumerate(constraints[:15]):
                type_symbol = {'P': '🔑', 'R': '🔗', 'U': '🔒', 'C': '✔️'}
                symbol = type_symbol.get(cons_type, '❓')
                print(f"   {i+1:2d}. {symbol} {cons_name} على {table_name} ({status})")
            
            if len(constraints) > 15:
                print(f"   ... و {len(constraints) - 15} قيد آخر")
        else:
            print("   ❌ لم يتم العثور على قيود")
        
        # فحص العلاقات الخارجية
        print("\n🔗 فحص العلاقات الخارجية:")
        target_cursor.execute("""
            SELECT c.constraint_name, c.table_name, 
                   r.table_name as ref_table, c.delete_rule
            FROM user_constraints c, user_constraints r
            WHERE c.constraint_type = 'R'
            AND c.r_constraint_name = r.constraint_name
            ORDER BY c.table_name
        """)
        
        foreign_keys = target_cursor.fetchall()
        
        if foreign_keys:
            print(f"   تم العثور على {len(foreign_keys)} علاقة خارجية")
            
            # عرض أول 10 علاقات
            for i, (fk_name, table_name, ref_table, delete_rule) in enumerate(foreign_keys[:10]):
                rule_text = f" ({delete_rule})" if delete_rule and delete_rule != 'NO ACTION' else ""
                print(f"   {i+1:2d}. {table_name} → {ref_table}{rule_text}")
            
            if len(foreign_keys) > 10:
                print(f"   ... و {len(foreign_keys) - 10} علاقة أخرى")
        else:
            print("   ❌ لم يتم العثور على علاقات خارجية")
        
        # إغلاق الاتصالات
        source_cursor.close()
        source_conn.close()
        target_cursor.close()
        target_conn.close()
        
        print("\n" + "=" * 50)
        
        if total_target > 0:
            print("✅ تم نسخ القيود بنجاح!")
        else:
            print("❌ لم يتم نسخ أي قيود")
        
        return total_target > 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص القيود: {e}")
        return False


def main():
    """الدالة الرئيسية"""
    success = check_constraints()
    
    if success:
        print("🎉 فحص القيود مكتمل!")
    else:
        print("💥 فشل في فحص القيود!")
    
    return success


if __name__ == "__main__":
    main()
